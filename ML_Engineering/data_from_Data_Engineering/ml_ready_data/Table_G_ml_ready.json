{"source_file": "data_renamed\\Table_G.docx", "processed_timestamp": "2025-06-12T14:25:15.422781", "ml_target_format": "data_json_compatible", "segments": [{"id": "Table_G_seg_001", "text": "Début de la retranscription : Travail collectif, matinée 1. <PERSON><PERSON><PERSON>, je vous laisse papoter, du coup, merci. Faut pas oublier d'annoncer sur les dictaphones. Ah oui, j'ai pas dit.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la retranscription", "Travail collectif", ", merci", "les dictaphones", "du coup", "travail collectif", ", du coup", ", merci", ", du coup", "travail collectif", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 0, "end": 4}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_002", "text": "Juste, du coup, on enregistre. <PERSON> est passée, elle a dit un petit truc dans le dictaphone. Ils sont pas lancés, là ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON>e, du coup", "un petit truc", "le dictaphone", "du coup", ", du coup", ", elle", ", du coup", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 1, "end": 4}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_003", "text": "Si, si, ils sont lancés et Cynthia a parlé dedans discrètement, donc elle a sûrement dit ce qu'on était en train de faire. <PERSON><PERSON>, mettre en commun, déj<PERSON>, c'est ça ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["C'est ça", ", c'est ça", "C'est ça", "C'est ça", ", c'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 2, "end": 4}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_004", "text": "Sacré exercice. Qui veut commencer ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 6, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Sacré exercice"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 3, "end": 5}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_005", "text": "Le CJD, c'est surtout pour ces dirigeants d'essayer de nouvelles expérimentations, essayer heu... de penser sa place de dirigeant, voilà, dans un monde qui est un peu plus complexe et... donc c'est un mouvement assez engagé et en fait, on a heu... on a une salariée au CJD qui avait fait son doctorat dans votre labo. Je sais pas si tu fais partie du labo aussi ? Non.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 68, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le CJD", "Le CJD", ", c'est surtout pour ces dirigeants", "de nouvelles expérimentations", "sa place", "un monde", "une salariée", "son doctorat", "votre labo", "un monde", "sa place", "et en fait", "un monde", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 4, "end": 7}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_006", "text": "Pas loin, mais... OK, donc c'est <PERSON><PERSON><PERSON><PERSON><PERSON> qui nous a introduits ici. Et l'autre casquette, c'est que moi, je suis psychologue du travail, donc j'accompagne toute l'année des organisations qui rencontrent des problèmes de santé au travail, qui du coup ont créé un système qui ne produit plus de la santé et de l'équilibre.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["l'autre casquette", "toute l'année", "des problèmes", "un système", "plus de la santé", "de l'équilibre", "un système", "du coup", "que moi", "du travail"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 5, "end": 7}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_007", "text": "Et mon boulot, c'est heu... d'essayer de leur permettre de retrouver de l'équilibre, donc v<PERSON>, c'est... j'allais dire, c'est un peu deux points de vue. Et mon entreprise à moi, puisque j'ai créé un cabinet de psycho où on est trois, mon entreprise à moi est une Scop, une coopérative. D'accord. OK. Dans le milieu de l'économie sociale et solidaire aussi. Oui, aussi.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["de l'équilibre", "mon boulot", "un cabinet", "mon entreprise", "une coopérative", "le milieu", "l'économie sociale et solidaire aussi", "une Scop", "c'est un peu", "économie sociale et solidaire", "une Scop", "l'économie sociale et solidaire", "l'économie sociale et solidaire", "mon boulot", "l'économie sociale et solidaire", "l'économie sociale et solidaire", "... j'"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 6, "end": 12}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_008", "text": "Donc ce que j'ai mis : pour moi, une entreprise aujourd'hui en 2023, elle doit rester performante, elle doit quand même continuer à gagner de l'argent, tant qu'on n'a pas trouvé d'autres systèmes, parce que heu... voilà. En ce moment, on accompagne une entreprise qui a un plan social à Saint-Étienne, qui vire tout le monde, j'allais dire, c'est quand même...", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": [", une entreprise", "d'autres systèmes", "ce moment", "une entreprise", "un plan social", "tout le monde", "pour moi", "une entreprise", "une entreprise", "tout le monde", "tout le monde", "le monde", "Une entreprise", ", elle", "le monde", "une entreprise", "tout le monde", "Une entreprise", "tout le monde", "tout le monde", "<PERSON>ut le monde", "<PERSON>ut le monde", "une entreprise", "une entreprise", "une entreprise", "le monde", "tout le monde", "une entreprise", "tout le monde", "une entreprise", "<PERSON>ut le monde", "tout le monde", "pour moi", "tout le monde", "le monde", "le monde", "le monde", "le monde", "que heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 7, "end": 10}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_009", "text": "si économiquement ça tient pas, bah ça va pas se créer des... donc c'est comment rester performante malgré tous les autres fils qu'on va pouvoir tirer. Évidemment, pour moi, coopérative.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["tous les autres fils", "pour moi", "Bah ça", "pour moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 8, "end": 10}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_010", "text": "<PERSON>, très bien. Sur ce premier point.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 7, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce premier point"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 9, "end": 11}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_011", "text": "Donc on essaie d'attraper les... les gens qui seraient susceptibles de répondre à notre questionnaire de comment ils s'engagent, à quel niveau, pourquoi, voilà. Donc c'est tout neuf, donc c'est... on est en construction. On a construit le questionnaire.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les gens", "notre questionnaire", "quel niveau", "le questionnaire", "les gens", "les gens", "les gens", "Les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "Les gens", "les gens", "les gens", "Les gens", "les gens", "les gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 10, "end": 13}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_012", "text": "Et puis heu... moi, je me suis spécialisée dans la sociologie de l'environnement, c'est-à-dire comprendre les interactions entre les gens, mais aussi entre les gens et l'environnement, et comment est-ce qu'on peut casser le « nature contre culture » et comment est-ce qu'on peut être des passeurs de nature et de culture. Et puis je suis en train de monter une Scop pour favoriser la communication interne et externe dans les associations, parce qu'y'a une grosse problématique dans le bénévolat, comment on engage les gens et comment on fait du lien entre les associations aussi pour que ça puisse fédérer et qu'on puisse avancer aussi.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 105, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["les gens", "Et puis heu... moi", "la sociologie", "les interactions", "les gens", "aussi entre les gens", "le « nature", "une Scop", "la communication interne et externe", "les associations", "une grosse problématique", "le bénévolat", "les gens", "les associations", "les gens", "Les gens", "la socio", "les gens", "les gens", "les gens", "les gens", "les gens", "une Scop", "les gens", "Les gens", "que ça", "que ça", "les gens", "les gens", "... moi", "Les gens", "les gens", "les gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 11, "end": 13}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_013", "text": "Et puis qui répond à un besoin. <PERSON>ur moi, c'est... voilà, apr<PERSON>, j'ai pas vraiment une qualité, mais pour moi, ça fait partie des trucs essentiels, pour que ce soit une bonne boîte.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une bonne boîte", "un besoin", "pour moi", "trucs essentiels", "un besoin", "un besoin", "une bonne boîte", "des trucs", "des trucs", "des trucs", "des trucs", "un besoin", "des trucs", "des trucs", "des trucs", "des trucs", "pour moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 12, "end": 14}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_014", "text": "<PERSON><PERSON>, tu parles de G<PERSON>i, tu peux penser à <PERSON>, ce sera peut-être pas répondre à un besoin, alors, ce serait peut-être... enfin oui, il faudrait redéfinir le besoin ou... ou repenser le... c'est difficile. Bah c'est la différence entre l'envie et le besoin.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un besoin", "un besoin", "un besoin", "ce serait", "le besoin", "le besoin", "la différence", "le besoin", "un besoin", "le besoin", ", tu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 13, "end": 15}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_015", "text": "<PERSON>h oui, les gens, ils ont plus de sous. Les gens, ils ont plus... ils peuvent plus supporter le coût supplémentaire.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les gens", "les gens", "les gens", "les gens", "Les gens", "le coût supplémentaire", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "Les gens", "les gens", "les gens", "Les gens", "les gens", "le coût", "les gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 14, "end": 16}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_016", "text": "Et puis surtout... oui, et puis y'a autre chose, c'est qu'en fait, le... le bio n'a pas apporté toutes ses promesses.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["autre chose", "le bio", "toutes ses promesses", "le bio", "autre chose", "c'est qu'", "autre chose", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 15, "end": 17}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_017", "text": "Le consommateur ne se retrouve pas dans les... dans ce qui était promis dans le bio, ce qui fait qu'aujourd'hui, bah oui, ça coince. Bon, très bien.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le bio", "Le consommateur", "le bio", "le consommateur", "le consommateur", "le consommateur"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 16, "end": 18}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_018", "text": "Du coup, je travaille donc en cabinet d'expertise comptable, donc avec les entreprises. Heu... alors moi, qu'est-ce que j'ai mis ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les entreprises", "expertise comptable", "donc avec les entreprises", "Heu... alors moi", "<PERSON><PERSON> moi", "<PERSON><PERSON> moi", "du coup", "les entreprises", "les entreprises", "alors moi", "<PERSON><PERSON> moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 17, "end": 19}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_019", "text": "Et c'est pas communication dans le marketing, hein, c'est communication, échanges entre... voilà, j'ai marqué « retours d'expérience collaborateurs, clients, fournisseurs, partenaires. D'être soucieuse des personnes, de l'environnement, équilibre dans les relations, implication dans des projets locaux, humanitaires », tout ça. OK. Et du coup, en biologie, on fait la différence entre la croissance et le développement.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "croissance_decroissance": {"side_a": 2, "side_b": 0, "tension_strength": 2, "total_indicators": 2}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "croissance_decroissance", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["le marketing", ", fournis<PERSON>urs", ", partenaires", ", équilibre", "les relations", "des projets locaux", "tout ça", "Et du coup", "la différence", "la croissance", "le développement", "Et du coup", "la croissance", "la croissance", ", équilibre", "du coup", "Et du coup", "tout ça", "tout ça", "les relations", "tout ça", "Et du coup"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 18, "end": 22}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_020", "text": "Un arbre, il commence par croître en hauteur et ensuite, il se développe. Et du coup, la croissance, on est d'accord, aujourd'hui, que elle peut pas être infinie, elle peut pas être éternelle. Tout à fait.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 36, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["Et du coup", "la croissance", "Un arbre", "Et du coup", "la croissance", "la croissance", "du coup", "Et du coup", ", elle", "Et du coup"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 19, "end": 22}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_021", "text": "On est dans un monde fini, donc... <PERSON><PERSON>, tout à fait. <PERSON><PERSON> <PERSON><PERSON>, de fait, aujourd'hui en 2023, c'est ce qu'on demande à une entreprise. Oui. <PERSON><PERSON>, c'est peut-être pas le bon mot , « croissance ».", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["un monde", "une entreprise", "une entreprise", "un monde", "une entreprise", "<PERSON><PERSON>, c'est peut-être pas le bon mot", ", « croissance", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "un monde"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 20, "end": 26}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_022", "text": "C'est peut-être « développement », comme tu dis, c'est peut-être plus ça qui... mais elles doivent forcément, je pense, pour que ça marche, trouver un... une façon de... y'a un équilibre à trouver. Y'a un équilibre à trouver entre justement cette croissance qui peut toujours être...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 2, "side_b": 0, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["une façon", "un équilibre", "un équilibre", "cette croissance", "que ça", "que ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 21, "end": 23}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_023", "text": "Donc c'est vrai que... ça, c'est un peu... un peu inquiétant. Oui, bah c'est super. Ça va être bien. C'est génial.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["c'est un peu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 22, "end": 26}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_024", "text": "Et d'ailleurs, j'ai... j'ai compris cette semaine que pour qu'on puisse se baigner dans la Seine, ils ont juste créé des bassins qui vont réceptionner l'eau usée, pour que sur un tronçon y'ait moins de pollution pendant un temps donné. Donc j'allais dire, on va pouvoir heu... les taux chimiques, je sais pas quoi, ils vont diminuer, mais en fait, les bassins d'eau polluée, apr<PERSON>, ils seront ouverts. <PERSON><PERSON> apr<PERSON>, ils vont les relâcher. En fait, va pas du tout... Ça va, vous avez réussi à faire le tour ? Tout à fait.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 93, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la Seine", "cette semaine", "la Seine", "des bassins", "l'eau usée", "que sur un tronçon y'", "un temps", "les taux chimiques", "en fait, les bassins", "eau poll<PERSON>e", "le tour", "un temps", "un temps", "en fait", "... j'", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 23, "end": 29}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_025", "text": "De la première partie, par contre, pas le... Ah bah si, l'idée, c'est tout, enfin c'est... sur 2023, oui. Pas sur 2050. <PERSON><PERSON>, de ce que vous avez écrit, quoi. Ah oui, al<PERSON> ap<PERSON>, ça se croise, quoi. <PERSON><PERSON> moi, j'ai eu du mal avec « une boîte légitime », le terme « légitimité » , je savais pas comment le...", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 62, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la première partie", "<PERSON><PERSON> moi", "du mal", "une boîte légitime", "le terme", "<PERSON><PERSON> moi", "Une boîte légitime", "alors moi", "<PERSON><PERSON> moi", "la première partie"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 24, "end": 30}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_026", "text": "Le côté légitimité, bah c'est pareil, c'est quoi, être légitime ? C'est ça. <PERSON><PERSON> moi, je me suis dit : légitime, pour moi, c'est quelque... quelque chose qui est juste et qui est équitable. Mais tu vois, je... <PERSON><PERSON>, j'ai mis : soucieuse des personnes, environnement, équilibre des relations. Une boîte légitime, c'est pas du tout un adjectif... Mais du coup, moi, j'ai pensé au côté heu... efficace, efficiente, parce que bon bah si t'es une entreprise, il faut que tu sois plus... plus heu... voilà, efficace, efficiente, que quelqu'un qui fait ça chez lui. Sinon, ça sert à rien d'avoir des pros.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 103, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.941747572815534, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.7766990291262136}, "noun_phrases": ["une entreprise", ", moi", "pour moi", "une entreprise", ", équilibre", "une entreprise", "<PERSON><PERSON> moi", "une boîte légitime", "Le côté légitimité", "C'est ça", "<PERSON><PERSON> moi", "quel<PERSON> chose", ", équilibre", "Une boîte légitime", "du coup", ", moi", "c<PERSON><PERSON> heu", "des pros", "Le <PERSON>", "le <PERSON><PERSON>", "quel<PERSON> chose", ", moi", "quel<PERSON> chose", "le <PERSON><PERSON>", "<PERSON><PERSON> du <PERSON>", "Une entreprise", "une entreprise", ", moi", "C'est ça", ", moi", "Une entreprise", "quel<PERSON> chose", "quel<PERSON> chose", "mais du coup", "quel<PERSON> chose", "<PERSON><PERSON> du <PERSON>", "alors moi", "une entreprise", "C'est ça", "une entreprise", "une entreprise", "quel<PERSON> chose", "quel<PERSON> chose", "une entreprise", "une entreprise", ", moi", "C'est ça", "... moi", "<PERSON><PERSON> moi", "pour moi", "C'est ça", "C'est ça", "C'est ça", "le <PERSON><PERSON>", "C'est ça", "<PERSON><PERSON> du <PERSON>", "mais du coup", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 25, "end": 31}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_027", "text": "Le côté réflexion dans son écosystème, donc heu... que ce soit pas complètement hors-sol et du coup, pour avoir sa place, que ce soit pensé. Le côté travail en réseau, du coup, ça, c'est la même chose, en fait.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["sa place", "Et du coup", "Et du coup", "du coup", "Le <PERSON>", "son <PERSON><PERSON><PERSON><PERSON><PERSON>", "sa place", "Le côté travail", "le <PERSON><PERSON>", "le <PERSON><PERSON>", ", du coup", "Et du coup", ", en fait", ", du coup", ", du coup", "le <PERSON><PERSON>", "en fait", "Et du coup", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 26, "end": 28}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_028", "text": "Voilà, bon ap<PERSON><PERSON>, ça fait 20 ans que je baigne dans le développement durable et que la notion Il va falloir qu'y'ait d'autres solutions, par contre, avant de les gicler. Eh bah oui, c'est ça. Répondre aux besoins, heu... voilà, des produits pensés avec et pour ses clients, quoi, c'est... il faut vraiment être heu... et ça, la notion de sociologie, pour moi, elle est importante.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["croissance_decroissance", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["pour moi", "<PERSON><PERSON>, c'est ça", "le développement", "C'est ça", "20 ans", "le développement durable", "la notion", "qu'y'ait d'autres solutions", "de les gicler", "<PERSON>h bah oui, c'est ça", "des produits", "et pour ses clients", "la notion", "le développement durable", "20 ans", "la notion", ", c'est ça", ", elle", "C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "Et ça", ", c'est ça", "et ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "pour moi", "C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "20 ans", "<PERSON><PERSON>, c'est ça", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 27, "end": 30}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_029", "text": "Aujourd'hui, d'ailleurs, on voit que dans le design et puis heu... dans... je donne des cours à [Info-Com], on me demande de faire de la socio avec des gens qui veulent faire du marketing, pour que les gens s'ancrent dans le : on fait pas des trucs pour faire des trucs.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 51, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les gens", "les gens", "les gens", "des gens", "les gens", "Les gens", "des trucs", "le design", "des cours", "la socio", "des gens", "les gens", "des trucs", "des trucs", "des trucs", "des trucs", "les gens", "les gens", "des gens", "des gens", "les gens", "les gens", "des gens", "des gens", "des trucs", "des gens", "les gens", "Les gens", "des trucs", "des gens", "les gens", "des trucs", "les gens", "Les gens", "des gens", "les gens", "les gens", "des gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 28, "end": 30}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_030", "text": "Et puis la prise en compte de l'impact social de manière générale, avec la construction d'indicateurs, pour heu... comprendre, est-ce qu'on amène quelque chose de... de positif, quoi. <PERSON><PERSON> bon, apr<PERSON>, moi, je... en 2002, on pensait déjà le développement durable comme quelque chose d'ancré dans les pratiques.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 48, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 1.0, "performance_indicators": 2, "legitimacy_indicators": 1}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": [", moi", "le développement", "quel<PERSON> chose", ", moi", "le développement durable", "l'impact social", "manière générale", "la construction", "quel<PERSON> chose", ", moi", "le développement durable", "quel<PERSON> chose", "les pratiques", ", moi", ", moi", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "impact social", ", moi", "Après, moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 29, "end": 31}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_031", "text": "Ils sont à la bourre, à la fac. Ils sont à la bourre.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la fac", "la fac"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 30, "end": 32}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_032", "text": "Ils ont... pour les jardins, enfin pour les parties heu... paysagers, ils ont pensé à la gestion des espaces, ils ont pensé les endroits où il va pas y avoir la tondeuse toutes les semaines, mais ils ont pas pensé un petit coin permaculture, alors que tous les toits sont plats. Je vous demande un peu d'attention, si ça vous va. Là, ce que je vous propose maintenant, c'est heu... est-ce que vous avez... alors c'est peut-être pas évident, mais toujours en tête ce qui s'est dit, ce qu'Alexis nous a raconté sur le scénario 2050, en petit aide ou rappelle", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 101, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les toits", "les toits", "les jardins", "enfin pour les parties heu", "la gestion", "les endroits", "la tondeuse", "toutes les semaines", "un petit coin permaculture", "tous les toits", "toujours en tête", "le scénario 2050", "petit aide", "les toits", "les endroits"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 31, "end": 34}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_033", "text": "-m<PERSON><PERSON><PERSON>, on a des effets sur les organisations, vous avez des feuilles comme ça, c'est la slide qu'il vous a... donc là, ce que je vais vous demander, c'est... ce qu'on vous demande, c'est, donc sur cette... sur ce document-là vierge, heu... de répondre à la partie 5, donc la première case à gauche.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des effets", "les organisations", "des feuilles", "ce document", "la partie", "donc la première case", "les organisations", "les organisations", "les organisations", "les organisations", "comme ça", "Comme ça", "comme ça", "la partie", "comme ça", "comme ça", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 32, "end": 34}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_034", "text": "Quelle sera une boîte légitime en 2050, quand on observe les effets sur les organisations que ça produit ? Donc en considérant que y'a notamment ces effets-là, mais aussi tous ceux que vous imaginez, hein, on ferme pas là-dessus, finalement, une boîte légitime en 2050, qu'est-ce qu'elle est ? Peut-être qu'y'a des choses que vous avez déjà écrites. Clap, 2, travail collectif. Il faudra aussi imaginer d'autres choses. La légi... j'espère que c'est clair sur la notion de légitimité pour tout le monde, sinon je peux aussi donner un peu d'exemples.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 91, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["Travail collectif", "tout le monde", "une boîte légitime", "Une boîte légitime", "la notion", "la notion", "les organisations", "sera une boîte légitime", "les effets", "les organisations", "y'a notamment", "mais aussi tous ceux", "des choses", "travail collectif", "d'autres choses", "La légi", "la notion", "tout le monde", "tout le monde", "le monde", "les organisations", "les organisations", "le monde", "les organisations", "des choses", "tout le monde", "tout le monde", "tout le monde", "des choses", "des choses", "<PERSON>ut le monde", "<PERSON>ut le monde", "que ça", "que ça", "le monde", "tout le monde", "travail collectif", "tout le monde", "<PERSON>ut le monde", "tout le monde", "des choses", "des choses", "des choses", "des choses", "tout le monde", "le monde", "... j'", "des choses", "le monde", "le monde", "le monde"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 33, "end": 39}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_035", "text": "Mais en tout cas, c'est comment on la considère légitime, donc utile, intéressante, et visible, en fait. Peut-être, on peut parler de « visible » par l'ensemble des parties prenantes autour de ça, si déjà on se dit ça, heu... c'est pas la définition très exacte de la légitimité, mais c'est ce qui vous parle.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["La légi", "tout cas", "parties prenantes autour de ça", "la légitimité", "tout cas", "tout cas", ", en fait", "tout cas", "tout cas", "la légitimité", "parties prenantes", "la légitimité", "parties prenantes", "parties prenantes", "tout cas", "la légitimité", "la légitimité", "parties prenantes", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "en fait", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 34, "end": 36}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_036", "text": "Voilà, donc on a 20 minutes, enfin on a une demi-heure pour faire ça. Juste qu'y'en ait un qui écrive sur cette feuille.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["20 minutes", "une demi-heure", "cette feuille", "une demi-heure"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 35, "end": 37}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_037", "text": "Donc qui écrit bien ? Parce que moi, c'est pas terrible.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Parce que moi", "Parce que moi", "Parce que moi", "que moi", "Parce que moi", "parce que moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 36, "end": 38}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_038", "text": "<PERSON><PERSON> là, vu ce qu'il a présenté, heu... donc là, ils vont travailler dans quelle boîte, nos enfants ? <PERSON><PERSON>, c'est pas rassurant de voir ça. Et en même temps, c'est pas parce que c'est pas rassurant qu'il faut pas le projeter, c'est comme mourir, ça va arriver à un moment donné. Si on l'anticipe pas, ce sera encore plus la merde.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 62, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un moment", "quelle boîte", ", nos enfants", "même temps", "un moment", "nos enfants", "un moment", "un moment", "la merde", "même temps", "un moment", "même temps", "un moment", "même temps"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 37, "end": 41}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_039", "text": "Bah dans le côté réflexion sur l'écosystème, en fait, si t'as pas de réflexion sur l'écosystème, tu... tu penses pas le ralentissement de l'activité, donc ton entreprise, elle... elle a pas su s'adapter. C'est nécessaire, en fait, heu... se dire que bah en fait, il faut... il faut penser heu... penser plus global qu'aujourd'hui où on se dit « ah on va faire des nouveaux téléphones portables ».", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 68, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["Le <PERSON>", "le <PERSON><PERSON>", "que bah en fait", "le <PERSON><PERSON>", "de réflexion", "le ralentissement", "ton entreprise", "des nouveaux téléphones portables", ", elle", ", en fait", "de réflexion", ", tu", "le <PERSON><PERSON>", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 38, "end": 40}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_040", "text": "Ça va être différent. On va tous être chez nous.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 39, "end": 41}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_041", "text": "De toute façon, il va y avoir du mouvement, hein, des flux migratoires partout, dans tous les sens, donc... même l'écologie, la nature, elle va changer, elle va évoluer, y'a des espèces qui... <PERSON>h oui, bien sûr.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la nature", "toute façon", "toute façon", "partout, dans tous les sens", "même l'écologie", "la nature", "des espèces", "toute façon", ", elle", "toute façon", "la nature", "toute façon", "toute façon", "toute façon", "la nature"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 40, "end": 42}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_042", "text": "Même chez nous en France, hein. Est -ce que vous avez vu le film « <PERSON><PERSON><PERSON> » ? <PERSON> ? Non, je l'ai pas vu. Je vous le conseille vivement, parce que en fait, heu... par exemple, il montre une entreprise heu... du Nord qui heu... a déjà complètement changé ses... sa chaîne de valeurs, qui du coup travaille avec... ils font des enveloppes et ils en font beaucoup.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 71, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "du coup", "<PERSON><PERSON>me chez nous", "le film", "<PERSON> ?", "une entreprise heu", "ses... sa chaîne", "des enveloppes", "Une entreprise", "une entreprise", "Une entreprise", "« <PERSON><PERSON><PERSON>", "une entreprise", "parce que en fait", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 41, "end": 47}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_043", "text": "Mais du coup, y'a plus de... ils ont que des encres naturelles, donc ils peuvent nettoyer leurs machines avec de l'eau, qui recyclent dans des bambous, donc ils réutilisent l'eau, enfin l'eau, elle est recyclée en interne, pour pouvoir nettoyer leurs cuves, etc., ils ont des panneaux solaires sur les toits et c'est végétalisé. Du coup, ils ont réduit, je sais plus que... quel pourcentage de... et en fait, du coup, en fait, ils ont... ils ont recrée heu... un écosystème à l'intérieur de leur propre entreprise, pour que heu... bah les chutes de papier des découpages des enveloppes, elle sert aux... aux chaudières pour le... pour le chauffage. Oui, disons, en fait, moi ce que je vois, c'est que on va retrouver un petit peu comme avant dans les... dans les campagnes, dans les fermes, où on recyclait tout. C'est ça.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 142, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.5633802816901409}, "noun_phrases": [", moi", "C'est ça", "du coup", ", moi", ", moi", "les toits", "les toits", "des enveloppes", "<PERSON><PERSON> du <PERSON>", "leurs machines", "de l'eau", "des bambous", "leurs cuves", "des panneaux solaires", "les toits", "quel pourcentage", "et en fait", ", du coup", "leur propre entreprise", "le chauffage", "les campagnes", "panneaux solaires", ", elle", ", en fait", ", moi", "C'est ça", ", moi", "mais du coup", ", du coup", "<PERSON><PERSON> du <PERSON>", "C'est ça", ", du coup", ", moi", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "en fait", "C'est ça", "<PERSON><PERSON> du <PERSON>", "mais du coup", "en fait", "C'est ça", "que heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 42, "end": 47}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_044", "text": "Y'avait rien qui était perdu, en fait, hein, donc rien n'était gâché. Alors qu'aujourd'hui, on a tendance à jeter, donc les entreprises, elles vont raisonner de la même... de la même manière, en fait. Alors ils appellent ça de « l'écolonomie », parce que du coup, c'est une façon de lier l'économie, matérielle et financière, à l'écologie, pour réduire son impact. Est -ce que ça peut pas être heu... Oui, c'est sûr qu'elle devra...", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 74, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 1.0}, "noun_phrases": ["les entreprises", "une façon", "du coup", "parce que du coup", "les entreprises", "la même manière", "« l'écolonomie", "parce que du coup", "son impact", ", elle", ", en fait", "les entreprises", "que ça", "que du coup", "que ça", ", parce que du coup", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 43, "end": 49}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_045", "text": "l'entreprise de demain, elle devra produire moins de déchets et avoir des circuits courts. Mai<PERSON> moi, je me dis vraiment, en termes d'organisation du travail, qu'est-ce que ça va... qu'est-ce qu'on va... qu'est-ce qu'on va pouvoir inventer ? Pas... j'allais dire heu...", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des circuits courts", "<PERSON><PERSON> moi", ", elle", "que ça", "de dé", "que ça", "du travail", "... j'"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 44, "end": 48}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_046", "text": "oui, dans les consommables de l'entreprise, on va trouver sans doute... bah de toute façon, y'aura plus, hein, il va bien falloir qu'on trouve autre chose. <PERSON><PERSON>, je me dis, vraiment, dans le... dans les organisations, parce qu'on pourra plus bosser 50 heures par semaine, c'est pas possible.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 48, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["autre chose", "toute façon", "les organisations", "les organisations", "toute façon", "les consommables", "autre chose", "les organisations", "50 heures", "50 heures", "les organisations", "toute façon", "toute façon", "les organisations", "autre chose", "toute façon", "toute façon", "toute façon"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 45, "end": 47}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_047", "text": "<PERSON><PERSON> non. Non, mais de toute façon, c'est... ça sert à rien de faire ça, on est 8 milliards, on peut pas faire ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["toute façon", "toute façon", "mais de toute façon", "toute façon", "toute façon", "toute façon", "toute façon", "toute façon"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 46, "end": 48}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_048", "text": "<PERSON><PERSON>, je me dis, mais du coup, demain, quel... aujourd'hui, par exemple, y'a des gens qu'on rencontre, qui sont dans la merde et qui disent « je... si y'avait pas un impact financier, je travaillerai moins, je conso... enfin je... », il va bien falloir qu'on trouve... que les organisations du travail trouvent un moyen de... d'équilibrer cette contrainte économique. Et puis peut-être aussi ne pas essayer de vendre tout à tout prix pour heu... pour... juste pour vendre.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 79, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["des gens", "du coup", "des gens", "les organisations", "les organisations", "<PERSON><PERSON> du <PERSON>", "les organisations", "des gens", "des gens", "un impact financier", "les organisations", "un moyen", "cette contrainte économique", "tout à tout prix", "les organisations", "des gens", "des gens", "des gens", "mais du coup", "un impact", "la merde", "<PERSON><PERSON> du <PERSON>", "des gens", "un impact", "un impact", "du travail", "des gens", "<PERSON><PERSON> du <PERSON>", "mais du coup", "des gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 47, "end": 49}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_049", "text": "Enfin pour moi, le... un des gros problèmes aujourd'hui, c'est qu'on a des besoins qui sont toujours croissants, et que on... on nous matraque avec heu... on a besoin de la dernière montre connectée. On vivait très bien sans avant.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pour moi", "gros problèmes", "des besoins", "gros problèmes", "enfin pour moi", "c'est qu'", "pour moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 48, "end": 50}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_050", "text": "<PERSON><PERSON>, ça, c'est ça. Où en fait, si on continue sur heu... toujours de la croissance, on va juste tous mourir. Non, mais ça, c'est les constats.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["la croissance", "la croissance", "la croissance", "C'est ça", "mais ça", "toujours de la croissance", "Non, mais ça, c'est les constats", "mais ça", ", c'est ça", "C'est ça", "C'est ça", ", c'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "en fait", "C'est ça", "mais ça", "en fait", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 49, "end": 52}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_051", "text": "Est-ce qu'ils se déplaceront différemment ? Alors peut-être que leur entreprise triera les déchets, effectivement, mais de me dire, ils vont être dans quelle réalité ?", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les déchets", "quelle ré<PERSON>té", "leur entreprise"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 50, "end": 52}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_052", "text": "<PERSON>h ça, du coup, dans les critères... dans les critères de performance, pour pouvoir être cohérent avec la... c'est effectivement le... les déchets, la gestion des déchets et la revalorisation. Voilà, le recyclage, la revalorisation. <PERSON><PERSON>, dans les critères de performance, tu peux être sûr que... Ce que tu disais, l'écolonomie. L'économie écologique. Oui, voilà.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", "la gestion", ", du coup", "les déchets", "les critères", "la... c'est effectivement le... les déchets", ", la gestion", "la revalorisation", "le recyclage", ", la revalorisation", "les critères", "L'économie écologique", "les critères", "Bah ça", "les critères", ", du coup", "les critères", "les critères", "les critères", ", du coup", "les critères", "les critères", "les critères", "les critères", ", tu", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 51, "end": 57}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_053", "text": "Donc une entreprise légitime, c'est une entreprise qui produira le moins de déchets possible ou qui sera en circuit un peu... Bah économie circulaire, je pense que ça, ça va être une base, l'économie circulaire.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "Une entreprise", "une entreprise légitime", "déchets possible", "économie circulaire", "une entreprise légitime", "une entreprise", "Une entreprise", "une entreprise", "que ça", "de dé", "une entreprise", "une entreprise", "que ça", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 52, "end": 54}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_054", "text": "On n'a pas le choix, en fait, enfin... si, bien sûr, on a le choix. On a le choix de transformer la planète en la planète Mars sans avoir à se déplacer.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la planète", "le choix", "le choix", "le choix", "la planète", "la planète Mars", "la planète", ", en fait", "la planète", "la planète", "le choix", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 53, "end": 55}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_055", "text": "Qui valorise heu... qui recycle, qui revalorise, oui. <PERSON><PERSON>, qui valorise et qui recycle, du coup, et que c'est l'économie circulaire, hein. On va retrouver une localité, ce qu'on disait aussi, on va retrouver de la localité. O<PERSON>, qui achète ses produits...", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["du coup", ", du coup", "économie circulaire", "une localité", "la localité", "ses produits", ", du coup", "ses produits", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 54, "end": 58}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_056", "text": "On mangera peut-être des produits de saison et puis voilà, hein. On va revenir... <PERSON><PERSON>, c'est ça, c'est plus heu... ré-ancrer dans... dans une réalité locale. Oui. Dans la pyramide <PERSON>, les besoins primaires, peut-être se concentrer peut-être plus là-dessus et... Tout ce qui est circuits courts, quoi, pas la peine d'aller acheter et de produire...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "C'est ça", "des produits", ", c'est ça", "une réalité locale", "la pyramide", "Tout ce qui", "quoi, pas la peine", "les besoins", "tout ce", "C'est ça", "tout ce", "C'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 55, "end": 60}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_057", "text": "Et puis concentrer sur les besoins plutôt que sur les envies. Oui, peut-être l'auto-production, aussi ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les besoins", "les envies", "Oui, peut-être l'auto-production", "Peut-être l'auto-production"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 56, "end": 58}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_058", "text": "Peut-être l'auto-production d'électricité, de jardin, de machin. On va moins, peut-être, dépendre des autres.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Peut-être l'auto-production"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 57, "end": 59}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_059", "text": "Moins dépendre des autres, j'en sais rien. Ou plus, mais localement.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 58, "end": 60}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_060", "text": "Tu disais : qui produit de l'auto... ? De l'auto... je sais plus.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 59, "end": 61}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_061", "text": "Et du coup, dans les critères de performance, y'a... <PERSON><PERSON>, c'est pas tout de suite, je crois. Ah oui ? O<PERSON>, il faut attendre un peu. C'est numéro 5. Est -ce qu'il y a des nouvelles normes, un nouveau label ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Et du coup", "Et du coup", "du coup", "les critères", "les critères", "Et du coup", "les critères", "Alors ça", "des nouvelles normes", "les critères", "les critères", "les critères", "tout de suite", "les critères", "les critères", "les critères", "les critères", "les critères", "Et du coup", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 60, "end": 67}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_062", "text": "<PERSON>h ça, s<PERSON><PERSON><PERSON>, hein. <PERSON>ur qu'elle soit légitime, heu... est-ce que le fait de ne pas aggraver les circonstances, ça peut être un critère de légitimité pour l'entreprise de demain ?", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["Bah ça", "sûrement, hein", "le fait", "les circonstances"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 61, "end": 63}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_063", "text": "<PERSON><PERSON>, oui, donc du coup, c'est déjà... Elle va maîtriser ses achats, ses matières premières. <PERSON><PERSON> contre, je pense que... oui. <PERSON>ois, ça, je me dis, ça c'est la structure au sens large, et les hommes et les femmes qui sont dedans, eux, ils vont devoir survivre à ça. Qu'est-ce qu'on va leur proposer pour vivre heu... dans ce truc tout pourri ? À mon avis, c'est aussi la place du travail qui va changer. Tu pourras plus travailler 40 heures par semaine.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 84, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", "la place", "ses achats", "ses matières premières", "sens large", "ce truc", "mon avis", "40 heures", "mon avis", "40 heures", "la place", "mon avis", "les femmes", "les hommes", "les femmes", "40 heures", "mon avis", "la place", "mon avis", "les Hommes", "mon avis", "du travail", "mon avis"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 62, "end": 68}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_064", "text": "À mon avis, elle va devoir un autre heu... parce que si tu... regarde, tu peux moins consommer, que peut-être, le mouvement des entreprises, tu vois, en économie, valoriser, recycler, produire toi-même, être... ça veut dire que nous-mêmes, dans nos habitations et avec nos familles, on va devoir faire ça. Mais pour pouvoir faire ça, si tu dois faire ton jardin, si tu dois...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 64, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": [", elle", "mon avis", "mon avis", "un autre heu", "le mouvement", "nos habitations", "avec nos familles", "ton jardin", "mon avis", "mon avis", "des entreprises", "mon avis", "mon avis", "mon avis", ", tu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 63, "end": 65}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_065", "text": "Ce sera peut-être 40 heures plus encore du temps, Ah bah oui, faudrait pas que... enfin moi, par exemple, j'ai un frère qui a quitté le monde du travail, parce qu'il veut vivre de manière alternative, heu... ça prend du temps, hein. <PERSON>ien sûr.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le monde", "40 heures", "40 heures", "peut-être 40 heures", "enfin moi", "un frère", "le monde", "manière alternative", "40 heures", "le monde", "... enfin moi", "du travail", "<PERSON><PERSON> moi", "le monde", "le monde", "le monde", "le monde", "<PERSON><PERSON> moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 64, "end": 66}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_066", "text": "Donc il va nous falloir du temps de vie, il va falloir à nos enfants du temps de vie qui soit dédié à ça. Tu vois ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["nos enfants"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 65, "end": 67}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_067", "text": "Donc l'entreprise, elle pourra plus heu... il faudra plus s'attendre à ce que les hommes et les femmes y vivent 40 heures par semaine. À mon avis, c'est mort.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": [", elle", "mon avis", "40 heures", "mon avis", "40 heures", "mon avis", "les femmes", "les hommes", "les femmes", "40 heures", "mon avis", "mon avis", "les Hommes", "mon avis", "mon avis"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 66, "end": 68}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_068", "text": "C'est : ils vont bosser deux ans, ils se barrent un an à faire des... à vivre. Vraiment, c'est en train de changer, c'est déjà en train de changer.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["deux ans", "un an", "Vraiment, c'est en train de changer, c'est déjà en train"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 67, "end": 69}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_069", "text": "Tu vois, par exemple, tous les nouveaux trucs qu'on observe, les flex -offices, les machins , est -ce que ça, c'est des choses qui vont...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des choses", "tous les nouveaux trucs", "les flex", ", les machins", "des choses", "des choses", "des choses", "que ça", "que ça", "des choses", "des choses", "des choses", "des choses", "des choses"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 68, "end": 73}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_070", "text": "? C'est-à-dire pour heu... pour avoir moins d'impact, les entreprises prennent des locaux plus petits avec beaucoup la consigne de « tu n'as plus ton petit bureau à toi ». du coworking.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les entreprises", "les entreprises", "C'est-à-dire pour heu", "les entreprises", "des locaux plus petits", "beaucoup la consigne", "ton petit bureau"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 69, "end": 71}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_071", "text": "En fait, on va avoir des salariés qui sont mobiles, enfin qui sont mobiles... qui de temps en temps seront ailleurs, parce qu'ici, ils vont être obligés de participer à l'équilibre du monde, qui vont venir, tu vois, aujourd'hui, l'entreprise et les organisations, elles... elles balisent énormément le temps de vie. Ah bah oui.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les organisations", "les organisations", "les organisations", "les organisations", ", elle", "des salariés", "les organisations", "le temps", "le temps", "le temps", "le temps", "le temps", "le temps", "le temps", ", tu", "en fait", "le temps", "le temps", "le temps", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 70, "end": 72}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_072", "text": "Donc il va falloir que les chefs d'entreprise de demain, ils acceptent que les salariés soient pas sous leur nez. Quand on voit ce que ça a produit, le télétravail... Qu'elle soit légitime et qu'elle soit adaptable.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le télétravail", "les chefs", "le télétravail", "que ça", "que ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 71, "end": 73}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_073", "text": "Il va falloir qu'on propose aussi une nouvelle manière de vivre. Mais c'est... ce sera essentiel. Le travail, il va être réorganisé, c'est obligé, quoi.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le travail", "une nouvelle manière", "Le travail", "le travail", "le travail"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 72, "end": 75}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_074", "text": "D'ailleurs, dans les pays où il fait 50 degrés l'été, les gens, ils travaillent pas l'après-midi. C'est... ça n'a... C'est peut-être ça, l'organisation.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les gens", "les gens", "les gens", "les gens", "Les gens", "les gens", "les gens", "les gens", "les gens", "les pays", "50 degrés", "les gens", "les gens", "Les gens", "les gens", "les gens", "Les gens", "les gens", "les gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 73, "end": 75}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_075", "text": "On va peut-être travailler... on va peut-être commencer les journées à 5 heures du matin, 6 heures du matin, pour finir à... à 13 heures, et puis voilà. À 13 heures, et puis heu... mais y'a déjà des endroits où c'est le cas.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les journées", "5 heures", ", 6 heures", "13 heures", "13 heures", "des endroits", "5 heures"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 74, "end": 76}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_076", "text": "Par exemple, moi, j'ai fait heu... j'ai travaillé dans les pommiers avant de faire une reconversion professionnelle, et quand à 5 heures du matin il faisait déjà 26 degrés, bah voilà, t'organises ton travail pour être à l'ombre, et puis à 13 heures, t'as fini ta journée, quoi. Ou comme les pommiers en Espagne ou en Italie.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", moi", ", moi", ", moi", "5 heures", "13 heures", "13 heures", "les pommiers", "une reconversion professionnelle", "5 heures", "26 degrés", "ton travail", "être à l'ombre", "puis à 13 heures", "ta journée", "Ou comme les pommiers", "en Italie", ", moi", ", moi", ", moi", "... j'"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 75, "end": 77}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_077", "text": "<PERSON><PERSON>, et puis peut-être que heu... Alors je me permets juste de vous déranger une petite minute. Heu... on a, pour vous accompagner sur la suite de la réflexion sur la légitimité toujours, fait des petites cartes « parties prenantes ».", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 41, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la réflexion", "La légi", "la légitimité", "une petite minute", "la suite", "la réflexion", "la légitimité", "des petites cartes", "parties prenantes", "la légitimité", "parties prenantes", "parties prenantes", "la légitimité", "la légitimité", "parties prenantes", "la réflexion", "que heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 76, "end": 78}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_078", "text": "Parce que du coup, la légitimité, c'est notamment auprès de toutes les parties prenantes de l'organisation, donc on a un certain nombre de qu... sujets, on a identifié un certain nombre de parties prenantes. Ce que je vous propose, c'est de regarder si y'en a qui vous aident à avancer ou à débattre, parce que peut-être que ça va juste vous faire débattre, mais c'est intéressant. Et toutes celles que vous utilisez, sur lesquelles vous vous arrêtez, vous les mettez de côté, comme ça on sait que vous les avez utilisées.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 91, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", "La légi", "la légitimité", "parce que du coup", "parce que du coup", "la légitimité", "parties prenantes", "la légitimité", "toutes les parties prenantes de l'organisation", "un certain nombre", "qu... sujets", "un certain nombre", "parties prenantes", "toutes celles", "parties prenantes", "les parties prenantes", "que ça", "que du coup", "que ça", "comme ça", "Comme ça", "comme ça", "la légitimité", "la légitimité", "comme ça", "comme ça", "parties prenantes", "un certain nombre", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 77, "end": 80}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_079", "text": "Et dernier point, c'est : si jamais y'a des choses qui manquent en parties prenantes, où vous dites « ils en ont oublié », on en a des vierges, donc vous les notez, pareil. OK, ça marche.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des choses", "parties prenantes", "parties prenantes", "Et dernier point", "des choses", "parties prenantes", "des vierges", "OK, ça marche", "des choses", "des choses", "parties prenantes", "des choses", "des choses", "des choses", "des choses", "des choses"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 78, "end": 80}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_080", "text": "Y'a quoi d'autre ? OK, toutes ces parties prenantes. Je pense qu'on peut quand même... on va regarder, même si on n'est pas vraiment... on n'est pas vraiment en panne, hein, en vrai.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["parties prenantes", "parties prenantes", "parties prenantes", "parties prenantes"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 79, "end": 82}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_081", "text": "Client Alors déjà heu... après, moi, c'est vraiment... c'est hyper orienté, y'a des gros biais, heu... je suis carrément... enfin pour moi, les actionnaires, ça ne devrait pas exister. C'est-à-dire que des gens qui gagnent de l'argent juste parce qu'ils posent de l'argent quelque part et qui sont rémunérés parce qu'ils ont mis de l'argent quelque part, ça va pas.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 60, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", moi", "pour moi", "des gens", ", moi", "quelque part", "des gens", ", moi", "des gens", "des gens", "Quelque part", ", moi", "des gros biais", "enfin pour moi", "les actionnaires", "des gens", "quelque part", "quelque part", "des gens", ", moi", "des gens", "des gens", "les actionnaires", "quelque part", ", moi", "Après, moi", "pour moi", "des gens", "des gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 80, "end": 82}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_082", "text": "<PERSON><PERSON>, et donc heu... Comment il a fait pour retrouver ? Parce que les actions... Il a des actions, son argent est placé.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des actions", "son argent"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 81, "end": 83}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_083", "text": "Et tout ce qui va, de l'argent placé, donc il a ret... Il gagne des milliards, il fait... il a plus besoin de rien faire.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Tout ce qui", "tout ce", "des milliards", "tout ce"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 82, "end": 84}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_084", "text": "<PERSON><PERSON>, y'a... il faudrait séparer l'actionnaire... le petit actionnaire, l'artisan qui crée sa boîte, il a un impact économique local, social, du géant mondial qui... qui fait rien. On peut... on peut pas tout...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 1, "tension_strength": 0, "total_indicators": 2}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["le petit actionnaire", "sa boîte", "un impact économique", "un impact", "un impact", "un impact"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 83, "end": 85}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_085", "text": "Ils voulaient être à l'équilibre pour pouvoir sortir les salaires, pouvoir avoir un coût de fonctionnement, mais par contre... Oui, bah ça, après... C'est des stratégies, oui. Mais par contre, des bénéfices, pourquoi faire ? Puisque l'entreprise heu... a marché pendant 30 ans, elle avait huit salariés, ça permettait de faire vivre tout le monde, mais bah voilà, mes parents, ils se sont pas construit de piscine, ils se sont pas fait... ils ont pas acheté un château en Espagne et c'est... pour eux, c'était pas la valeur heu... pour eux, ils étaient pas légitimes à faire du bénéfice. Ils étaient légitimes à faire vivre décemment tous les gens qui doivent en vivre et pour autant, voilà, les bénéfices, c'est pour ça...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 122, "thematic_indicators": {"performance_density": 0.819672131147541, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.6557377049180328}, "noun_phrases": ["tout le monde", "les gens", "les gens", "les gens", "les gens", "Les gens", "les gens", "les gens", "tout le monde", "tout le monde", "le monde", "les gens", "les gens", "Bah ça", ", elle", "le monde", "les gens", "des bénéfices", "les salaires", "un coût", "l'entreprise heu", "30 ans", "huit salariés", "tout le monde", "mes parents", "un château", "tous les gens", "les bénéfices", ", c'est pour ça", "leur heu", "tout le monde", "tout le monde", "les gens", "Les gens", "<PERSON>ut le monde", "<PERSON>ut le monde", "le monde", "tout le monde", "tout le monde", "les gens", "30 ans", "<PERSON>ut le monde", "tout le monde", "les salaires", "les gens", "Les gens", "tout le monde", "30 ans", "30 ans", "les gens", "des bénéfices", "le monde", "les gens", "le monde", "le monde", "le monde"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 84, "end": 88}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_086", "text": "<PERSON><PERSON>, ça, voil<PERSON>, mais c'est comme dans le... les Scop, c'est-à-dire qu'on rémunère d'abord la valeur travail, plutôt que la valeur capital, et je crois que c'est ça, tous les actionnaires sont pas des capitalistes, il va falloir... <PERSON><PERSON>, c'est pour ça, le mot « actionnaires », il faut le...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["C'est ça", "les actionnaires", "C'est ça", ", c'est pour ça", "abord la valeur travail", "plutôt que la valeur capital", "tous les actionnaires", "C'est ça", "les actionnaires", "<PERSON>", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 85, "end": 87}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_087", "text": "cette... ce côté sortie du capitalisme, enfin du néo-libéralisme, pour heu... pour faire que... on veut moins de disparités, parce que finalement, c'est aussi les très grosses disparités qui font que... bah on peut pas s'acheter du bio, que... voilà, que les riches, eux, ils crament du CO2 tant et plus, que ils savent pas quoi faire de leur argent, donc ils achètent des trucs qui sont hyper polluants et heu... En fait, c'est pas tant que de prendre heu... j'allais dire, on va pas tous devenir pauvres pour sauver la planète. Y'en a qui ont gagné de l'argent, et j'allais dire, très bien pour eux.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 106, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.9433962264150942}, "noun_phrases": ["des trucs", "des trucs", "des trucs", "des trucs", "des trucs", "la planète", "la planète", "la planète", "cette... ce côté", "parce que finalement, c'est aussi les très grosses disparités", "les riches", "leur argent", "des trucs", "la planète", "la planète", "ce côté", "des trucs", "des trucs", "en fait", "... j'", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 86, "end": 89}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_088", "text": "Le problème, c'est quand dans une entreprise, que tu rémunères d'abord le capital, alors que ceux qui ont produit concrètement la richesse heu... arrivent même pas à payer leurs factures, je me dis, c'est ce déséquilibre... après, moi je me dis, si t'as bien payé tous tes collaborateurs, etc., mais qu'il reste du bénéfice pour récompenser les proprié... j'allais dire OK, mais c'est ce... cette primauté. <PERSON><PERSON>, la première pensée, elle doit être vers ceux qui ont produit.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 78, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 2, "side_b": 0, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", ", moi", "une entreprise", "une entreprise", ", moi", ", moi", "Une entreprise", "le problème", ", elle", "une entreprise", ", moi", ", moi", "Le problème", "abord le capital", "la richesse heu", "leurs factures", "tous tes collaborateurs", "les proprié", "cette primauté", "la première pensée", "Une entreprise", "Le problème", "Le problème", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", ", moi", "tes collaborateurs", "Après, moi", "... j'"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 87, "end": 89}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_089", "text": "Et puis le côté démesure, quoi. C'est ça, démesure.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "Le <PERSON>", "le <PERSON><PERSON>", "le <PERSON><PERSON>", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "le <PERSON><PERSON>", "C'est ça", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 88, "end": 90}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_090", "text": "Donc c'est une entrepr... Une entreprise juste.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 7, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "Une entreprise", "une entreprise", "Donc c'est une entrepr", "Une entreprise juste", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 89, "end": 91}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_091", "text": "Mais en même temps... Mais il... il s'enrichit aux dépens de la planète, des gens qui fabriquent... Et des gens qui achètent. Et des gens qui achètent, qui vont faire un crédit pour s'acheter un iPhone.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 36, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des gens", "des gens", "même temps", "des gens", "des gens", "la planète", "la planète", "la planète", "des gens", "des gens", "la planète", "Mais en même temps", "la planète", "des gens", "Et des gens", "un crédit", "un iPhone", "des gens", "même temps", "même temps", "des gens", "des gens", "même temps"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 90, "end": 92}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_092", "text": "Mais je pense qu'il faut faire atten... enfin à mon sens, il faut être mesuré dans les... dans les analyses ou les critères, mais... Bah être mesuré, quand on n'aura plus rien à bouffer, plus rien à boire, on sera plus mesurés. C'est ça, le problème. Moi, personnellement... personnellement, je vais pas acheter des conneries, comme tu disais, qui vont pas nous servir. Heu... y'a pas... on fait attention, on fait du... on fait du compost, on fait notre jardin, on fait... on fait énormément de choses, on fait attention à la nature, mais pour ça, si j'ai besoin d'un téléphone, je vais bien être obligé d'acheter mon téléphone.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 109, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.5504587155963302}, "noun_phrases": ["la nature", "C'est ça", "la nature", "les critères", "les critères", "les critères", "le problème", "C'est ça", "Le problème", "Le problème", "Le problème", "enfin à mon sens", "les analyses", "les critères", "plus rien", "des conneries", "notre jardin", "la nature", "un téléphone", "mon téléphone", "mon téléphone", "C'est ça", "les critères", "les critères", "les critères", "les critères", "C'est ça", "les critères", "des conneries", "C'est ça", "les critères", "C'est ça", "les critères", "C'est ça", "C'est ça", "les critères", "la nature", "les critères", "les critères", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 91, "end": 95}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_093", "text": "<PERSON><PERSON>, mais du coup, heu... tu vois, moi, je pré<PERSON><PERSON> attendre pour m'acheter mon téléphone et l'acheter chez Fairphone, parce que je sais qu'y'a pas d'esclave, parce que je sais qu'ils ont des normes dans les mines, parce que... <PERSON><PERSON>, voilà, y'a peut-être des choses qu'on peut... mais apr<PERSON>, finalement, c'est le consommateur qui va orienter... Mais en fait c'est... ça, bah justement, ça demande de l'esprit critique. … qui va orienter tout ça, qui va dire aux entreprises... Bah oui, du coup, ça demande d'avoir... d'avoir ouvert les yeux sur : bah c'est que je consomme, ça a un impact. O<PERSON>, mais disons, la... la force, je pense que... il faut travailler le consommateur et pas travailler heu... l'industriel.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 120, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.6666666666666667}, "noun_phrases": [", moi", "Le consommateur", "tout ça", "du coup", ", moi", ", moi", "des choses", "<PERSON><PERSON> du <PERSON>", ", du coup", "des choses", ", moi", ", moi", "des choses", "le consommateur", "mon téléphone", "mais du coup", "mon téléphone", "des normes", "les mines", "des choses", "apr<PERSON>, finalement, c'est le consommateur", "l'esprit critique", "tout ça", ", du coup", "les yeux", "un impact", "la force", "le consommateur", "le consommateur", "<PERSON><PERSON> du <PERSON>", "tout ça", ", du coup", ", moi", "des choses", "un impact", "un impact", "des choses", "des choses", "tout ça", "des choses", "en fait", "<PERSON><PERSON> du <PERSON>", "mais du coup", "des choses", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 92, "end": 96}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_094", "text": "<PERSON><PERSON>, mais l'industriel, enfin je veux dire, je dirais que tout est lié, en fait, parce que si le consommateur arrête d'acheter, l'industriel, il va bouger. <PERSON><PERSON>, voil<PERSON>, c'est bien pour ça, c'est ce que je dis.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le consommateur", ", en fait", "le consommateur", "le consommateur", "mais l'industriel", "le consommateur", ", voilà, c'est bien pour ça", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 93, "end": 95}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_095", "text": "Si Apple dit demain : on arrête de faire de la merde. Les gens qui sont à fond Apple vont dire « oh bah super », heu... parce qu'ils sont de toute façon derrière cette marque, parce que ça représente quelque chose.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les gens", "les gens", "les gens", "les gens", "Les gens", "quel<PERSON> chose", "toute façon", "les gens", "les gens", "quel<PERSON> chose", "quel<PERSON> chose", "toute façon", "les gens", "les gens", "toute façon", "toute façon", "les gens", "quel<PERSON> chose", "quel<PERSON> chose", "les gens", "la merde", "Les gens", "cette marque", "quel<PERSON> chose", "que ça", "que ça", "quel<PERSON> chose", "quel<PERSON> chose", "les gens", "toute façon", "les gens", "toute façon", "toute façon", "Les gens", "les gens", "les gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 94, "end": 96}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_096", "text": "Que ce soit le consommateur ou heu... l'entreprise, il faut que ce soit avec et pour un monde habitable, en fait. Mais du coup, heu... pour ça, ça veut dire qu'il faut enlever ses œillères et qu'il faut accepter de se dire « bon, on va faire comment quand ce sera la merde ? ».", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un monde", "Le consommateur", "un monde", "du coup", "<PERSON><PERSON> du <PERSON>", ", en fait", "le consommateur", "mais du coup", "le consommateur", "le consommateur", "la merde", "Que ce soit le consommateur", "et pour un monde habitable", "<PERSON><PERSON> du <PERSON>", "ses œillères", "un monde", "en fait", "<PERSON><PERSON> du <PERSON>", "mais du coup", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 95, "end": 97}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_097", "text": "C'est pour ça que regarder un film comme « <PERSON><PERSON><PERSON> », je trouve ça bien. Je regarderai, oui, je regarderai. Vraiment, ça te montre des gens qui font des trucs pour que ça change et ça marche.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des gens", "des trucs", "des gens", "des trucs", "des trucs", "des trucs", "des trucs", "des gens", "des gens", "des gens", "des gens", "des trucs", "des gens", "un film", "« <PERSON><PERSON><PERSON>", "des trucs", "et ça marche", "que ça", "des gens", "que ça", "Et ça", "et ça", "des trucs", "des gens", "des gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 96, "end": 99}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_098", "text": "Parce que moi, je vois, dans les créations d'entreprises aujourd'hui, parce que je suis aussi intervenant en chambre de commerce pour tout ce qui est créateurs, tout ça, et je vois qu'y'a beaucoup de... enfin de plus en plus de projets avec heu... une économie circulaire, où c'est qu'on va recycler, rechercher, où c'est que les... les entrepreneurs s'interrogent sur comment est-ce que je vais réduire mon impact. Mais bien sûr.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 71, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["tout ça", "Parce que moi", "Parce que moi", "économie circulaire", "Tout ce qui", "tout ce", "tout ça", "Parce que moi", "les créations", "tout ce", "tout ça", "c'est qu'", "les entrepreneurs", "mon impact", "que moi", "Parce que moi", "tout ça", "parce que moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 97, "end": 99}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_099", "text": "<PERSON>c ça, on le voit de plus en plus. <PERSON><PERSON> comment ils sont organisés, ces petites entreprises, pour soutenir ça ? C'est-à-dire, concrètement ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 98, "end": 102}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_100", "text": "En fait, je... alors moi, j'ai une petite entreprise, donc si demain je décide d'avoir un... de maîtriser mes achats, ma... c'est facile. On est cinq.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["<PERSON><PERSON> moi", "<PERSON><PERSON> moi", "alors moi", "une petite entreprise", "mes achats", "<PERSON><PERSON> moi", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 99, "end": 101}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_101", "text": "Parce que les gros modèles organisationnels, si t'as un siège à Paris, des antennes à Lyon et machin, il va falloir qu'ils soient tous connectés, heu... il va... enfin tu vois, je me dis, est-ce que du coup, vu ça et vu les préoccupations de... est-ce qu'on va pas devoir revenir à des entreprises qui sont à taille plus... plus humaine ? Plus humaine, sûrement.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 65, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["du coup", "gros modèles organisationnels", "un siège", "des antennes", "que du coup", "vu ça", "les préoccupations", "des entreprises"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 100, "end": 102}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_102", "text": "Mais est-ce que du coup, ce serait pas les entreprises en local qui... qui tissent ? C'est le modèle coopératif, c'est-à-dire au lieu d'aller chercher la sécurité dans les... les trucs, les frais généraux d'un groupe, bah en fait, on mutualise avec le voisin. On n'est pas obligés de mutualiser avec... Oui, peut-être.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["les entreprises", "ce serait", "du coup", "les entreprises", "les entreprises", "que du coup", "Mais est-ce que du coup", ", ce serait pas les entreprises", "qui... qui tissent", "la sécurité", "les... les trucs", "les frais généraux", "un groupe", "le voisin", "mod<PERSON><PERSON>", "le modèle", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 101, "end": 105}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_103", "text": "<PERSON><PERSON>, moi je vois, une association de commerçants qui pourrait mutualiser les coûts, ça marche pas. Pourquoi ça marche pas ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", moi", "une association", ", moi", ", moi", ", moi", ", moi", "une association", "les coûts", ", moi", "Après, moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 102, "end": 104}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_104", "text": "En fait, je me dis que peut-être, ce modèle de groupe, il aura heu... il sera tombé et puis je... tu vois, pour avoir des enfants qui vont avoir cet âge de travailler, leur génération, ils vont pas obéir à un mec qu'ils ont jamais vu. Ça, c'est mort. Ça, c'est mort. Ils vont dire « lui, il est où ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un mec", "il est", "ce modèle", "des enfants", "cet âge", "leur génération", "un mec", "« lui", "en fait", "des enfants", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 103, "end": 107}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_105", "text": "Et puis pour eux, Paris, j'allais dire, c'est pas parce que c'est la capitale que c'est Paris, enfin tu vois, ils sont vraiment... y'a quelque chose qui s'est... qui s'est affaissé, je trouve. Oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "puis pour eux", "quel<PERSON> chose"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 104, "end": 106}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_106", "text": "Ils sont plus indiscipli... enfin « plus indisciplinés »... Non, ils sont... disons, ils considèrent un humain pour un humain et plus non plus une fonction. Ils considèrent un...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["plus non plus une fonction"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 105, "end": 108}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_107", "text": "avant, la loyauté de l'institution et... donc je me dis, l'entreprise de demain, elle va devoir être performante en local, je pense que ça va remettre en question vraiment le principe heu... D'un côté, la vision, c'est : la vie d'abord, et puis heu... le travail ensuite. En fait, ça fait un siècle qu'on se focalise sur le travail, on met tout le monde au boulot et voilà le résultat.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["tout le monde", "tout le monde", "tout le monde", "le monde", "le travail", ", elle", "le monde", "Le travail", "tout le monde", "tout le monde", "tout le monde", "<PERSON>ut le monde", "<PERSON>ut le monde", "un côté", "que ça", "que ça", "la loyauté", "vraiment le principe heu", "un côté", ", la vision", ", c'est : la vie", "... le travail", "un siècle", "le travail", "le monde", "le résultat", "tout le monde", "tout le monde", "la vie", "<PERSON>ut le monde", "et tout le monde", "tout le monde", "en fait", "tout le monde", "le travail", "le monde", "le monde", "le monde", "le monde", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 106, "end": 108}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_108", "text": "Non, il a pas le droit. Il faut qu'il aille acheter son steak congelé qui vient je sais pas d'où, parce que tu... t'as pas le droit d'aller... Voilà.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le droit", "le droit", "son steak", "le droit", "le droit", "le droit", "le droit", "le droit"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 107, "end": 110}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_109", "text": "Donc je pense qu'on va créer du groupement, mais un groupement qui sera... je sais pas, hein, mais je me dis, plus heu... plus horizontal, plutôt que vertical, parce qu'il va... si on veut chercher du local, il va bien falloir qu'on... qu'on tisse des liens comme ça, plutôt que comme ça. Mais on voit bien, après, dans des zones comme Saint-Étienne ou des... des endroits où l'industrie, elle s'est écroulée comme dans le Nord, heu... en fait, la résilience d'un territoire, elle se fait heu... par la multiplication de petites heu... structures.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 93, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 2, "side_b": 0, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["la résilience", ", elle", "des endroits", "un groupe", "des liens", "comme ça", "plutôt que comme ça", "dans le Nord", "un territoire", "la multiplication", "petites heu", "La résilience", "la résilience", "Comme ça", "comme ça", "comme ça", "comme ça", "en fait", "comme ça", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 108, "end": 110}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_110", "text": "C'est dur. C'est dur ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 5, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 109, "end": 111}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_111", "text": "C'est... <PERSON><PERSON>, vous pouvez aussi l'écrire, enfin v<PERSON>, soyez libres d'écrire ce que vous avez envie d'écrire. Y'a trop de trucs.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 110, "end": 112}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_112", "text": "Et d'ailleurs, la résilience dans la diversité, elle se voit aussi à l'échelle végétale, c'est-à-dire que quand tu clones un végétal et que tu le mets sur des hectares, il se fait défoncer. Ah oui", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la résilience", ", elle", "La résilience", "la diversité", "la résilience", "la diversité", "l'échelle végétale", "des hectares"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 111, "end": 113}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_113", "text": "Si y'a un problème avec les pois, il va quand même avoir des lentilles. Et ça... Et puis tu travailles les saisons, tu respectes...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un problème", "un problème", "un problème", "les pois", "des lentilles", "Et ça", "les saisons", "les saisons", "et ça", ", tu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 112, "end": 114}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_114", "text": "Et les saisons, si c'est sec, bah en fait, c'est... y'a tel végétal qui va s'en sortir. En fait, la... la diversité, c'est ça qui fait l'équilibre, aussi. Et comme y'a plein d'entreprises qui se... qui se mettent en filiales, non, justement, pour pas qu'y'en ait une qui embarque heu... Oui, on peut imaginer des montages comme ça, oui, tout à fait, oui. Y'en a qui éviter que ça se casse la gueule de partout, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 76, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["C'est ça", ", c'est ça", "C'est ça", "que ça", "C'est ça", "que ça", "comme ça", "la diversité", "la diversité", "Comme ça", "les saisons", "les saisons", "fait, la... la diversité", ", c'est ça", "des montages", "comme ça", "la gueule", "comme ça", "comme ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "en fait", "C'est ça", "comme ça", "en fait", "C'est ça", "que heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 113, "end": 118}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_115", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, oui, de... <PERSON><PERSON>, à l'échelle universelle, une galaxie, elle... une petite galaxie, elle va s'agréger avec une autre galaxie pour pouvoir être plus stable et que heu... ça... ça soit cohérent et que ça reste cohérent.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", elle", "que ça", "que ça", "l'échelle universelle", "une galaxie", "une petite galaxie", "une autre galaxie", "que heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 114, "end": 117}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_116", "text": "Donc heu... y'a quand même une nécessité de... d'agrégation, au moins un minimum, parce que si on est juste tout seul à faire son truc, c'est... c'est moins stable que quand on est plusieurs. Oui. <PERSON>, quels sont les critères de performance ? C'est ça, maintenant ? Je crois que ça va être... c'est bientôt. Ils ont pas sonné le... Y'a deux minutes. La communauté locale. Le gouvernement. Je vous propose qu'on... Les créanciers, tiens.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 75, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["accumulation_partage", "individuel_collectif", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["C'est ça", "les critères", "les critères", "les critères", "C'est ça", "les critères", "que ça", "C'est ça", "que ça", "deux minutes", "quand même une nécessité", "... d'agrégation", "moins un minimum", "son truc", "les critères", "deux minutes", "La communauté locale", "Le gouvernement", "Les créanciers", ", tiens", "deux minutes", "les critères", "les critères", "Le gouvernement", "les critères", "C'est ça", "les critères", "C'est ça", "les critères", "C'est ça", "les critères", "C'est ça", "C'est ça", "les critères", "les critères", "les critères", "un minimum", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 115, "end": 125}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_117", "text": "Je vous propose qu'on coupe pour le temps de la légitimité. Heu... du coup, je vous propose qu'on coupe sur la légitimité, on va passer à la partie « critères de performance ».", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", "la partie", "La légi", "la légitimité", "... du coup", "le temps", "la légitimité", "la légitimité", "le temps", "la légitimité", "... du coup", "la légitimité", "la partie", "le temps", "le temps", "le temps", "le temps", "le temps", "le temps", "... du coup", "le temps", "le temps"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 116, "end": 118}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_118", "text": "On a pensé aux actionnaires. Oui, les actionnaires, on l'a fait.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les actionnaires", "les actionnaires"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 117, "end": 119}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_119", "text": "<PERSON><PERSON>, et puis ces deux-là. Super, merci. En vrai, tout a été un peu... <PERSON><PERSON>, si elles vous ont pas... Non, pas plus que ça. Si les autres, vous les avez déjà en tête... merci, à tout de suite. Clap 3, travail collectif.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["Travail collectif", ", merci", "travail collectif", ", merci", "que ça", "que ça", "<PERSON>, merci", "... merci", "travail collectif", "tout de suite"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 118, "end": 124}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_120", "text": "<PERSON><PERSON> l<PERSON>, on est bien sur les critères de performance et... On a le droit de rêver ? Je vous le dis tout de suite, au bout d'un... une demi-heure là-dessus et au bout d'un quart d'heure, de la même façon, je vais vous donner des cartes « critères de performance », c'est la même logique :", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une demi-heure", "les critères", "les critères", "les critères", "les critères", "le droit", "le droit", "le droit", "les critères", "les critères", "le droit", "une demi-heure", "un quart", "la même façon", "des cartes", "tout de suite", "un quart", "les critères", "les critères", "le droit", "le droit", "le droit", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 119, "end": 121}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_121", "text": "Tu avais bien relancé ? Oui. Est -ce que heu... quand tu parles de critères de performance, est-ce qu'on peut avoir un petit heu... comme pour la légitimité, histoire qu'on parle à peu près la même langue ? Oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["La légi", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "un petit heu", "pour la légitimité", "peu près la même langue", "de critères", "que heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 120, "end": 125}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_122", "text": "Le critère de performance, c'est : qu'est-ce que heu... là, on est plutôt en interne, si je peux le dire comme ça, de l'entreprise, c'est-à-dire comment l'entreprise va considérer qu'y'a une performance sur son marché vis-à-vis de l'environnement. En fait, c'est aussi un peu lié aux parties prenantes.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 48, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 1.0}, "noun_phrases": ["parties prenantes", "parties prenantes", "parties prenantes", "comme ça", "Comme ça", "comme ça", "comme ça", "Le critère", "comme ça", "une performance", "son march<PERSON>", "parties prenantes", "en fait", "comme ça", "en fait", "que heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 121, "end": 123}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_123", "text": "D'ailleurs, les collègues l'ont fait de façon plus systémique. Critères de réussite, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les collègues", "façon plus systémique"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 122, "end": 124}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_124", "text": "C'est les critères de réussite, comment on considère qu'elle est... donc performante, c'est qu'elle est heu... utile, c'est qu'elle ait ré<PERSON>i, qu'elle marche, en fait, qu'elle peut et qu'elle est, surtout, peut-être je vais appeler ce mot-là, « pérenne », en fait. Et du coup, est-ce que c'est des critères heu... des critères mesurables forcément ?", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 0.6}, "noun_phrases": ["Et du coup", "Et du coup", "du coup", "les critères", "les critères", "Et du coup", "les critères", ", en fait", "les critères", "c'est qu'", "les critères", "les critères", "qu'elle marche", "ce mot-là", "les critères", "les critères", "des critères", "les critères", "des critères", "les critères", "des critères", "les critères", "en fait", "Et du coup", "les critères", "les critères", "les critères", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 123, "end": 125}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_125", "text": "C'est... c'est une mesure. Bah ça peut être une...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Bah ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 124, "end": 126}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_126", "text": "En tout cas que l'impact carbone de l'entreprise soit une mesure qui est... O<PERSON>, c'est une des mesures. C'est quantifiable, oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "impact carbone", "tout cas", "l'impact carbone", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 125, "end": 127}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_127", "text": "Donc heu... éco-responsable par son heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 6, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["son heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 126, "end": 128}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_128", "text": "oui, du coup, y'a l'idée de mesure d'impact social. <PERSON><PERSON>, comment on mesurerait l'impact ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 15, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", ", du coup", ", du coup", ", du coup", "impact social"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 127, "end": 129}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_129", "text": "OK. Comment on pourrait mesurer l'engagement local de l'entreprise de demain ? Ah, peut-être que c'est le bien-être des salariés, peut-être ? Le bien-être des salariés, le turn-over ?", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["des salariés", "l'engagement local", "Le bien-être", "le turn", "Le bien-être", ", le turn", "Le bien-être", "Le bien-être", "le bien-être"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 128, "end": 132}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_130", "text": "Si y'a pas de turn-over... Heu... donc en critère de performance, le bien-être des collaborateurs. Oui, le bien-être des collaborateurs.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le bien-être", "de turn", "donc en critère", ", le bien-être", "<PERSON><PERSON>, le bien-être", "Le bien-être", "Le bien-être", "Le bien-être", "le bien-être"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 129, "end": 131}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_131", "text": "comment ça s'appelle ? Ah oui !", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 7, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 130, "end": 132}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_132", "text": "Mais oui. Le produit intérieur de bonheur, un truc comme ça ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un truc", "comme ça", "Comme ça", "comme ça", "comme ça", "comme ça", "Le produit", "le produit intérieur", "Le produit intérieur", "un truc", "un truc", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 131, "end": 133}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_133", "text": "Quels sont les critères sur lesquels s'appuie le PIB ? Je crois que ça doit être dans l'espérance de vie.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "les critères", "les critères", "que ça", "que ça", "les critères", "les critères", "les critères", "les critères", "le PIB", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 132, "end": 134}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_134", "text": "Inscrit dans la Constitution, il se veut une définition du niveau de vie, dans des termes globaux. Est", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la Constitution", "une définition", "des termes globaux"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 133, "end": 135}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_135", "text": "On a tout un tas de critère. Par exemple, moi, par an, je dois montrer le nombre de réunions que j'ai fait avec les collaborateurs, je dois prouver heu... la gouvernance, je dois trouver des exemples concrets de gouvernance partagée.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["la gouvernance", ", moi", "la gouvernance", ", moi", ", moi", ", moi", "un tas", ", moi", "tout un tas", "le nombre", "les collaborateurs", "la gouvernance", "des exemples concrets", ", moi", "le nombre", "Le nombre", "le nombre"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 134, "end": 136}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_G_seg_136", "text": "<PERSON><PERSON><PERSON>, on a des tableurs Excel qui deviennent de plus en plus précis pour heu... mesurer la coopération. C'est pas seulement... tu dis pas seulement que tu fais de la coopération, tu... t'es censé le prouver.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 36, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la coopération", "La coopération", "La coopération", "des tableurs", "la coopération", "la coopération", "la coopération", "la coopération", ", tu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 135, "end": 137}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_137", "text": "Donc heu... des outils de mesure... Peut-être qu'y'aura une commission qui va valider les... les critères, peut-être.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "Donc heu... des outils", "une commission", "les... les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 136, "end": 138}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_138", "text": "Donc elle sera éco-responsable, elle produira <PERSON>eut-être qu'y'aura de plus en plus d'avis clients, aussi, peut-être. Des retours de... d'expériences de clients, qui pourront peut-être valider des trucs, je sais pas.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des trucs", "des trucs", "des trucs", "des trucs", "des trucs", ", elle", "des trucs", "des trucs", "plus en plus d'avis clients", "Des retours", "... d'expériences", "des trucs"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 137, "end": 139}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_139", "text": "De toute façon, c'est ce qui s'essaie de partout, hein, même sur <PERSON>, vous achetez heu... sur Vinted, on note l'acheteur, le vendeur, heu... un peu de partout. <PERSON><PERSON>, je suis pas sûre de rêver de ça en 2050. Non, mais c'est peut-être un critère qui existera peut-être, après.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["toute façon", "toute façon", "toute façon", "toute façon", "toute façon", "même sur Vinted", "le vendeur", "toute façon", "toute façon"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 138, "end": 141}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_140", "text": "Ah, le côté participatif, tout va pas dans le sens négatif, mais finalement, heu... c'est le Panoptique de Bentham qui s'applique à tout, et tout le monde s'observe pour heu... que tout le monde se juge, mais que par contre, il puisse y avoir heu... la participation de chacun, quoi, une vraie démocratie heu... <PERSON><PERSON>, des critères de... je vais marquer... de participation.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["tout le monde", "Le <PERSON>", "le <PERSON><PERSON>", "tout le monde", "le <PERSON><PERSON>", "tout le monde", "le monde", "le monde", "tout le monde", "tout le monde", "tout le monde", "<PERSON>ut le monde", "<PERSON>ut le monde", "le monde", "tout le monde", "tout le monde", "<PERSON>ut le monde", "le côté participatif", "le sens négatif", "et tout le monde", "tout le monde", "des critères", "des critères", "des critères", "le <PERSON><PERSON>", "tout le monde", "le monde", "le monde", "le monde", "le monde"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 139, "end": 141}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_141", "text": "To<PERSON>, tu peux pas dire... n'importe quel patron peut dire « oui, oui, je fais de la coopération », et puis en fait, il le fait pas. Là, le recrutement, il est pas... enfin... la prise de décision, elle est pas légitimée. Je peux pas heu... je peux pas déménager le bureau et dire heu... on va aller bosser sans... Tu peux avoir du leadership, mais t'as pas le droit de faire de la dictature. C'est ça.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 77, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "il est", "le fait", ", elle", "C'est ça", "C'est ça", "de dé", "le droit", "le droit", "le droit", "la coopération", "La coopération", "La coopération", "le droit", "la coopération", "la coopération", "la coopération", "le droit", "le droit", "quel patron", "la coopération", "puis en fait", "le recrutement", "enfin... la prise", "le bureau", "du leadership", "le droit", "la dictature", "C'est ça", "C'est ça", "C'est ça", "C'est ça", ", tu", "en fait", "C'est ça", "en fait", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 140, "end": 145}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_142", "text": "Même si tu dis que c'est une bonne... j'ai une bonne raison de recruter cette personne-là. Bah oui, faut que t'arrives à le prouver à tes collaborateurs et collaboratrices. Exactement.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une bonne... j'", "une bonne raison", "cette personne", "tes collaborateurs", "... j'"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 141, "end": 144}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_143", "text": "Après, peut-être, dans les critères de performance, des critères de... de transparence, égalité hommes-femmes, sur les salaires, sur les... sur les relations, sur heu... parce qu'on va... on va vers de plus en plus de transparence, donc y'a des choses comme ça. Après, moi je... mais là, vraiment, c'est plus d'un point de vue sociologique, mais si je me dis : les gens vont devoir avoir plus de temps, participer à l'écosystème en tant que consommateur, habitant, peut-être que la place du travail va changer, il va falloir qu'on repense les modèles de rémunération, l'intelligence artificielle va récupérer certaines tâches, y'a ça aussi.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 103, "thematic_indicators": {"performance_density": 0.970873786407767, "legitimacy_density": 0.970873786407767, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.3883495145631068}, "noun_phrases": [", moi", "les gens", "les gens", "les gens", "les gens", "Les gens", "les relations", ", moi", "les gens", "les gens", ", moi", "des choses", "la place", "les gens", "les gens", "les critères", "les critères", "les critères", "la place", "les gens", "des choses", ", moi", ", moi", "les salaires", "les gens", "des choses", "les critères", "des choses", "Les gens", "comme ça", "Comme ça", "comme ça", "les critères", "les critères", "comme ça", "comme ça", "les critères", "les critères", "les gens", "des critères", ", moi", "les critères", "des critères", "... de transparence", ", égalité", "les salaires", "les relations", "des choses", "Après, moi", "les gens", "tant que consommateur", "la place", "les modèles", "l'intelligence artificielle", "certaines tâches", "l'intelligence artificielle", "l'intelligence artificielle", "des choses", "des choses", "Les gens", "les critères", "des critères", "les critères", "du travail", "des choses", "les gens", "les critères", "les critères", "les critères", "comme ça", "des choses", "les gens"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 142, "end": 144}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_144", "text": "<PERSON><PERSON><PERSON><PERSON>, il était très clairvoyant sur heu... <PERSON><PERSON>, les Économistes atterrés, qui a montré que... c'est... c'est faisable, en fait.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", en fait", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>, les Économistes", "en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 143, "end": 145}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_145", "text": "On a tous notre place là-dedans. Est -ce que t'as déjà un impact de l'intelligence artificielle dans ton groupe ? Alors moi non, tout de suite, non, mais les banquiers, par exemple.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON> moi", "<PERSON><PERSON> moi", "un impact", "alors moi", "tout de suite", "l'intelligence artificielle", "un impact", "tous notre place", "un impact", "l'intelligence artificielle", "ton groupe", "<PERSON><PERSON> moi", "les banquiers", "Les banquiers", "l'intelligence artificielle"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 144, "end": 148}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_146", "text": "Les banquiers, par exemple, aujourd'hui, les emprunts, on les fait sur Internet. Y'a plus besoin de conseiller pour valider un... une demande d'emprunt.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les banquiers", "Les banquiers", "les emprunts", "un... une demande"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 145, "end": 147}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_147", "text": "En tout cas, elle me dit « moi, dans dix ans, il faudra que je me dise que les... les nouveaux, ils arriveront en disant « non, non, ça, je... je vais le faire sortir par la machine et puis que ta mission à toi sera de relire », enfin ça va vraiment modifier les choses. Mais bien sûr. En sociologie, y'a... de dire : nous, c'est quoi, notre job ? C'est de observer, analyser, rédiger. Heu... l'observation, ça, ça va plutôt être nous, mais sur ce qui est analyse et rédaction, tu peux gagner un temps fou en utilisant... Oui, c'est ça, oui.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 104, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "un temps", "C'est ça", "tout cas", "tout cas", "tout cas", ", c'est ça", ", elle", "un temps", "tout cas", "tout cas", "C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "tout cas", ", c'est ça", "tout cas", "tout cas", "C'est ça", "tout cas", "la machine", "tout cas", "la machine", "tout cas", "dix ans", "la machine", "ta mission", "les choses", ", c'est quoi, notre job", "un temps fou", "<PERSON><PERSON>, c'est ça", "la machine", "tout cas", "C'est ça", "tout cas", "un temps", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", ", tu", "<PERSON><PERSON>, c'est ça", "C'est ça", "tout cas", "tout cas", "les choses", "les choses", "tout cas", "les choses", "tout cas", "tout cas", "tout cas", "<PERSON><PERSON>, c'est ça", "C'est ça", "les choses"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 146, "end": 153}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_148", "text": "Le truc, c'est que c'est un outil, donc heu... J'ai jamais essayé, moi, dans mon boulot, voir ce que ChatGPT pouvait produire.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["mon boulot", ", moi", ", moi", ", moi", ", moi", ", moi", "le truc", ", moi", "Le truc", "mon boulot", "... j'"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 147, "end": 149}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_149", "text": "Bah franchement, enfin c'est... Non, c'est bluffant. C'est vrai ? Ah oui, oui.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 148, "end": 152}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_150", "text": "Non seulement c'est bluffant... Ça te produit des... des contenus de formation.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["... des contenus"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 149, "end": 151}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_151", "text": "<PERSON><PERSON>, ça dépend de... il faut une version payante ou.... enfin v<PERSON>, mais je sais qu'en formation, une fois, des créateurs, y'en a un qui m'a montré justement le ChatGPT, là, et je me suis amusé avec, mais c'est... c'est impressionnant, quoi. Mais bien sûr. C'est impressionnant. Ils disent des conneries sur certaines choses. <PERSON><PERSON>, mais pas tant que ça, encore.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des conneries", "que ça", "que ça", "une version payante ou....", "une fois", ", des créateurs", "le ChatGPT", "des conneries", "certaines choses"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 150, "end": 155}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_152", "text": "C'est pas tant... enfin ça dépend du sujet, peut-être. Ça dépend du sujet.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 151, "end": 153}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_153", "text": "Je veux dire, même quelque... quelqu'un aujourd'hui qui a... enfin moi, j'ai eu... dans ma vie, j'ai eu droit à avoir une allocation adulte handicapé, parce que je sortais d'un cancer et que j'étais pas capable de bosser. Je n'ai pas rien fait. J'ai rien fait tant que j'avais besoin de rien faire. Oui, exactement, oui. Mais à partir du moment où heu... t'es reposé, où heu... physiquement t'es alerte, bah en fait, t'as envie de faire des choses.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 79, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des choses", "enfin moi", "ma vie", "des choses", "des choses", "des choses", "des choses", "... enfin moi", "ma vie", "une allocation adulte", "un cancer", "des choses", "des choses", "des choses", "en fait", "<PERSON><PERSON> moi", "des choses", "en fait", "<PERSON><PERSON> moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 152, "end": 157}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_154", "text": "Donc tu vas être dans des associations, tu vas aller heu... aider des copains. en fait. C'est ça.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "C'est ça", "C'est ça", "C'est ça", "des associations", "des copains", "C'est ça", "C'est ça", "C'est ça", ", tu", "en fait", "C'est ça", "en fait", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 153, "end": 156}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_155", "text": "Le faire, c'est un truc d'humain et donc même si on nous donne des sous, on va avoir envie de faire des choses. T'en as toujours, hein, qui font de la grève du zèle, mais c'est.... en tout cas, moi, c'est pas du tout la majeure partie des salariés que je reçois.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 52, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", moi", "un truc", ", moi", ", moi", "des choses", "tout cas", "tout cas", "tout cas", "tout cas", "des salariés", "tout cas", "des choses", ", moi", ", moi", "des choses", "des choses", "tout cas", "tout cas", "un truc", "tout cas", ", moi", "des choses", "tout cas", "tout cas", "tout cas", "tout cas", "des choses", "des choses", "la grève", "tout cas", "la grève", "un truc", "des choses", "tout cas", "tout cas", "tout cas", "tout cas", "des choses", "tout cas", "tout cas"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 154, "end": 156}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_156", "text": "Ah bah non. Ah oui, tu veux dire par rapport à ce qu'on... bah bien sûr.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", tu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 155, "end": 157}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_157", "text": "Alors qu'en fait... On pourra pas faire toujours de plus en plus, faut gagner de plus en plus de parts de marché, il faut croître tes revenus. Oui. Par contre, oui, le côté innovation, ça, je... ça peut être aussi un critère de performance, heu... dans le sens où heu... il faut favoriser l'adaptation. T'es obligé d'innover pour t'adapter. T'es obligé de prendre le temps de heu... bah de réflexion, comme aujourd'hui.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 72, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["Le <PERSON>", "le <PERSON><PERSON>", "le <PERSON><PERSON>", "de réflexion", "le temps", "le temps", "tes revenus", "le côté innovation", "être aussi un critère", "... dans le sens", "le temps", "le temps", "le temps", "de réflexion", "le temps", "le temps", "le <PERSON><PERSON>", "en fait", "le temps", "le temps", "le temps", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 156, "end": 162}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_158", "text": "Donc est-ce que dans les critères de performance, il peut y avoir heu... un truc qui serait de l'ordre de heu... bah savoir prendre le temps de réfléchir à ce qu'on fait, pourquoi on le fait et comment on le fait.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 41, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un truc", "les critères", "les critères", "les critères", "le fait", "le temps", "les critères", "les critères", "le temps", "les critères", "les critères", "un truc", "les critères", "les critères", "les critères", "le temps", "les critères", "un truc", "le temps", "le temps", "le temps", "le temps", "le temps", "les critères", "les critères", "le temps", "le temps", "les critères"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 157, "end": 159}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_159", "text": "<PERSON><PERSON>, c'est ça. Pour heu... de la réflexion. Exactement.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la réflexion", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça", "la réflexion", "C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", "la réflexion", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 158, "end": 161}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_160", "text": "Et si dans l'année t'as fait aucune réunion, ou personne n'est parti en formation, que t'es resté à la production pendant heu... bah à mon avis, c'est un des critères qui... libération du temps. Temps de repos.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["mon avis", "mon avis", "mon avis", "mon avis", "des critères", "des critères", "mon avis", "mon avis", "des critères", "aucune ré<PERSON>", "la production", "mon avis"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 159, "end": 161}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_161", "text": ". <PERSON><PERSON>, a<PERSON><PERSON><PERSON>, y'en a beaucoup qui sont revenus en arrière aussi. Oui. Bah oui, mais parce que la société heu... a pas changé.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["la société heu", "la société"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 160, "end": 165}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_162", "text": "<PERSON><PERSON> di<PERSON>, après... je pense pas, y'a pas que la société, hein, y'a aussi après l'individu, quand y'avait un certain confort, un certain... changer, apr<PERSON>, sans le confinement, c'est pas aussi bien que ce qu'ils croyaient, parce qu'y'avait une espèce d'idéalisation aussi. Bien sûr.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["Bah disons", "la société", "un certain confort", ", un certain", "le confinement", "une espèce"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 161, "end": 163}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_163", "text": "Et c'est ce qui fait avancer aussi, c'est ce qui permet de motiver, de... C'est ça, l'utopie. Et puis des fois, ça marche, et puis des fois, ça marche pas. C'est ça, l'utopie, en fait, si on la pense pas, elle risque pas d'arriver, hein, ça, on en est sûr.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le risque", "C'est ça", "le risque", ", elle", ", en fait", "C'est ça", "<PERSON> fois", "C'est ça", "C'est ça", "C'est ça", "des fois", "C'est ça", "C'est ça", "puis des fois", "en fait", "C'est ça", "en fait", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 162, "end": 165}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_164", "text": "<PERSON><PERSON> v<PERSON>, ça peut être une utopie heu... qui reste utopique, et puis heu... y'a des choses qui se concrétisent aussi. Critère de performance utopiste.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des choses", "des choses", "des choses", "des choses", "des choses", "des choses", "des choses", "des choses", "des choses"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 163, "end": 165}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_165", "text": "<PERSON><PERSON>, al<PERSON> ap<PERSON><PERSON>, il pourrait. Il faisait pas partie de l'économie sociale et solidaire, mais en fait, dans les principes, il était heu... il faisait partie de l'ESS.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 28, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["économie sociale et solidaire", "l'économie sociale et solidaire", "l'économie sociale et solidaire", "l'économie sociale et solidaire", "l'économie sociale et solidaire", "en fait", "les principes", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 164, "end": 166}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_166", "text": "Et comme c'est plus visible, c'est... en fait, tout le monde est... y'a une sorte de garantie collective, quoi. Comment ils vont faire, demain ? Je sais pas. En tout cas, j'ai pas envie de vieillir de... demain, moi. Nous, on sera à la retraite en 2050. C'est pour ça.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["tout le monde", ", moi", ", moi", ", moi", "tout le monde", "tout cas", "tout cas", "tout le monde", "le monde", "tout cas", "le monde", "tout cas", "tout cas", ", moi", ", moi", "tout le monde", "tout le monde", "tout le monde", "<PERSON>ut le monde", "<PERSON>ut le monde", "le monde", "tout cas", "tout le monde", "tout le monde", "tout cas", "tout cas", "<PERSON>ut le monde", "tout le monde", ", moi", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "en fait", "tout le monde", "une sorte", "garantie collective", "tout cas", "demain, moi", "la retraite", "tout cas", "la retraite", "la retraite", "la retraite", "tout cas", "la retraite", "Une sorte", "la retraite", "la retraite", "tout cas", "le monde", "tout cas", "tout cas", "le monde", "le monde", "le monde", "en fait"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 165, "end": 171}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_167", "text": "<PERSON><PERSON>, la retraite. <PERSON><PERSON>, je serai en retraite en 2050, je pense. <PERSON><PERSON> , je pense que je serai retraitée. C'est quoi, dans 30 ans ? 30 ans, oui.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["30 ans", "30 ans", "la retraite", "Oui, la retraite", "C'est quoi, dans 30 ans", "30 ans", "30 ans", "la retraite", "la retraite", "la retraite", "la retraite", "la retraite", "la retraite"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 166, "end": 172}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_168", "text": "<PERSON>h moi, on me prévoit une retraite à 75 ans, donc heu... il me restera cinq ans à travailler en 2050. Ah bon ? Oui, oui.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une retraite", "75 ans", "cinq ans"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 167, "end": 170}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_169", "text": "<PERSON><PERSON> moi, <PERSON><PERSON><PERSON><PERSON>, 67 ans, heu... J'ai été malade, donc heu... quand t'as été malade... C'est pas compté, tes... ? <PERSON>h non. <PERSON><PERSON>, mais c'est... pour l'instant, aujourd'hui, c'est plafonné à 67 ans.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON><PERSON><PERSON>, 67 ans", "67 ans", "... j'"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 168, "end": 172}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_170", "text": "<PERSON><PERSON>, avec les... d'aujourd'hui, mais... <PERSON><PERSON>, et puis comme ça, dans 20 ans, y'aura plus de retraite. <PERSON><PERSON> moi, en tout cas, je... ma retraite plutôt dans : j'aurai un jardin, heu... une vache et heu...", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 36, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", en tout cas", "20 ans", "20 ans", "tout cas", "tout cas", "tout cas", "enfin moi", "tout cas", "tout cas", "tout cas", "comme ça", "Comme ça", "comme ça", "comme ça", "comme ça", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "et puis comme ça", "20 ans", "<PERSON><PERSON> moi", "tout cas", "un jardin", "... une vache", "tout cas", "tout cas", "comme ça", "tout cas", "tout cas", "<PERSON><PERSON> moi"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 169, "end": 171}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_171", "text": "Bah je sais pas ce qu'ils auraient fait différemment, mais ils auraient... ils m'ont dit... ils m'ont fait cette réflexion « on aurait fait les choses différemment ». Bah c'est sûr, quand... en mettant le... le drapeau rouge de dire « ça n'existera peut-être plus », les gens se mettent à travailler comme des fous, en fait.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les gens", "les gens", "les gens", "les gens", "Les gens", "les gens", "les gens", "les gens", "les gens", ", en fait", "les gens", "les gens", "Les gens", "les gens", "les gens", "les choses", "Les gens", "en fait", "les choses", "cette réflexion", "les choses", "le drapeau rouge", "les gens", "des fous", "les choses", "les gens", "en fait", "les choses"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 170, "end": 172}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_172", "text": "<PERSON><PERSON>, c'est pas évident. C'est vrai que c'est pas évident.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 171, "end": 173}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_173", "text": "Ça va être plutôt là-dedans. On va changer de... rentabilité, capacité à générer des bénéfices, retour sur capitaux propres.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des bénéfices", "... rentabilité", ", capacité", "des bénéfices", ", retour", "capitaux propres"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 172, "end": 174}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_174", "text": "C'est... on parle de... voilà, les critères de performance, ils sont sur la rentabilité, ils sont sur le coût opérationnel, sur le côté innovation technologique. Ah bah oui, c'est encore là-dedans.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 3.0, "legitimacy_density": 0.0, "performance_indicators": 3, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.6, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le <PERSON>", "le <PERSON><PERSON>", "le <PERSON><PERSON>", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "les critères", "le côté innovation", "les critères", "le <PERSON><PERSON>", "les critères", "le coût", "les critères", "la rentabilité", "le coût opérationnel", "le côté innovation technologique", "les critères"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 173, "end": 175}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_175", "text": "Il a le temps, encore. Il a le temps encore, oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le temps", "le temps", "le temps", "le temps", "le temps", "le temps", "le temps", "le temps", "le temps", "le temps"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 174, "end": 176}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_176", "text": "Peut-être qu'on reviendra aux fondamentaux. Comme on nous l'annonce, ça a pas l'air d'être heu... en plus, déplacements des... des peuples un peu de partout, ça va être compliqué aussi à... peut-être, sur des territoires en surpopulation. On vit dans des conditions peut-être plus difficiles. Oui.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["plus, déplacements des", "... des peuples", "des conditions peut-être plus difficiles"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 175, "end": 179}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_G_seg_177", "text": "… extrême droite Y'en a un peu de partout, c'est en train de... Donc comme tout à l'heure, vous me laissez les cartes que vous avez utilisées sur votre support.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les cartes", "Extrême droite", "extrême droite Y'", "les cartes", "votre support"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 176, "end": 178}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_178", "text": "Et puis c'est pas en Argentine, aussi, la semaine dernière ? Si, en Argentine, c'est ce que j'allais dire.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["La semaine dernière", "aussi, la semaine derni<PERSON>"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 177, "end": 179}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_179", "text": "Ça fait deux coup sur coup. Surtout l'autre avec sa tronçonneuse.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["deux coup", "Surtout l'autre", "sa tronçonneuse"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 178, "end": 180}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_180", "text": "<PERSON><PERSON>, c'est des barjots, quoi. Ils font vraiment flipper.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 179, "end": 181}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_181", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, oui. <PERSON>s al<PERSON> quand tu vois celui d'Argentine... Argentine, oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 180, "end": 182}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_182", "text": "Regarder celui des Pays-Bas, mais rien que quand tu vois sa tête, tu dis... non, mais tu sais, c'est le mec, tu le croises dans la rue, il fait déjà louche. T'as pas du tout envie de lui donner les manettes du pouvoir, quoi.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON> rien", ", tu", "sa tête", "la rue", "tout envie", "les manettes"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 181, "end": 183}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_183", "text": "Il essaie, hein. Et en plus, on est résistants au changement. Oui. C'est vrai que c'est facile de débattre à un niveau conceptuel, de se dire... mais demain, concrètement... <PERSON><PERSON>, ça se fera de manière heu... graduelle. En fait, c'est ça.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 41, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", ", c'est ça", "C'est ça", "C'est ça", "de dé", ", c'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "en fait", "C'est ça", "un niveau conceptuel", "manière heu", "fait, c'est ça", "en fait", "C'est ça", "manière heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 182, "end": 187}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_184", "text": "Il faut pas... il faut pas se dire « je vais sauver le monde ». Oui.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le monde", "le monde", "le monde", "le monde", "le monde", "le monde", "le monde"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 183, "end": 185}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_185", "text": "Et même l'entreprise, elle va... elle va très bien s'adapter, l'entreprise. <PERSON><PERSON>, c'est exactement la même chose. <PERSON> s'adapte toujours, l'entreprise. Ils savent toujours faire. C'est impressionnant.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": [", elle", "<PERSON><PERSON>, c'est exactement la même chose"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 184, "end": 189}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_G_seg_186", "text": "<PERSON><PERSON> moi, je suis toujours impressionné quand je... je vois la capacité des dirigeants à se... s'adapter, ou même les salariés. Mais ça se fera, tout se fera. C'est ça. Mai<PERSON> ton m<PERSON>, il va évoluer de manière heu... graduelle. C'est ça. Y'aura des gens qui vont faire du coaching pour que heu...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des gens", "C'est ça", "des gens", "des gens", "mais ça", "des gens", "mais ça", "enfin moi", "des gens", "C'est ça", "des gens", "des gens", "des gens", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "C'est ça", "<PERSON><PERSON> moi", "des gens", "mais ça", "manière heu", "<PERSON><PERSON> moi", "la capacité", "C'est ça", "ton métier", "manière heu", "des gens", "que heu"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 185, "end": 191}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_187", "text": "ça soit plus fluide, tu vas avoir un... justement, tu vois, finalement, le cabinet que tu as monté, il... c'est une forme de transformation, parce que si les gens, ils sont pas bien, qu'ils sont angoissés et qu'ils font pas bien leur job, donc on va prendre en compte ce... ce genre de problématiques, donc on va changer. Oui, oui, non, non, tu vois, c'est vraiment de me dire : comment déjà être innovant dans le quotidien du travail.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 79, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le quotidien", "les gens", "les gens", "les gens", "les gens", "Les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "Les gens", "les gens", "les gens", "Les gens", "du travail", ", tu", "les gens", "le quotidien", "les gens", ", le cabinet", "parce que si les gens", "leur job", "ce genre", "le quotidien"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 186, "end": 188}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_188", "text": "Date : 24/12/2023 Nom du fichier : « G1 »", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["« G1"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 187, "end": 189}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_G_seg_189", "text": "Commanditaire : <PERSON> : 89 minutes Remarques particulières : en italique les modératrices du groupe global. Plusieurs time codes.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["<PERSON>", "89 minutes", "les modératrices", "groupe global", "Plusieurs time codes"]}, "metadata": {"source": "data_renamed\\Table_G.docx", "segment_lines": 1, "position": {"start": 188, "end": 190}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}]}