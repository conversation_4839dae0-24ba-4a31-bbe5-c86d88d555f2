{"source_file": "data_renamed\\Table_B.docx", "processed_timestamp": "2025-06-12T14:01:43.153066", "ml_target_format": "data_json_compatible", "segments": [{"id": "Table_B_seg_001", "text": "Début de la retranscription : Travail collectif 1, matinée. Et ça, on va se laisser un quart d'heure.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la retranscription", "Travail collectif", "un quart", "travail collectif", "un quart", "un quart"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 0, "end": 3}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_002", "text": "Pendant ce temps-là, moi je vais prendre en photo chacun de vos heu... petits documents, OK ? Donc je passerai prendre en photo et vous faites comme si je n'existe pas, vous discutez entre vous.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce temps", "vos heu", "... petits documents", ", vous", "ce temps", ", moi"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 1, "end": 3}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_003", "text": "Et ensuite, sur la base de 2050 et donc notamment du scénario que vous a présenté <PERSON>, vous avez normalement la feuille, vous allez remplir dans un premier temps, mais je vous dirai, je vous demande au départ la partie légitimité en 2050, et puis ensuite... donc là, vous aurez à peu près 20 minutes, une demi-heure, et, de la même façon sur la partie critères de performance. OK ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la base", "donc notamment du scénario", "la feuille", "un premier temps", "la partie", "peu près 20 minutes", "une demi-heure", "la partie", "la base", "la partie", "une demi-heure", ", je", "la feuille", ", vous", "la partie", "la même façon", "la feuille"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 2, "end": 4}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_004", "text": "Et donc ça par groupe et vous avez compris, je remets des petits codes couleurs là -dessus", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des petits codes couleurs", ", je"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 3, "end": 6}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_005", "text": "pour voir comment évolue au fur et à mesure... donc là, par contre, ce que je vous demande vraiment pour heu... pour ce premier temps, pendant que je prends en photo, c'est vraiment de parler de 2023, donc essayez de pas tout de suite projeter sur 2050, mais d<PERSON>, v<PERSON>à, apprendre à vous connaître, discuter de ça et je vous dis quand on passe à ce... à ce moment-là .", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce premier temps", "ce moment", "tout de suite", ", ce", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 4, "end": 6}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_006", "text": "Ça a été lancé, normalement. Il me semble que... il me semble qu'elle l'avait déjà fait, oui. Alors j'ai la... j'ai l'honneur, c'est ça, de commencer ? Voilà. Bah", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "C'est ça", "C'est ça", ", c'est ça", ", c'est ça", ", c'", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 5, "end": 10}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_007", "text": "ce que je vous propose, c'est qu'on fasse heu... les critères, et puis... <PERSON><PERSON>, déj<PERSON> les critères, allez. Alors pour moi, une bonne boî<PERSON>, bon pas forcément dans l'ordre, hein, mais ce qui me venait à l'esprit : innovante, qualitative, c'est-à-dire qui produit de la qualité, heu... RSE. C'est quoi, la... qui produit de la qualité dans quel sens ? Heu... bah qui produit des produits de qualité, produits ou services de qualité. Surtout dans la... dans une optique de durée, de... de durabilité des produits. OK, ah oui, d'accord. OK, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 92, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["déjà les critères", "Alors pour moi", "la qualité", "C'est quoi, la... qui produit de la qualité dans quel sens ?", "des produits", ", produits", "Surtout dans la... dans une optique", "... de durabilité", ", d'accord", "pour moi", "la qualité", "les critères", "les critères", ", d'accord", ", hein", "C'est quoi", ", hein", "une bonne boîte", "les critères", "les critères", "des produits", "des produits", ", c'", "Ah oui", ", hein"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 6, "end": 13}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_008", "text": "RSE, enfin <PERSON>, je sais que c'est pas une heu... qualité, mais heu... engagée, responsable, responsabilité sociale et environnementale. J'ai mis « rentable » aussi, quand même. Heu... et égalitaire. À vous le tour ? Tu veux qu'on passe à nous aussi ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 3.0, "performance_indicators": 0, "legitimacy_indicators": 3}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.6, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le tour", ", je", "une heu", "une heu", "une heu"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 7, "end": 12}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_009", "text": "<PERSON><PERSON>, je pense qu'on fait d'abord les critères, et puis ensuite, on verra chaque boîte. OK, ça marche, oui. Je t'en prie.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["abord les critères", "chaque bo<PERSON>te", "les critères", "les critères", ", je", "les critères", "les critères", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 8, "end": 11}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_010", "text": "<PERSON><PERSON>, eh bien moi j'avais mis : donc qui se préoccupe du bien-être des salariés, qui a un impact social et sociétal, donc là, j'étais plutôt interne et puis tourné vers l'extérieur ensuite, qui limite ses déplacements, dans le sens où je trouvais qu'y'avait cert... beaucoup d'entreprises qui... où on se déplace pour pas grand-chose, où on pourrait faire différemment. Donc finalement, y'a beaucoup de choses comme bah la RSE que tu évoquais. Heu... qui n'a pas une activité directe de destruction de la nature, de l'environnement, et rentable. OK, bah moi aussi, je pense qu'y'a un certain nombre de choses qui se... qui se répètent.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 106, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.9433962264150942, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.7547169811320755}, "noun_phrases": ["eh bien moi", "un impact social et sociétal", "ses déplacements", "le sens", "pas grand-chose", "bah la RSE", "une activité directe", "la nature", ", de l'environnement", "un certain nombre", "un impact", "un impact", "moi j'", "un certain nombre", "la nature", "un certain nombre", ", je", "un certain nombre", "le sens", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "le sens", "un certain nombre", "un certain nombre", "le sens", "le sens", "le sens", ", de l'environnement", "le sens", "un certain nombre", "dans le sens", "le sens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 9, "end": 13}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_011", "text": "Donc pas... pas dans l'ordre également, donc attractive, heu... dans le sens où ses produits et ses services donnent envie. Productive, heu ... j'ai également mis la notion de rentabilité.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le sens", "Donc pas... pas dans l'ordre", "... dans le sens", "ses produits", "ses services", "la notion", "la notion", "la notion", "le sens", "le sens", "le sens", "le sens", "le sens", "ses produits", "le sens", "dans le sens", "le sens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 10, "end": 13}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_012", "text": "<PERSON><PERSON> enfin, j'ai terminé par les notions un peu plus heu... so... sociétales, si on peut dire. Heu... qui a une politique environnementale juste et heu... qui est bienveillante vis-à-vis de son personnel.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["les notions un peu plus heu", "une politique environnementale"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 11, "end": 13}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_013", "text": "Donc en gardant à l'esprit que ça reste quand même une boîte. Le but, c'est quand même de faire de l'argent, mais si en faisant de l'argent on peut avoir ces qualités-là, voilà.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["quand même une boîte", "Le but", "ces qualités", "une boîte", "une boîte", "une boîte", "une boîte", "que ça", "le but", "une boîte", ", c'", "une boîte", "une boîte"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 12, "end": 14}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_014", "text": "services, alors déjà utiles à la société, parce qu'une entreprise qui produit heu... qui produit pour produire, je pense qu'y'en a beaucoup aussi, qui pour moi est pas légitime heu... voilà. Durable dans le temps, encore une fois, parce que je pense que ça rejoint le... la responsabilité environnementale, c'est-à-dire que si on a un produit de qualité qui dure dans le temps, on le remplace moins, il tombe moins en panne, etc. Avec un bilan carbone heu... faible. Un bilan carbone et un impact environnemental faible, voire nul.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 89, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 2.0, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "la société", "le temps", "encore une fois", "le... la responsabilité environnementale", "un produit", "le temps", "un bilan carbone", "Un bilan carbone", "un impact", "pour moi", "un impact", "une entreprise", "la société", ", je", "une entreprise", "que ça", "encore une fois", "une entreprise", "la société", "une entreprise", "le temps", "une entreprise", "une entreprise", "le temps", "une entreprise", ", c'", "une entreprise", "une entreprise", "encore une fois", "la société", "la société", "le temps", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 13, "end": 18}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_015", "text": "<PERSON><PERSON> dernier : qui prend soin de ses salariés. Heu... donc j'avais mis : qui réfléchit aux impacts de ses missions en 360 avant de prendre des décisions, donc v<PERSON>, vraiment, la mission, comment elle impacte différentes thématiques, différents sujets.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ses salariés", "ses missions", "des décisions", "différentes thématiques", "des décisions", "la mission", "la mission", "La mission", "ses salariés", "ses salariés"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 14, "end": 16}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_016", "text": "Je partais du principe qu'on ne peut pas attendre d'une boîte privée qu'elle puisse être légitime. Sinon, finalement, c'est tout le... comment dire ? Tout la liberté du commerce et de l'industrie qui est remise en cause, en fait.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 2, "side_b": 0, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["une boîte", "une boîte privée", "<PERSON><PERSON>, finalement, c'est tout le", "Tout la liberté", "de l'industrie", "une boîte", "une boîte", ", en fait", "une boîte", "en fait", ", en fait", "une boîte", ", c'", "une boîte", "une boîte", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 15, "end": 18}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_017", "text": "Donc au<PERSON>'hui, il doit exister des règles pour dire à une société privée « vous avez le droit de faire de l'argent, mais dans la limite de certaines règles », pour par exemple respecter l'environnement, pour respecter le salarié. Donc ça va un peu dans le sens de ce que tu as évoqué, le harcèlement, tout ce... ce genre de choses.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 1, "tension_strength": 0, "total_indicators": 2}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["le sens", "des règles", "une société privée", "le droit", "<PERSON>es r<PERSON>", "par exemple", "le salarié", "un peu dans le sens", "le harc<PERSON><PERSON>", "tout ce", "ce genre", "... ce", "une société", "des règles", "le sens", "tout ce", "une société", "le droit", "le sens", "ce genre", "le salarié", "ce genre", "... ce", "le sens", "le sens", "le sens", "le sens", "le droit", "tout ce", "dans le sens", "le sens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 16, "end": 18}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_018", "text": "<PERSON>u... <PERSON><PERSON><PERSON>, quand même, en étant réaliste, un bilan financier positif. Je reviens sur la qualité, après, en disant : taux de défaut, de panne bas. <PERSON><PERSON><PERSON>, apr<PERSON>, c'est vrai que ça fait répétition un peu quand même, c'est des critères pour évaluer le... la légitimité et les premières qualités, mais un faible taux de turn-over, de maladies, de burn-out.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 60, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la qualité", "la qualité", "panne bas", "le... la légitimité", "les premières qualités", "un faible taux", ", de maladies", "C'est des critères", "la légitimité", "La légi", "la légitimité", "la légitimité", "que ça", "la légitimité", "des critères", "la légitimité", "la légitimité", "la légitimité", "des critères", ", c'", "des critères", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 17, "end": 21}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_019", "text": "C'est des critères en fait qui permettraient de mesurer les... les qualités d'avant, enfin c'était avant. Donc moi, sur les critères de performance, j'avais mis heu... les critères de performance qui vont être plutôt sociaux, donc mesurer son impact social, qui me semblait être important et heu... ça rejoint un peu ce qu'on disait tout à l'heure sur la... la légitimité, mais de... de faire des choses dont on a besoin. Heu... voilà.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 73, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 2.0, "performance_indicators": 2, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est des critères", "les... les qualités", "c'était avant", "<PERSON><PERSON> moi", "les critères", "les critères", "son impact social", "un peu ce", "la... la légitimité", "des choses", "des choses", "son impact social", "un peu ce", "la légitimité", "des choses", "des choses", "La légi", "la légitimité", "la légitimité", "la légitimité", "des choses", "des critères", "un peu ce", "en fait", "la légitimité", "donc moi", "la légitimité", "la légitimité", "les critères", "des choses", "les critères", "des critères", "un peu ce", "des choses", "des choses", "tout à l'heure", "en fait", "des critères", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 18, "end": 22}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_020", "text": "Heu... environnementaux, donc en gros, quelles sont les actions qui peuvent être évitées. Et si y'en a que je peux pas éviter, mais qui ont un impact fort, bah est-ce que je peux faire des actions réparatrices, par exemple, sur d'autres éléments, « compensatoires » peut-être plus que réparatrices, d'ailleurs.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un impact", "par exemple", "les actions", "un impact", "des actions réparatrices", "d'autres éléments, « compensatoires » peut-être plus que réparatrices, d'ailleurs", "des actions", "des actions"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 19, "end": 21}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_021", "text": "Et puis économique, importance de la rentabilité, plus de la valorisation aussi des salariés. C'est comment on peut aussi valoriser heu... le travail de... de nos salariés. On se dit encore 5-10 minutes, si ça vous va ? O<PERSON>, très bien. Tout bien, oui. <PERSON><PERSON>, je comprends que... quand elle a posé la question de la sensibilité tout à l'heure, j'étais plus au milieu, voire derrière par rapport à vous. Et là, je me rends compte de la question, parce que vraiment, si on doit se placer en 2023 et heu... quand on parle de performance, pour moi, et de réalisme, j'ai pas le sentiment que les... les boîtes, aujourd'hui, elles sont à fond dans l'environnement, tout ça.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 118, "thematic_indicators": {"performance_density": 1.6949152542372883, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.6779661016949153}, "noun_phrases": ["la question", "pour moi", "la rentabilité", ", plus de la valorisation", "aussi des salariés", "le travail", "... de nos salariés", "5-10 minutes", "la question", "la sensibilité", "voire derrière par rapport", "la question", "le sentiment", "que les... les boîtes", ", tout ça", "le sentiment", "la question", "la question", "la question", ", je", "tout ça", ", tout ça", "la question", "la question", "les boîtes", "La question", "la question", "la question", "La question", ", tout ça", "la question", "la question", "tout ça", ", tout ça", "tout ça", "tout ça", "la question", "le sentiment", "le sentiment", "tout à l'heure"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 20, "end": 27}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_022", "text": "Donc comment je perçois aujourd'hui une boîte qui est performante, donc une boîte qui touche le plus grand nombre, le public cible, voire un peu plus. Heu... une boîte qui répond aux attentes du plus grand nombre. Et le cas échéant, qui va rectifier le tir sans avoir peur de la contre-performance. Dans plusieurs domaines différents, hein, mais heu... une boîte qui n'a pas peur d'avouer ses erreurs, de se dire « on a fait une erreur, là, on rectifie le tir ».", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 83, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une boîte", "une boîte", "une boîte", "le plus grand nombre", "le public", "Heu... une boîte", "plus grand nombre", "le tir", "la contre-performance", "plusieurs domaines différents", "heu... une boîte", "ses erreurs", "une erreur", "le tir", ", hein", "une boîte", "le cas", "le cas", ", hein", "... un", "... une boîte", "une boîte", "une boîte", "une boîte", ", hein"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 21, "end": 25}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_023", "text": "<PERSON>vide<PERSON><PERSON>, ça se traduit également par le... par le chiffre d'affaires, et heu... enfin, j'ai noté : un partage équilibré des bénéfices entre les dirigeants et les employés, parce qu'aujourd'hui, on voit des boîtes qui sont très performantes, où le sala... le... l'employeur va gagner heu... 100, 2 fois, 300 fois plus qu'un salarié lambda et où y'a pas un partage équilibré. Donc celui qui est tout en bas, il reste pauvre, il évolue jamais, alors que... voilà.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 78, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["le chiffre", "un partage équilibré", "les dirigeants", "les employés", "des bo<PERSON>", "2 fois", "300 fois", "qu'un salarié lambda", "un partage équilibré", "le chiffre", "des bo<PERSON>", "un salarié", "les dirigeants", "des bénéfices", "des bo<PERSON>", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 22, "end": 24}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_024", "text": "<PERSON><PERSON>, j'ai le senti... Au niveau... sur l'environnement ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["... sur l'environnement"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 23, "end": 25}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_025", "text": "Sur le côté écolo, environnement, oui ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 7, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le côté écolo", ", environnement"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 24, "end": 26}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_026", "text": "<PERSON><PERSON>, j'ai... enfin oui, j'ai le sentiment que vous êtes un peu le niveau au-dessus, quand même, parce que y'a des choses qui me touchent, mais apr<PERSON>, je... objectivement, je regarde ce que je fais au quotidien pour savoir si heu... si je suis le... comment dire ? Cette sensibilité. Et heu... et aujourd'hui, peut-être que ça me choque pas tant que ça, quand une boîte me dit qu'elle veut toutes ces choses-là pour être performante et que y'a pas la notion de... d'environnement là-dedans. L'environnement, oui.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 87, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la notion", "une boîte", "des choses", "le sentiment", "une boîte", "une boîte", "le sentiment", "des choses", "<PERSON>tte sensibilité", "une boîte me", "toutes ces choses", "la notion", "... d'environnement", "cette sensibilité", "des choses", "des choses", "la notion", ", je", "une boîte", "que ça", "des choses", "des choses", "une boîte", "une boîte", "des choses", "le sentiment", "une boîte", "le sentiment", "des choses"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 25, "end": 30}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_027", "text": "Enfin vous voyez, par rapport à vos réponses et aux miennes, je me dis que vous devez quand même être un peu plus sensible. <PERSON><PERSON>, alors moi, c'est... moi oui, oui, je suis complètement sensible et anxieuse aussi, donc moi j'étais vraiment dans le coin en haut à gauche.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON> moi", "<PERSON><PERSON> moi", "moi j'", "vos réponses", "aux miennes", "<PERSON><PERSON>, alors moi", "le coin", "haut à gauche", "le co", ", je", ", c'est...", "donc moi", "le coin", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 26, "end": 28}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_028", "text": "Le fait aussi que heu... je gravite pas mal dans le milieu de l'économie sociale et solidaire, heu... donc heu... et... et dans mes cours, je fais intervenir aussi heu... des organismes de l'ESS pour que par exemple les étudiants qui ont des projets entrepreneuriaux réfléchissent heu... bah par exemple à leur impact social et à comment on mesure son impact social.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 62, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["par exemple", "son impact social", "pas mal", "Le fait", "le milieu", "l'économie sociale et solidaire", "et dans mes cours", "des organismes", "des projets entrepreneuriaux", "son impact social", "leur impact", ", je", "le fait", "l'économie sociale et solidaire", "l'économie sociale et solidaire", "le fait", "des organismes", "le fait", "le fait", "le fait", "les étudiants", "les étudiants", "le fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 27, "end": 29}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_029", "text": "Donc en effet, enfin à la fois dans ma vie personnelle et aussi professionnelle, j'essaie d'avoir ce... ce regard-là, oui. Et puis aussi, bah dans le labo de recherche, heu... bah déj<PERSON> on organise cette journée, donc pas forcément tous les collègues sont sensibles à ça, mais je suis aussi entourée de collègues qui... qui ont cette sensibilité. Oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 59, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["<PERSON>tte sensibilité", "enfin à la fois", "ma vie personnelle et aussi professionnelle", "ce regard", "Et puis aussi, bah dans le labo", "cette journée", "tous les collègues", "cette sensibilité", "en effet", "... ce", "les collègues", "... ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 28, "end": 31}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_030", "text": "<PERSON><PERSON>, tu vois, j'ai quand même l'impression que tu as le co... enfin tu as le pragmatisme, tu vois, tu dis quand même qu'il faut être rentable aussi derrière, tu peux être autant ...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le co", "le pragmatisme"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 29, "end": 31}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_031", "text": "À peu près, oui. <PERSON><PERSON>, mais de toute manière, enfin c'est heu... c'est le problème souvent quand on parle de l'ESS, c'est qu'on oublie que dans « ESS », y'a « économie » et de toute manière, enfin il faut bien... la seule différence qu'on peut voir, c'est que l'argent devient un moyen et pas une finalité.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["mais de toute manière", "c'est heu", "« ESS", "y'a « économie", "de toute manière", "la seule différence", "un moyen", "un moyen", "le problème", "le problème", "le problème", "le problème", "le problème", ", c'", "toute manière", "Le problème", "le problème"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 30, "end": 32}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_032", "text": "Et donc c'est... c'est aussi ça, la différence. Et ça veut pas dire qu'il faut pas des rentrées d'argent, qu'il faut pas être rentable, parce que de toute manière, si on veut avoir une mission, qu'elle soit sociale, sociétale, environnementale, il va bien falloir un moyen de le faire.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 2.0, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": [", environnement", "de toute manière", "un moyen", ", la différence", "des rentrées", "parce que de toute manière", "une mission", "un moyen", "une mission", "toute manière", "une mission"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 31, "end": 33}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_033", "text": "<PERSON><PERSON> <PERSON>, heu... enfin je vois pas comment faire autrement.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 32, "end": 34}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_034", "text": "Donc heu... oui, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 4, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 33, "end": 35}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_035", "text": "Après, la question de l'argent, elle heu... elle peut se questionner après différemment sur un peu ce que tu évoquais tout à l'heure sur la légitimité, de... tu vois, typiquement, des associations, bah est-ce qu'on peut se dire que c'est une très bonne chose qu'à des moments, elles aident tel type de population et qu'elles se démènent pour y arriver, alors que finalement, elles heu... elles pallient heu... bah du coup des... des actions que devrait faire à des moments le... le gouvernement et qu'elles ont pas les moyens. Enfin tu vois, après, c'est... c'est une autre histoire de positionnement, mais du coup, on peut se poser des questions économiques sur heu... le modèle économique, heu... ou tu vois, ou des assos aussi, à des moments, ont refusé de demander des subventions parce que bah si demain y'en a plus, elles ne pourront plus fonctionner, donc elles préfèrent s'en priver pour se dire qu'elles ont un modèle en fait autonome, où on peut se dire : bah du coup, c'est ch... on peut se dire que ça peut être chouette, parce que c'est... au moins, elles... elles ne comptent pas là-dessus, etc., sauf que tu peux te dire aussi « bah oui, mais en fait, à la base, le gouvernement, il doit déjà le faire, là, il te donne un peu d'argent et tu... », enfin donc voilà, après, c'est une question plus complexe, mais voilà, pour parler un peu de tout ce qui est lié à l'argent, oui, ça, c'est une question qu'est pas si évidente que ça. Mais en effet, faut... faut pas avoir peur de l'aborder, quoi. Oui, c'est ça. Après, oui, je pense que c'est du pragmatisme, quoi, de se dire heu... Oui, complètement.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 288, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.3472222222222222, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.1388888888888889}, "noun_phrases": ["la base", "<PERSON><PERSON>, c'est ça", "la question", "tout ce", "un peu ce", "la question", "la question", "la question", "elle heu", "un peu ce", "la légitimité", "typiquement, des associations", "qu'à des moments", "tel type", "elles heu", "des actions", "des moments", "le... le gouvernement", "les moyens", "du coup", "des questions économiques", "le modèle économique", "des subventions", "un modèle", "fait autonome", "la base", "le gouvernement", "<PERSON><PERSON>, c'est ça", "la question", "la question", "<PERSON><PERSON>, c'est ça", "en effet", "La légi", "la légitimité", "la légitimité", ", je", "le modèle", "C'est ça", "mais du coup", "<PERSON><PERSON>, c'est ça", "C'est ça", "que ça", "<PERSON><PERSON>, c'est ça", "la légitimité", "la question", "la question", "du coup", "les moyens", "du coup", "La question", "la question", "la question", ", c'est...", "du coup", "C'est ça", "tout ce", "La question", "un peu ce", "en fait", "du coup", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "la légitimité", "des actions", "la question", "mais du coup", "la question", "<PERSON><PERSON>, c'est ça", "la légitimité", "la légitimité", ", c'est ça", "des questions", "oui, c'est ça", "<PERSON><PERSON> du <PERSON>", "<PERSON><PERSON>, c'est ça", ", c'est ça", ", c'", "un peu ce", "<PERSON><PERSON>, c'est ça", "les heu", "du coup", "la question", "Ah oui", "les moyens", "les moyens", "les moyens", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça", "tout à l'heure", "en fait", "<PERSON><PERSON> du <PERSON>", "tout ce", "les heu", "<PERSON><PERSON> du <PERSON>", ", ça", "la légitimité", "mais du coup"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 34, "end": 40}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_036", "text": "Et puis c'est... bah c'est à long terme aussi, tu vois, c'est gagnant-gagnant, je veux dire, si t'as une entreprise qui arrive à être rentable en prenant en compte tous ces critères, c'est gagnant aussi pour la société, au final, quoi. Complètement. O<PERSON>, la question est de savoir : aujourd'hui, en tout cas, c'est pas le cas aujourd'hui, heu... c'est que l'entreprise privée, aujourd'hui, elle n'a pas d'obligation à avoir cet aspect humaniste. Oui, complètement. Ah bah oui. Non, pas du tout. Si... si c'était fixé dans la loi, de se dire que heu... pour exister, vous avez besoin de respecter telle ou telle chose, aujourd'hui, les règles environnementales, maintenant, les sociétés, si elles respectent pas... elles se font pas taper sur les doigts. Y'a pas grand-chose derrière.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 128, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.78125, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 1, "tension_strength": 0, "total_indicators": 2}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": ["individuel_collectif", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["pas grand-chose", "une entreprise", "la société", "tout cas", "la question", "la question", "la question", "la question", "une entreprise", "tous ces critères", "la société", "la question", "tout cas", "cet aspect humaniste", "la loi", "telle chose", "les règles environnementales", "les sociétés", "les doigts", "Y'a pas grand-chose", "la question", "la loi", "tout cas", "la loi", "la loi", ", je", "tout cas", "tout cas", "tout cas", "la loi", "la loi", "une entreprise", "la question", "la question", "La question", ", vous", "la question", "la question", "une entreprise", "la société", "le cas", "en tout cas", "le cas", "en tout cas", "tout cas", "La question", "tout cas", "une entreprise", "la question", "la question", "une entreprise", "une entreprise", "une entreprise", "tout cas", ", c'", "tout cas", "une entreprise", "cet aspect", "la question", "une entreprise", "Ah oui", "ces critères", "au final", ", au final", "la société", ", au final", "la société", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 35, "end": 43}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_037", "text": "Donc la question est de savoir si ça peut être dans l'esprit de chacun heu... <PERSON><PERSON>, c'est ça aussi. Ou est-ce que c'est quelque chose qui doit être heu... obligatoire et imposé par la loi, au risque de se voir... <PERSON><PERSON>, complètement.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "la question", "la question", "la question", "la question", "<PERSON><PERSON>, c'est ça", "la question", "la loi", "la question", "<PERSON><PERSON>, c'est ça", "la loi", "la loi", "quel<PERSON> chose", "la loi", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "la loi", "la loi", "quel<PERSON> chose", "<PERSON><PERSON>, c'est ça", "la question", "la question", "La question", "la question", "la question", "C'est ça", "La question", "quel<PERSON> chose", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "quel<PERSON> chose", "la question", "la question", "<PERSON><PERSON>, c'est ça", ", c'est ça", "quel<PERSON> chose", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", "quel<PERSON> chose", ", c'", "<PERSON><PERSON>, c'est ça", "quel<PERSON> chose", "la question", "quel<PERSON> chose", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 36, "end": 40}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_038", "text": "Et on voit que celles qui se... qui se l'imposent, enfin justement, elles se l'imposent à elles-mêmes, par exemple avec l'agrément ESUS ou des choses comme ça, c'est déjà en fait des structures qui se préoccupent de leur impact, qui vont elles-mêmes se mettre la contrainte de demander l'agrément ESUS pour répartir les salaires, etc., heu... mais en effet, c'est elles qui décident, parce que déjà elles ont un engagement, de se rajouter, si je puis dire, cette contrainte. <PERSON>ur el<PERSON>, c'est certainement pas une contrainte, puisque c'est un choix, mais heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 92, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["par exemple", "des choses", "des choses", "que celles", "des choses", "des structures", "leur impact", "la contrainte", "l'agrément ESUS", "les salaires", "un engagement", "en effet", "des choses", "c'est ce", "que ce", "des choses", "Comme ça", "des structures", "en fait", "Comme ça", "comme ça", "des choses", ", ce", ", c'", "comme ça", "comme ça", "des choses", "que ce", "des choses", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 37, "end": 39}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_039", "text": "donc en effet, ça pose un certain nombre de questions. Et moi, je relierai ça sur... parce que tu vois, t'avais beaucoup de choses aussi sur l'égalité hommes-femmes, et plus largement sur tout ce qui est questions de diversité, et c'est aussi heu... puisqu'on parle d'environnement, tout ce qui va être lié en fait au système de domination, mais quel qu'il soit, même de l'être humain sur la nature, heu... et donc heu... tout ce qui peut être lié à... à l'éco-féminimisme aussi, par exemple, sur comment... oui, on met en avant tous ces systèmes de domination.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 97, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["la nature", "un certain nombre", "par exemple", "tout ce", "un certain nombre", "l'égalité hommes-femmes", "mais quel qu'il soit, même de l'être humain", "la nature", "tous ces systèmes", "en effet", "un certain nombre", ", je", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "Égalité hommes-femmes", "tout ce", "un certain nombre", "en fait", "un certain nombre", "un certain nombre", "l'être humain", "un certain nombre", "en fait", "tout ce", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 38, "end": 40}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_040", "text": "ce qui s'est dit heu ... ce que Alexis nous a raconté sur le scénario 2050 ? En petit aide ou rappel-mémoire, on a les effets sur les organisations, vous avez des feuilles comme ça, qui est la slide qu'il vous a présentée.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["... ce", "le scénario", "petit aide", "les effets", "les organisations", "des feuilles", "les effets", "les organisations", "Comme ça", ", vous", "Comme ça", "comme ça", "comme ça", "... ce", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 39, "end": 42}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_041", "text": "Et donc là, ce que je vais vous demander, c'est... ce qu'on vous demande, c'est, donc sur cette... sur ce document-là vierge, heu... de répondre à la partie cinq, donc la première case à gauche : quel sera une boîte légitime en 2050, quand on observe les effets sur les organisations que ça a produit ? Donc en considérant que y'a notamment ces effets-là, mais aussi tous ceux que vous imaginez, hein, vous... vous vous fermez pas là-dessus, finalement, une boîte légitime en 2050, qu'est-ce qu'elle est ? Peut-être qu'y'a des choses que vous avez déjà écrites en 2023, que vous allez pouvoir reverser à 2050, mais peut-être que il faudra aussi imaginer d'autres choses.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 115, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.3478260869565218}, "noun_phrases": ["la partie", "la partie", "une boîte", "des choses", "une boîte", "une boîte", "des choses", "des choses", "... ce", "les effets", "les organisations", "ce document", "la partie", "donc la première case", "les effets", "les organisations", "y'a notamment", "des choses", "d'autres choses", "une boîte légitime", "une boîte légitime", ", une boîte", ", hein", "une boîte", "que ça", "une boîte légitime", "des choses", ", vous", ", c'est...", ", hein", "la partie", "des choses", ", ce", "une boîte", ", c'", "ce document", "une boîte", "... ce", "des choses", "une boîte", ", hein", "des choses"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 40, "end": 43}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_042", "text": "La légi... enfin j'espère que c'est clair sur la notion de légitimité pour tout le monde, sinon je peux aussi heu... Clap deux, travail collectif. … donner un peu d'exemples, mais en tout cas, c'est comment on la considère légitime, donc utile, intéressante, heu... et visible, en fait. Peut-être, on peut parler de visible par l'ensemble des parties prenantes, autour de ça. Si déjà on se dit ça, heu... c'est pas la définition très exacte de la légitimité, mais c'est ce qui vous parle.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 84, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["Travail collectif", "la notion", "tout cas", "la notion", "la légitimité", "tout cas", "La légi", "la notion", "tout le monde", "travail collectif", "tout cas", "parties prenantes", "la légitimité", "c'est ce", "tout le monde", "la légitimité", "tout cas", "le monde", "tout le monde", "tout cas", ", en fait", "tout cas", "la légitimité", "parties prenantes", "parties prenantes", "tout le monde", "en tout cas", "en tout cas", "tout cas", "tout le monde", "en fait", "tout cas", "la légitimité", "le monde", ", en fait", "la légitimité", "la légitimité", "parties prenantes", "tout le monde", "le monde", "le monde", "tout cas", ", c'", "tout cas", "tout le monde", "Mais en tout cas", "tout le monde", "tout le monde", "tout le monde", "en fait", "tout le monde", "tout le monde", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 41, "end": 45}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_043", "text": "Voilà, donc on a vingt minutes, une... enfin on a une demi-heure pour faire ça. Juste qu'y'en ait un, s'il vous plaît, qui écrive sur cette feuille. N'hésitez pas à commencer à écrire, à raturer, etc., mais à écrire de façon lisible. Bon, qui c'est qui a une belle écriture ? Parce que moi, c'est pas... c'est pas ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 59, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une demi-heure", "vingt minutes", "une demi-heure", "cette feuille", "façon lisible", "c'est qui a une belle écriture", "que moi", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 42, "end": 47}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_044", "text": "Moi j'ai des lettres des fois qui sont un peu avalées, donc heu... comme vous voulez.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["moi j'", "des lettres", "des fois", "des fois", "des fois", "<PERSON> fois"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 43, "end": 45}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_045", "text": "<PERSON><PERSON>t-<PERSON><PERSON>, hein, c'est ça ? Pour le bien de l'étude, je pense qu'il est préférable d'éviter... bon, légitime en 2050 ? Heu... Bah là, si on se dit que... avec tous ces nouveaux critères, et notamment avec les conséquences qui avaient été évoquées, c'est-à-dire baisse de revenus, baisse des matières premières, je me dis qu'on part du principe que maintenant, c'est écrit dans la loi, en fait. Ah oui.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la loi", "la loi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hein, c'est ça", "le bien", "tous ces nouveaux critères", "notamment avec les conséquences", "matières premières", "la loi", "la loi", ", je", "C'est ça", ", en fait", "C'est ça", "la loi", "la loi", ", hein", "C'est ça", "en fait", ", hein", ", en fait", ", c'est ça", ", c'est ça", "les conséquences", ", c'", "il est", "Ah oui", ", hein", "C'est ça", ", c'est ça", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 44, "end": 49}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_046", "text": "<PERSON><PERSON>, d<PERSON><PERSON><PERSON> y'a quelque chose qui change par rapport à aujourd'hui, c'est qu'y'a des nouvelles normes qui imposent un certain nombre de choses, c'est pas possible autrement. Par exemple, interdiction du... du pé<PERSON>le, heu...", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un certain nombre", "par exemple", "un certain nombre", "quel<PERSON> chose", "des nouvelles normes", "un certain nombre", "un certain nombre", "quel<PERSON> chose", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "quel<PERSON> chose", "quel<PERSON> chose", "un certain nombre", "quel<PERSON> chose", "quel<PERSON> chose", ", c'", "un certain nombre", "quel<PERSON> chose", "quel<PERSON> chose", "un certain nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 45, "end": 47}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_047", "text": "ce qui heu... comment dire ? Est utile à la société.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["la société", "la société", "Est utile à la société", "la société", "qui heu", "la société", "la société", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 46, "end": 48}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_048", "text": "On sort de ces systèmes purement capitalistes où tu fais exactement ce que tu veux, tu peux gagner des milliards et des milliards sans... voilà, c'est ce côté-là où cette fois, y'a la loi qui est inscrite et heu... y'a un équilibre privé/public à... voilà. Oui. Et du coup, tu la fais intervenir dans la légitimité ? Légitimité, ce serait par exemple des boîtes qui respecteraient les lois, par exemple, ou pas ?", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 73, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["par exemple", "des bo<PERSON>", "la légitimité", "du coup", "la loi", "la loi", "La légi", "la légitimité", "c'est ce", "la loi", "ces systèmes purement capitalistes", "des milliards", "des milliards", "... voil<PERSON>, c'est ce côté", "cette fois", "la loi", "un équilibre privé", "la légitimité", "les lois", "et du coup", "Et du coup", "la loi", "la loi", "des milliards", "la légitimité", "du coup", "du coup", "Et du coup", "du coup", "du coup", "la légitimité", "la légitimité", "la légitimité", "des bo<PERSON>", ", ce", ", c'", "du coup", "des milliards", "des bo<PERSON>", "la légitimité", "des milliards", "des milliards"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 47, "end": 51}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_049", "text": "Non, je sais pas du tout si tu... si tu parlais juste pour faire un échange ou si... enfin je veux dire, plus largement, ou si tu étais sur cette brique. Je pense que ça pourrait être heu... Pour moi, apr<PERSON>, on peut commencer... on peut commencer par ça, enfin qui respectent les... les nouvelles lois heu... environnementales et éthiques, on peut mettre, heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 64, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 2.0, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["pour moi", "les lois", "un échange", "cette brique", "les nouvelles lois heu", ", je", "que ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 48, "end": 50}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_050", "text": "Enfin qui utilisent... qui utilisent plus d'énergies fossiles. Qui utilisent ? Qui n'utilisent plus. Qui n'utilisent plus d'énergies fossiles. <PERSON>, c'est ça, oui.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["énergies fossiles", "énergies fossiles", "C'est ça", "C'est ça", "C'est ça", ", c'est ça", ", c'est ça", ", c'", "Voilà, c'est ça", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 49, "end": 54}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_051", "text": "<PERSON><PERSON> al<PERSON>, on peut mettre « qui utilisent uniquement des énergies renouvelables », c'est... enfin je pense, c'est... l'un est l'opposé de l'autre, la négation. Y'a de la place, quand même, on peut mettre... «", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des énergies renouvelables", "la place", "Uniquement des énergies renouvelables", "des énergies renouvelables", "des énergies renouvelables", "énergies renouvelables", ", c'est...", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 50, "end": 52}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_052", "text": "Uniquement des énergies renouvelables. Ou alors, par compensation, c'est-à-dire que si t'utilises un bateau qui tourne au pétrole pour heu... ramener tes iPhone, tes Mac, heu... der<PERSON><PERSON>, ils financent des projets d'énergies renouvelables à côté, d'une valeur équivalente.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des énergies renouvelables", "Uniquement des énergies renouvelables", "un bateau", "tes iPhone", ", tes <PERSON>", "énergies renouvelables à côté", "une valeur équivalente", "des énergies renouvelables", "des énergies renouvelables", "énergies renouvelables", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 51, "end": 53}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_053", "text": "<PERSON>, <PERSON>, je sais pas de quoi il a besoin pour faire ses téléphones... Ah bah oui, non, mais après... Je sais qu'y'a un certain nombre de choses qui sont pas très très loin de là où je suis née et je peux vous dire qu'y'a rien...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 47, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un certain nombre", "un certain nombre", "un certain nombre", ", je", "ses téléphones", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "Ah oui", "un certain nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 52, "end": 55}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_054", "text": "Ah bah oui, non, mais c'est sûr que les terrains, apr<PERSON>, les métaux rares, par exemple, au final, c'est... bah je pense que si on te dit « t'en as besoin pour créer un téléphone, une voiture fabriquée », pour ça, tu pars du principe, tu dis « non, on a interdit, parce que y'en a de moins en moins et que l'exploitation, c'est heu... c'est néfaste pour l'environnement, les personnes. Ou alors, tu légifères sur tout ça et tu dis que les personnes qui travaillent, peu importe où c'est dans le monde, il faut qu'ils soient dans de bonnes conditions, etc.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 102, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.39215686274509803}, "noun_phrases": ["par exemple", "c'est heu", "les terrains", "les métaux rares", "un téléphone", "une voiture", ", les personnes", "tout ça", "le monde", "de bonnes conditions", ", c'est...", "le monde", "le monde", "le monde", "tout ça", ", c'", "tout ça", "tout ça", "Ah oui", "au final", ", au final", ", au final"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 53, "end": 55}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_055", "text": "Alors oui, on peut faire pour sa propre maison des énergies renouvelables, etc., mais juste, à l'échelle mondiale, on n'est pas capables parce que ça produit pas autant. Si tu vois <PERSON> sur le nucléaire, etc., enfin quand il montre ce que produisent les énergies renouvelables et ce que produit aujourd'hui... Bah oui, non, mais c'est sûr, oui.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 58, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["des énergies renouvelables", "les énergies renouvelables", "des énergies renouvelables", "des énergies renouvelables", "sa propre maison", "énergies renouvelables", "l'échelle mondiale", "le nucléaire", "les énergies renouvelables", "les énergies", "que ça", "Ah oui"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 54, "end": 57}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_056", "text": "<PERSON><PERSON> du coup, je me dis, heu... tu vois, en fait , je...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", ", je", ", en fait", "du coup", "du coup", "du coup", "en fait", "du coup", ", en fait", "du coup", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 55, "end": 57}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_057", "text": "<PERSON> vois, ou là, comme on est 2050, c'est demain, est-ce qu'on serait pas sur plus justement qui réduit au minimum ? <PERSON><PERSON>, qui minimisent l'utilisation des... Mais enfin c'est juste heu... mon avis, hein, je... <PERSON><PERSON>, oui, on peut mettre, on peut rajouter ça, oui.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", je", ", hein", ", hein", ", c'", ", hein"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 56, "end": 58}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_058", "text": "C'était quoi, les énergies fossiles ? « Qui utilisent au minimum les énergies fossiles », oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["énergies fossiles", "énergies fossiles", "les énergies fossiles", "les énergies fossiles", "les énergies", "c'était qu"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 57, "end": 59}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_059", "text": "<PERSON><PERSON>, tu rachètes un nouveau téléphone, oui. Donc c'est... c'est dans l'idée de se dire que la... la boîte, elle continue de vivre, non pas juste en produisant, mais en améliorant ce qu'elle a déjà produit.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 36, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le co", "un nouveau téléphone", "un nouveau téléphone", "Donc c'est... c'est dans l'idée", "la boîte", "un nouveau téléphone"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 58, "end": 60}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_060", "text": "C'est vrai que c'est le mot que je cherchais, l'expression. <PERSON><PERSON>, c'est... c'est terrible, je sais même pas à quel moment j'ai eu le... j'ai eu le déclic, de dire : je vais l'emmener chez un... Le réparateur, oui. C'était la première fois que je mettais les pieds là-bas.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", je", "quel moment", "le dé<PERSON>lic", "Le réparateur", "C'était la première fois que je mettais les pieds là-bas.", ", c'est...", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 59, "end": 63}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_061", "text": "Mais c'est fou comme.... mon chargeur, ça chargeait plus du tout, j'ai acheté plusieurs autres chargeurs, ça ne fonctionnait toujours pas. <PERSON><PERSON><PERSON>, y'a cinq ans, j'aurais juste acheté un autre téléphone, c'est tout. Pourquoi à ce moment-là ? Je sais pas. Oui, oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce moment", "mon chargeur", "plusieurs autres chargeurs", "cinq ans", "un autre téléphone", "Pourquoi à ce moment", ", c'", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 60, "end": 65}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_062", "text": "Et je sais pas pourquoi je pense à ça maintenant, mais ça me fait penser, heu... tu sais, des fois, quand ton écran, il se fissure, et puis tu te dis : bon bah justement, est-ce que je change mon téléphone ou pas ? Et puis des fois, maintenant, on se... bah on pourrait rentrer dans la démarche de dire : bah non, on change pas son téléphone, mais pour autant, des fois, on change pas forcément l'écran, alors que en fait, en tout cas la... ça dépend de quelle lentille on regarde, mais pour la santé, c'est hyper mauvais, parce que du coup, y'a... y'a plein de... alors je sais pas exactement ce que c'est, mais en gros, les composants font que ça... ça met des trucs sur la peau, enfin ça...", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 133, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["tout cas", "du coup", "tout cas", "tout cas", "tout cas", "tout cas", "mon téléphone", "des fois", "quand ton écran", "mon téléphone", "la démarche", "son t<PERSON><PERSON><PERSON>", "des fois", "tout cas", "quelle lentille", "parce que du coup", "les composants", "des trucs", "la peau", "enfin ça", "parce que du coup", "parce que du coup", "que ça", "mais ça", "du coup", "du coup", "mais ça", "en tout cas", "du coup", "en tout cas", "tout cas", "des trucs", "en fait", "quelle lentille", "du coup", "tout cas", "des fois", "<PERSON><PERSON>a", "tout cas", ", c'", "des trucs", "tout cas", "la santé", "du coup", "<PERSON> fois", "en fait", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 61, "end": 63}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_063", "text": "Et du coup, finalement, pour la santé, tu vois, c'est pas bon. C'est bon à savoir, ça. <PERSON><PERSON>, j'ai lu un truc là-dessus, alors je saurais pas vous réexpliquer dans le détail, mais oui, ça... ça fait sens, parce que du coup, heu... Bah ça m'étonne pas, parce que tu dois avoir un paquet de produits de... composés chimiques, tout ça. <PERSON><PERSON>, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", tout ça", "du coup", "tout ça", "un truc", "et du coup", "parce que du coup", "Et du coup", ", finalement, pour la santé", "un truc", "le d<PERSON>", "parce que du coup", "un paquet", ", tout ça", "un truc", "un truc", "un truc", "parce que du coup", "du coup", "du coup", "Et du coup", "du coup", "du coup", ", tout ça", "un paquet", "tout ça", ", c'", ", tout ça", "tout ça", "un truc", "la santé", "du coup", "tout ça", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 62, "end": 67}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_064", "text": "Bref... D'accord. <PERSON><PERSON>, justement toujours dans cette heu... dans cette optique-là, c'est que ça reste quand même accessible, parce qu'aujourd'hui, tu veux changer tout ton é<PERSON>ran, des fois, vaut mieux racheter un nouveau téléphone. <PERSON><PERSON>, c'est sûr. Bah oui, oui.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un nouveau téléphone", "un nouveau téléphone", "des fois", "des fois", ", justement toujours dans cette heu", "tout ton é<PERSON>ran", "un nouveau téléphone", "cette heu", "que ça", "des fois", ", c'", "Ah oui", "<PERSON> fois"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 63, "end": 67}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_065", "text": "Donc du coup, que ça reste quand même... en cas de panne sur l'existant, mais que ça coûte pas un bras de... Oui, parce que si ça te coûte la moitié du prix pour changer, typiquement, c'est... c'est le scénario où tu changes pas, parce que tu te dis : ça va me coûter la moitié du prix de l'achat du neuf, bon...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", "le scénario", "un bras", "la moitié", "la moitié", "que ça", "du coup", "du coup", ", c'est...", "du coup", "du coup", ", c'", "du coup"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 64, "end": 66}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_066", "text": "<PERSON><PERSON>, et puis les services. Et là en parlant, oui, de... après, on peut parler... je sais pas, par exemple, aussi de la fraude fiscale, machin et tout, enfin financièrement, quoi. Effectivement... <PERSON><PERSON>, complètement. Est -ce qu'une entreprise qui a des profits indécents mérite toujours d'exister heu... en 2050, quoi ? Oui, oui.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "par exemple", "une entreprise", "puis les services", "aussi de la fraude fiscale", "des profits", "indécents mérite", "fraude fiscale", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 65, "end": 72}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_067", "text": "Ou est-ce que le profit, à partir d'un certain moment... mais bon, c'est sûr que c'est difficile à légiférer dessus, mais... Et est-ce que justement, pour aller dans... dans ton sens, est-ce que l'État ne prend pas plus de place dans cette société-là, au point de dire, quand on a un mauvais dirigeant, même privé, la loi permet de l'enlever, de mettre quelqu'un d'autre, pour remplir... est-ce que ce serait légitime d'aller heu... parce que là, tu disais... heu... est-ce qu'elle a une raison d'exister à partir du moment où elle ne respecte pas la loi ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 97, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 1, "side_b": 1, "tension_strength": 0, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la loi", "la loi", "la loi", "la loi", "que ce", "ton sens", "cette société", "un mauvais dirigeant", "la loi", "une raison", "la loi", "... Est", ", c'", "que ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 66, "end": 68}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_068", "text": "Alors je pense que ce serait... enfin je vais un peu trop loin, je pense, dans l'idée de... mais tu sais, est-ce que ce serait pas... une boîte légitime, une boîte, en gros, je sais pas si... c'est pas du tout le bon terme, mais tu sais, qui « collabore », en gros, avec l'État, quoi. <PERSON>, où y'a un truc un peu de l'État qui a une vision de vers quoi il veut amener un peu le pays.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 80, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une boîte", "une boîte", "une boîte", ", je", "que ce", "un truc", "une boîte légitime", "un truc", "une boîte légitime", ", une boîte", "un truc", "un peu de l'État", "une vision", "un peu le pays", "un truc", "un truc", "une boîte", "une boîte légitime", "... un", "... une boîte", "une boîte", "un truc", "une boîte", "une boîte", "que ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 67, "end": 69}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_069", "text": "<PERSON><PERSON>, parce que là, je pourrais faire une partie genre en gros privé, et puis là public. Sauf si on part du principe...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": [", je", "une partie genre", "gros privé", "une partie", "une partie"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 68, "end": 70}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_070", "text": "des règles et après, surtout le côté heu... fraude... fraude fiscale, quoi, dont on entend parler tous les ans, et en fait, t'as l'impression qu'y'a rien qui change. Et forcément, toute la transition énergétique, etc., ça va avoir besoin de... capitaux, de... d'argent, qui pourrait être financée avec heu... toute la fraude fiscale. Après, bon, c'est... est-ce que c'est utopique ? 2050 , hein.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 64, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des règles", "des règles", "surtout le côté heu... fraude", "fraude fiscale", "tous les ans", "toute la transition énergétique", "... capitaux", "... d'argent", "... toute la fraude", ", hein", ", c'est...", "et en fait", "en fait", ", hein", "... Est", ", c'", ", hein", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 69, "end": 74}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_071", "text": "Non, non, je rigole, mais parce que... Non, parce qu'aujourd'hui, on en parle encore, donc concrètem<PERSON>, ça veut dire qu'une entreprise qui est basée en France puisse pouvoir heu... payer tous ses impôts en France, en fait. Bah oui. Oui, oui, je suis d'accord. Que ce soit vraiment une réalité, oui. Du coup, je fais... dans son pays de... de résiden... ? Comment je mets ?", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "du coup", "une entreprise", ", je", "que ce", ", en fait", "une entreprise", "tous ses impôts", "son pays", "du coup", "du coup", "une entreprise", "du coup", "en fait", "du coup", "une entreprise", ", en fait", "une entreprise", "une entreprise", "une entreprise", "du coup", "une entreprise", "une entreprise", "Ah oui", "que ce", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 70, "end": 76}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_072", "text": "Ou je mets en France, dans le sens où on se dit que... enfin tu vois. Qui paye ses impôts heu... bah d'origine, oui et non. Pays d'origine, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le sens", "le sens", "ses impôts heu", "le sens", "le sens", "le sens", "le sens", "le sens", "dans le sens", "le sens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 71, "end": 75}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_073", "text": "Enfin sans fraude heu... oui, sans fraude fiscale, oui, surtout, oui. Alors oui, parce que du coup, je voulais repartir, parce que heu... une boîte légitime, aussi, quand je pensais heu... à tout ce qui est déchets, quels qu'ils soient, textiles, plastiques, etc., tu vois, une boîte qui va... va produire énormément de déchets, mais qui va l'envoyer justement en Afrique, en Océanie, etc., qui... ou du coup, c'est les populations les plus pauvres qui heu... déjà polluent le moins, qui subissent déjà les changements environnementaux et en plus de ça, nos propres heu... déchets.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 95, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une boîte", "tout ce", "une boîte", "une boîte", "Heu... une boîte", "heu... une boîte", "du coup", ", je", "une boîte légitime", "parce que du coup", "parce que du coup", "une boîte légitime", ", une boîte", "fraude fiscale", "Enfin sans fraude heu", "parce que du coup", ", textiles", "une boîte", "les changements environnementaux", "en plus de ça", "nos propres heu", "une boîte légitime", "du coup", "du coup", "du coup", "tout ce", "du coup", "parce que heu", "qui heu", "... un", "... une boîte", "une boîte", ", c'", "du coup", "une boîte", "une boîte", "tout ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 72, "end": 75}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_074", "text": "Donc tu vois, en gros, qui assumeraient pleinement, en fait, heu... Oui, oui, et heu... oui, limite, tu... tu vois, je vois dans l'idée que ça, ça devient obligatoire, en fait, en 2050, toutes ces choses-là. C'est-à-dire que pour moi, le législateur, ça n'est plus possible.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pour moi", "toutes ces choses", ", je", ", en fait", "que ça", "le législateur", "en fait", ", en fait", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 73, "end": 77}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_075", "text": "Je sais pas comment le... limite, ça n'est même plus du domaine de... des... Non, mais c'est vrai que en parlant plastique, on peut dire heu... qui n'utilise plus aucun plastique, enfin au moins pour le packaging. Au moins pour le packaging.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le... limite", "aucun plastique", "enfin au moins pour le packaging", "moins pour le packaging", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 74, "end": 76}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_076", "text": "Tu vois ? Parce que du coup, ça fait que de revenir et en fait, on... on ne le dit pas, parce que y'a cette consigne. On est bloqués par ça, oui. Mais en fait, bah non. Non. Je suis d'accord", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 41, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", "parce que du coup", "parce que du coup", "parce que du coup", "cette consigne", "du coup", "du coup", "et en fait", "du coup", "en fait", "du coup", "du coup", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 75, "end": 81}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_077", "text": ". C'était quoi, la consigne ? Que serait une boîte légitime ? Je crois qu'y'a personne qui s'est arrêté. O<PERSON>, c'est ça, mauvais élèves.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "une boîte", "une boîte", "une boîte", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "une boîte légitime", "<PERSON><PERSON>, c'est ça", "C'est ça", "une boîte légitime", "une boîte", "une boîte légitime", "<PERSON><PERSON>, c'est ça", ", mauva<PERSON>", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", "une boîte", ", c'est ça", ", c'", "<PERSON><PERSON>, c'est ça", "une boîte", "une boîte", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça", "c'était qu"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 76, "end": 80}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_078", "text": "Donc qui... qui légifère, c'est ça que tu disais ? O<PERSON>, qui légifère. Pour contraindre... Ça s'é... à force d'écrire à l'ordi, je sais plus comment ça s'é... c'est avec un « F » ? Oui, c'est un « F », oui. <PERSON><PERSON>, je vous dérange deux secondes, heu... en fait, pour vous accompagner là sur la deuxième partie de cette question de la légitimité, toujours, on reste dessus, là, heu... on a fait des petites cartes « en cas de panne » sur la question des parties prenantes.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 89, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la question", "la question", "la question", "la question", "la légitimité", "la question", "la question", "La légi", "parties prenantes", "la légitimité", "la légitimité", ", je", "C'est ça", "C'est ça", "deux secondes", ", heu... en fait", "la deuxième partie", "cette question", "la légitimité", "des petites cartes", "la question", "parties prenantes", "parties prenantes", "la question", "La question", "la question", "la question", "C'est ça", "La question", "en fait", "la légitimité", "la question", "la question", "la légitimité", "la légitimité", ", c'est ça", "parties prenantes", ", c'est ça", ", c'", "la question", "C'est ça", ", c'est ça", "en fait", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 77, "end": 83}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_079", "text": "Et donc on a identifié un certain nombre de parties prenantes et ce que je vous propose, c'est de les regarder, de voir s'il y en a qui vous parlent, qui vous aident, qui vous font débat aussi. Et toutes celles que vous utilisez, que ce soit pour un... pour débattre, même si vous l'utilisez pas après et que ça vous aide pas à écrire des choses, mais que vous en débattez ou que ça vous sert pour avancer dans votre réflexion, vous les mettez de cô<PERSON>.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 87, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un certain nombre", "des choses", "des choses", "des choses", "un certain nombre", "des choses", "parties prenantes", "un certain nombre", "un certain nombre", "que ce", "que ça", "parties prenantes", "un certain nombre", "parties prenantes", "toutes celles", "des choses", "votre réflexion", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", ", vous", "un certain nombre", "un certain nombre", "parties prenantes", "des choses", ", c'", "un certain nombre", "des choses", "que ce", "des choses", "un certain nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 78, "end": 80}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_080", "text": "Et nous, c'est au cœur de ce qu'on fait, donc on les rajoute ».", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 79, "end": 81}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_081", "text": "Donc vous les rajoutez et vous les mettez aussi de cô<PERSON>. Ça va ?", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 80, "end": 82}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_082", "text": "Mais tu sais que j'ai des missions très importantes au sein du laboratoire ? Ah bon ? Je m'occupe de la communication, très cher. Voilà, c'est magnifique.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["des missions très importantes", "la communication, très cher", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 81, "end": 85}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_083", "text": "Mais je prends pas tous les outils, parce qu'il faudrait quand même pas que... donc j'ai mis donc « qui légifère et qui prend une place importante pour obliger les entreprises privées ». C'est ce que... c'est ce que tu disais.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 41, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["c'est ce", "tous les outils", "une place importante", "les entreprises privées", "une place", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 82, "end": 84}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_084", "text": "Que finalement, les... que les choses.... un certain nombre de choses heu... qui seraient des doléances aujourd'hui deviennent des normes, en fait. Oui. C'est que y'a plus de possibilité de faire autrement, ça veut dire qu'y'a... y'a plus de... d'entreprise qui permet de prendre des déchets, de les envoyer à l'autre bout du monde, ça n'existe plus. C'est que on n'ait même plus à se poser de questions.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 68, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", ", en fait", "l'autre bout", "l'autre bout", "un certain nombre", "les choses", "un certain nombre", "choses heu", "des normes", "des déchets", "l'autre bout", "C'est que on n'ait même plus à se poser de questions.", "ses heu", "un certain nombre", "un certain nombre", "un certain nombre", "les choses", "l'autre bout", "un certain nombre", "un certain nombre", "du monde", "en fait", "... un", ", en fait", "un certain nombre", "des doléances", "un certain nombre", "l'autre bout", "l'autre bout", "un certain nombre", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 83, "end": 87}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_085", "text": "<PERSON><PERSON>, même le... apr<PERSON>, le souci entre guillemets, c'est qu'il faut aussi que ce soit mondial, quoi, donc c'est même pas un État, il faut que ce soit... parce que c'est bien beau, tu vois, si la <PERSON> le fait, mais que derri<PERSON>, les Chinois, les Américains, les Indiens, ils... ils font pas... Oui, mais la France, c'est... c'est un pays qui... qui est libre de bloquer ses heu... ses frontières vis-à-vis d'un certain nombre de choses, qui le fait aujourd'hui. Oui, mais ça veut dire quoi, par exemple ? C'est que demain, heu... demain, je sais pas, y'a des entreprises heu... françaises, elles respectent, OK, tout ce qui est dit par l'État français, mais que les... les entreprises extérieures le font pas, tu interdis l'importation de tous les produits heu... extérieurs ? Bah tu pourrais, mais... avoir... der<PERSON><PERSON>, ça rend heu...", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 142, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.7042253521126761}, "noun_phrases": ["un certain nombre", "par exemple", "tout ce", "Le fait", "un certain nombre", "un certain nombre", ", je", "un certain nombre", "le fait", "que ce", "un certain nombre", "un certain nombre", "le souci", "la France", "mais la <PERSON>", "ses heu", "... ses frontières", "un certain nombre", "des entreprises heu", "l'État français", "les entreprises extérieures", "tous les produits heu", "mais ça", "un certain nombre", "un certain nombre", "un certain nombre", "mais ça", ", c'est...", "tout ce", "un certain nombre", "le fait", "le fait", "un certain nombre", "<PERSON><PERSON>a", "le fait", ", c'", "un certain nombre", "le fait", "les entreprises", "le fait", "que ce", "un certain nombre", "tout ce", ", ça", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 84, "end": 89}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_086", "text": "<PERSON><PERSON>, parce que tu vois, ça me fait écho à... à ça, difficultés des transports de marchandises, du coup, comme y'a difficultés de transports sans forcément, encore une fois, dire « on arrête toute importation », mais est-ce qu'à des moments on... voilà, se pose la question de la relocalisation, et donc... mais ça, c'est peut-être parce que je suis universitaire, mais du coup, et de la formation, du coup, tu vois, pour réapprendre aussi des savoirs qui ont été heu... justement délocalisés, quoi. Et donc de fait, bah on peut reproduire peut-être un certain nombre de choses, peut-être pas tout, et puis après, c'est... c'est la question de : est-ce qu'on garde heu... le même heu... train de vie, le même niveau de vie ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 126, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.47619047619047616}, "noun_phrases": ["un certain nombre", "encore une fois", "la question", "la question", "la question", "la question", "qu'à des moments", "des moments", "du coup", "la question", "la question", "un certain nombre", "un certain nombre", "un certain nombre", "mais du coup", "la question", "un certain nombre", "un certain nombre", "un certain nombre", "encore une fois", "toute importation", "la question", "la relocalisation", "mais ça", "du coup", "de la formation", "des savoirs", "un certain nombre", "puis après, c'est... c'est la question", "le même heu", "le même niveau", "un certain nombre", "du coup", "un certain nombre", "La question", "la relocalisation", "la question", "la question", "mais ça", ", c'est...", "du coup", "un certain nombre", "La question", ", du coup", "du coup", "la question", "mais du coup", "la question", "un certain nombre", "<PERSON><PERSON>a", "<PERSON><PERSON> du <PERSON>", ", c'", "un certain nombre", "du coup", "la question", "encore une fois", ", du coup", "un certain nombre", "<PERSON><PERSON> du <PERSON>", "<PERSON><PERSON> du <PERSON>", ", ça", "<PERSON><PERSON>a", "mais du coup"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 85, "end": 87}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_087", "text": "<PERSON><PERSON>, on était dans le scénario de business as usual, donc je pense qu'il faut conserver cet état d'esprit. Heu ... est-ce qu'y'a un certain nombre de choses qu'on peut heu... déjà faire différemment, tout de même ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un certain nombre", "un certain nombre", "le scénario", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "cet état", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "... Est", "un certain nombre", "un certain nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 86, "end": 89}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_088", "text": "<PERSON><PERSON>, oui, et puis quand on parle de... de ça, là, les difficultés des transports, est-ce qu'à un moment, de toute façon, ça va pas être du bon sens de dire qu'on n'aura plus les moyens, en fait, d'emmener les choses à l'autre bout du monde ? Oui. Est -ce que... est-ce que ça va pas s'imposer à nous, en fait ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 62, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["bon sens", "un moment", "un moment", "les moyens", ", en fait", "que ça", "l'autre bout", "l'autre bout", "les choses", "l'autre bout", "... de ça", "les difficultés", "les moyens", "les choses", "l'autre bout", "toute façon", "du monde", "en fait", ", en fait", "... Est", "l'autre bout", "l'autre bout", "bon sens", "les moyens", "les moyens", "les moyens", "en fait", "un moment", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 87, "end": 91}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_089", "text": "Bah ça aura plus aucun sens, oui, c'est ça. Une fois que t'auras plus le pétrole gratuit, enfin machin... Parce qu'aujourd'hui, pareil, on voit des...", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "aucun sens", "le pétrole gratuit", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", ", c'", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 88, "end": 91}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_090", "text": "des vols des fois qui... qui roulent... non, qui roulent pas, qui sont à vide, donc ça, pareil, demain, c'est... c'est impossible, en fait. Oui, oui. Et du coup, « prend une place » et du coup « qui repense ses... ses relations internationales ». Ah oui, oui, bah oui. <PERSON><PERSON>, ou tu peux pas être un complice, allié d'un État heu... qui est tout le contraire de toi-même. Exactement.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["le co", "du coup", ", en fait", "et du coup", "des fois", "des fois", "Et du coup", "du coup", "des vols", "une place", "du coup", "ses... ses relations internationales", "être un complice", "un État heu", "Et du coup", ", c'est...", "du coup", "en fait", "du coup", "des fois", ", en fait", ", c'", "du coup", "Ah oui", "<PERSON> fois", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 89, "end": 95}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_091", "text": "Et donc t'es obligé de renégocier de nouvelles choses pour être heu... pour être cohérent. Et nous, en France, y'a quand même l'Union européenne, donc ça paraît difficile de se dire que ça... ça restera juste au niveau français, je pense pas.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", je", "que ça", "les choses", "les choses", "de nouvelles choses", "l'Union européenne", "niveau français"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 90, "end": 92}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_092", "text": "Ils disaient que de toute façon, vu que... vu les coûts de... des transports, justement, qui avaient explosé post-Covid, etc., ils disaient bien qu'y'avait une heu... une optique de relocaliser. Je sais pas où ça en est, ça a été totalement oublié, parce que maintenant, les prix sont redevenus à la normale.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 52, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["une heu", "toute façon", "les coûts", "une heu", "... une optique", "les prix", "une heu", "... un", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 91, "end": 93}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_093", "text": "<PERSON><PERSON><PERSON><PERSON> pas, <PERSON><PERSON><PERSON><PERSON>, à écrire à l'arrière de la feuille, si vous avez plus de place, hein, vous... <PERSON>a marche. On avait « vis-à-vis des produits et des services », mais peut-être pas vis-à-vis des personnes en tant que telles.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la feuille", "des produits", ", hein", "Vis-à-vis des personnes", ", Stéphanie", "la feuille", ", vous", "des services", ", hein", "des produits", "des personnes", "des produits", "des services", ", hein", "la feuille"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 92, "end": 95}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_094", "text": "Du salarié, en fait, de... du consommateur de... de façon générale. Ah oui, du salarié aussi.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", en fait", "... de façon générale", "en fait", ", en fait", "Ah oui", "du salarié", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 93, "end": 95}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_095", "text": "Et qu'on n'ait plus à se poser la question de... je sais pas, de l'accessibilité de certaines personnes handicapées, que... enfin que... qu'une entreprise ressemble à la société, quoi, que chacun ait son petit rôle à jouer en fonction de ses capacités. Et heu... voilà, une heu... une répartition du travail et un salaire plus juste pour tout le monde, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "la société", "la question", "personnes handicapées", "la question", "la question", "la question", "une entreprise", "la société", "la question", "la question", "tout le monde", "tout le monde", "le monde", "tout le monde", "une heu", "une entreprise", "la question", "la question", "La question", "une heu", "la question", "la question", "certaines personnes", "une entreprise", "la société", "ses capacités", "une heu", "une répartition", "un salaire", "tout le monde", "La question", "tout le monde", "le monde", "une entreprise", "la question", "la question", "... un", "une entreprise", "tout le monde", "le monde", "le monde", "une entreprise", "une entreprise", "tout le monde", "une entreprise", "la question", "une entreprise", "la société", "tout le monde", "tout le monde", "tout le monde", "la société", "tout le monde", "tout le monde", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 94, "end": 96}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_096", "text": ", c'est... <PERSON><PERSON>.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 3, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", c'est...", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 95, "end": 97}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_097", "text": "Et d'autant plus qu'on sait très bien, alors là, par exemple, pour reprendre le cas des hommes et des femmes, mais je pense que ça doit être la m... à peu près le même mécanisme heu... sur d'autres sujets, hein, le handicap, etc., heu... c'est que en temps de crise, on recule sur les droits, c'est-à-dire que par exemple, heu... je crois qu'à la base, enfin à la base... avant le Covid, on était, je crois, à... on envisageait l'égalité entre les femmes et les hommes dans 80 ans, puis crise <PERSON>, bah ça a fait reculer de 20 ans, en fait, donc là, c'est à 100 ans. Et en fait, l'Histoire a montré que voilà, à chaque crise, on fait reculer heu... Et c'est quoi, la... y'a une explication à ça ? Eh bah il faut que je creuse, j'avoue que je... j'ai pas creusé le sujet.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 148, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la base", "par exemple", "le co", "la base", ", je", ", en fait", ", hein", "que ça", "le cas", "des femmes", "peu près le même mécanisme heu", "d'autres sujets", ", le handicap", "les droits", "enfin à la base", "le Covid", "les femmes", "les hommes", "80 ans", "20 ans", "là, c'est à 100 ans", "chaque crise", "une explication", "le sujet", "le Covid", "le cas", "des femmes", "le Covid", "les femmes", "et en fait", "autres sujets", "les hommes", "20 ans", "C'est quoi", "en fait", ", hein", ", en fait", "à 100", ", c'", "le Covid", "les hommes", ", hein", "en fait", "20 ans"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 96, "end": 100}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_098", "text": "Mais par exemple, juste, alors ce qui n'est pas la... la cause, mais en tout cas, comment ça a été fait, par exemple, le <PERSON><PERSON>, sur le cas des hommes et des femmes, heu... eh bah les invités dans les médias étaient des hommes, parce que bah sujets sérieux, scientifiques, quand même, le <PERSON><PERSON>, donc bah du coup, les femmes étaient moins invitées. Donc en fait, la proportion de femmes entendues dans les médias a baissé. Et... et si on regarde en plus sur quoi on les a interrogées, heu... bon bah en gros, c'était pas sur les sujets importants, quoi, parce qu'elles sont pas légitimes. Savoir comment elles cuisinaient pendant le télétravail. Exactement, oui. Si elles regardaient Cyril Lignac avec ses recettes, etc.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 124, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["tout cas", "par exemple", "le co", "du coup", "tout cas", "tout cas", "tout cas", "tout cas", "tout cas", "du coup", "du coup", "le cas", "des femmes", "le Covid", "les femmes", "Mais par exemple", "en tout cas", "le Covid", "le cas", "des femmes", "eh bah les invités", "les médias", "le Covid", "donc bah du coup", "les femmes", "la proportion", "les médias", ", c'était pas sur les sujets importants", "le télétravail", "<PERSON>", "ses recettes", "du coup", "en tout cas", "tout cas", "en fait", "du coup", "tout cas", "les médias", "les médias", "les médias", "tout cas", ", c'", "tout cas", "le Covid", "du coup", "Mais en tout cas", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 97, "end": 103}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_099", "text": "Heu... et en fait, ça a été le cas sur plein d'autres sujets et donc finalement, bah là où on allait avancer, en fait, on a régressé. Par contre, le mécanisme de pourquoi, etc., tu vois, j'aurais du mal à le dire, mais tu vois, c'est comme pendant la guerre, on a fait beaucoup appel aux femmes, hein, pour gérer les industries, etc., et puis en fait, dès que les hommes sont revenus, on leur a redit qu'elles étaient pas capables, quoi. Complexe heu... complexe d'infériorité, les gars. On est là pour les aider, ils ont les boules et du coup, heu... non, non.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 104, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", ", en fait", "et du coup", "Et du coup", ", hein", "du coup", "du coup", "Et du coup", "le cas", "d'autres sujets", "les hommes", "le cas", "et en fait", "autres sujets", "contre, le mécanisme", "pendant la guerre", "les industries", "puis en fait", "les hommes", "Complexe heu", "les gars", "les boules", "du coup", "les industries", "en fait", ", hein", "du coup", ", en fait", "la guerre", ", c'", "du coup", "les hommes", ", hein", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 98, "end": 102}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_100", "text": "Donc je saurais pas expliquer heu... davantage pourquoi, mais en tout cas, ce que je veux dire, c'est que globalement, heu... là on se dit que ce serait souhaitable, mais que dans un scénario probable, ça n'arrivera pas. D'accord.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["tout cas", "tout cas", "tout cas", "tout cas", "que ce", "tout cas", "tout cas", "en tout cas", "en tout cas", "c'est que globalement, heu...", "un scénario probable", "tout cas", "tout cas", ", ce", "tout cas", ", c'", "tout cas", "Mais en tout cas", "que ce", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 99, "end": 101}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_101", "text": "Alors que on voit bien que par exemple, dans tout ce qui est le milieu de l'économie sociale et solidaire, de l'engagement de militants, enfin en l'occurrence militantes, c'est beaucoup heu... des femmes, parce que c'est le domaine du « prendre soin » et le care, c'est plutôt quelque chose qui est genré féminin. Heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["par exemple", "tout ce", "le milieu", "l'économie sociale et solidaire", "quel<PERSON> chose", "quel<PERSON> chose", "des femmes", "des femmes", "tout ce", "l'économie sociale et solidaire", "enfin en l'occurrence militantes", "le care", ", c'est plutôt quelque chose", "l'économie sociale et solidaire", "quel<PERSON> chose", "quel<PERSON> chose", "coup heu", "quel<PERSON> chose", "quel<PERSON> chose", ", c'", "quel<PERSON> chose", "quel<PERSON> chose", "tout ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 100, "end": 102}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_102", "text": "mais même si y'a beaucoup de militants, de gens qui sont engagés dans des structures de l'économie sociale et solidaire, quand il faut parler heu... concret, bah on va plutôt entendre <PERSON>, etc., enfin des hommes heu... Ah oui", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["l'économie sociale et solidaire", "des structures", "l'économie sociale et solidaire", "des structures", "l'économie sociale et solidaire", "enfin des hommes heu", "Ah oui"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 101, "end": 103}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_103", "text": "Donc elle a... elle a beaucoup œuvré heu... bah pour s'ériger contre les industries, pareil sur heu... donc elle... elle travaille beaucoup sur heu... tout ce qui est semences, et aussi sur l'accessibilité à l'eau, bah par exemple de Coca qui puisait toutes les sources d'eau en Inde et qui faisait que les populations locales avaient même pas de quoi boire. Et elle a vraiment fait des grosses grosses actions là-dessus. Et elle fait aussi des formations, justement, sur l'agriculture heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 81, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["par exemple", "tout ce", "le travail", "Le fait", "le fait", "les industries", "tout ce", "les industries", "toutes les sources", "les populations locales", "des grosses grosses actions", "des formations", "l'agriculture heu", "le fait", "le fait", "le fait", "le fait", "le fait", "tout ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 102, "end": 105}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_104", "text": "éco-paysanne, agro-paysanne, etc., heu... je pense à elle, parce que bah je l'ai vue la semaine dernière en conférence et qu'elle fait une tournée en France et c'est quelqu'un qui... qui a fait énormément de choses, elle est... elle doit avoir 60 ans maintenant, donc elle a un beau parcours derrière elle. Rappelle-moi son prénom ?", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le fait", "le fait", "la semaine dernière", "une tournée", "60 ans", "un beau parcours", "son p<PERSON><PERSON><PERSON>", "le fait", "le fait", "le fait", "le fait", "le fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 103, "end": 105}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_105", "text": "Est -ce qu'on est obligés d'avoir de l'eau potable dans nos toilettes ? Non. Oui, mais quand on a très très soif et y'a du monde à la maison souvent on va aux toilettes.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["l'eau potable", "nos toilettes", "du monde", "la maison"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 104, "end": 108}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_106", "text": "C'est vrai, c'est vrai, j'oubliais ça, parce que tout le monde peut pas aller de la cuisine, donc heu... en effet, on doit avoir une multiplicité. Heu...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["en effet", "tout le monde", "tout le monde", "le monde", "tout le monde", "tout le monde", "tout le monde", "la cuisine", "une multiplicité", "le monde", "tout le monde", "le monde", "le monde", ", c'", "tout le monde", "tout le monde", "tout le monde", "tout le monde", "tout le monde", "tout le monde"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 105, "end": 107}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_107", "text": "<PERSON><PERSON> oui, pour aller plus loin, finalement, finalement, ça rejoint un peu ce que tu disais, est-ce qu'y'a pas des sociétés qui vont être amenées à disparaître parce qu'elles sont en fait néfastes pour l'humanité ? Oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["un peu ce", "un peu ce", "un peu ce", "des sociétés", "en fait", "des sociétés", "un peu ce", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 106, "end": 108}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_108", "text": "Enfin bon... Et est-ce que ce serait le rôle de l'État, du coup ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", "que ce", "du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", du coup", "que ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 107, "end": 109}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_109", "text": "<PERSON> vois, si tu dis... si tu dis : j'interdis un fabricant qui utilise pas de... qui utilise des énergies fossiles, le problème, c'est qu'aujourd'hui, à ce moment-là, je sais pas, mais sinon, ça vient...", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce moment", "énergies fossiles", "énergies fossiles", ", je", "le problème", "le problème", "un fabricant", "des énergies fossiles", "le problème", ", c'est qu'aujourd'hui, à ce moment", "le problème", "le problème", ", c'", "Le problème", "le problème", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 108, "end": 110}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_110", "text": "Parce que par exemple, que le... enfin pour reprendre ce que tu dis, c'est heu... par exemple, que l'essence soit chère, bah en soi, c'est pas forcément plus mal, parce que tu vas réduire tes déplacements. Bah c'est ça, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["par exemple", "c'est heu", "C'est ça", "C'est ça", "C'est ça", "bah en soi", "tes déplacements", "Bah c'est ça", "Bah c'est ça", ", c'", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 109, "end": 111}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_111", "text": "Sauf que bah quand t'arrives déjà pas à te nourrir, heu... bah ça veut dire que tu peux pas aller travailler, donc en fait... Bah c'est ça, et puis les gens, ils ont pas forcément le choix.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "C'est ça", "C'est ça", "en fait", "les gens", "les gens", "les gens", "Bah c'est ça", "puis les gens", "le choix", "Bah c'est ça", "les gens", "les gens", "le choix", "les gens", "les gens", "les gens", "les gens", "C'est ça", "le choix", "le choix", "le choix", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 110, "end": 112}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_112", "text": "… socialement, ça pose des questions, donc ça dépend toujours par quelle lentille tu regardes, le format environnemental, social, etc. Et heu... et du coup, bah est-ce qu'il faudrait avoir un salaire heu... le même salaire que... <PERSON><PERSON><PERSON> . Le salaire unique ? Est-ce que y'aurait un salaire minimum ? Oui, y'a un salaire minimum.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 2.0, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", "et du coup", "quelle lentille", "Et du coup", "du coup", "du coup", "Et du coup", "un salaire", "du coup", "quelle lentille", "le format environnemental", "du coup", "un salaire heu", "Le salaire unique", "un salaire minimum", "un salaire minimum", "un salaire minimum", ", socia", "des questions", "du coup", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 111, "end": 117}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_113", "text": "Enfin y'a un salaire minimum, y'en a un aujourd'hui, mais le fait est que... <PERSON>pr<PERSON>, c'est... bon voilà, c'est plus une entreprise, là. C'est la... c'est la société dans son ensemble.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "la société", "Le fait", "une entreprise", "la société", "le fait", "une entreprise", "une entreprise", "la société", "un salaire", ", c'est...", "un salaire minimum", "un salaire minimum", "un salaire minimum", "le fait", "son ensemble", "une entreprise", "le fait", "une entreprise", "une entreprise", "le fait", "une entreprise", ", c'", "le fait", "une entreprise", "une entreprise", "le fait", "la société", "la société", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 112, "end": 114}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_114", "text": "Donc soit tu le contrains, oui, c'est ce qui se passe heu... bah t'as les gens, c'est pour ça que t'as les médecins roumains qui viennent en France, les Français qui partent en Suisse, heu... aux États-Unis.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le co", "c'est ce", "les gens", "les gens", "les gens", "Donc soit tu le contrains", "les gens", "les médecins roumains", "les Français", "les gens", "les gens", ", c'", "les gens", "les gens", "<PERSON><PERSON>, c'est ce", "<PERSON><PERSON>, c'est ce", "les gens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 113, "end": 115}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_115", "text": "Ah bah oui, oui, c'est sûr. Donc je disais quelque chose, mais honnê<PERSON>, je... je savais pas si... si on s'était compris.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["quel<PERSON> chose", ", je", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", ", c'", "quel<PERSON> chose", "Ah oui", "quel<PERSON> chose"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 114, "end": 116}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_116", "text": "<PERSON><PERSON>, bah si c'est partout dans le monde la même chose, heu... bah t'as plus le choix, mais... Et puis sans aller dans l'extrême de tout légiférer, on n'a pas non plus en France une culture où on accepte facilement qu'on nous dise ce qu'on doit faire, quoi. <PERSON><PERSON>, c'est ça. On a quand même nos principes. Est -ce que ça s'oppose un peu à la liberté individuelle, quoi, les gens, ils mettent ça un peu en avant. Oui, bien sûr. Bien sûr.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 83, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 2, "side_b": 0, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "le monde", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "que ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "les gens", "les gens", "les gens", "le choix", "la liberté individuelle", "les gens", "<PERSON><PERSON>, c'est ça", "les gens", "la même chose", "le choix", "une culture", "<PERSON><PERSON>, c'est ça", "nos principes", "un peu à la liberté individuelle", "les gens", "le monde", "<PERSON><PERSON>, c'est ça", ", c'est ça", "le monde", "le monde", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", ", c'", "les gens", "les gens", "<PERSON><PERSON>, c'est ça", "les gens", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça", "le choix", "le choix", "le choix"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 115, "end": 122}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_117", "text": "<PERSON>s là, c'est pour ça que là, conc... concrètement, je pourrais dire : bah oui, là, il faut légiférer, c'est quand même super important, etc., et puis dans les faits, en effet, individuellement, « mais... oh pourquoi on m'oblige à faire ça ? ». C'est... c'est... évidemment que c'est une question très complexe, parce que si y'avait qu'une solution, on l'aurait déjà inventée.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["en effet", ", je", "Mais là, c'est pour ça", "puis dans les faits", "une solution", ", c'", "Ah oui"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 116, "end": 119}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_118", "text": "Pour info, il vous reste deux minutes sur la légitimité, donc si vous pouvez noter ce que vous avez... Yes. Non, mais là, on est en train de tout révolutionner.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la légitimité", "La légi", "la légitimité", "la légitimité", "la légitimité", "deux minutes", "la légitimité", "la légitimité", "la légitimité", "deux minutes", "deux minutes", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 117, "end": 119}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_119", "text": "<PERSON><PERSON>, sur heu... sur les profits, etc. machin, oui. «", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["les profits"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 118, "end": 120}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_120", "text": "Je vois pas trop, « garants bén<PERSON> » ? Bah en fait, c'est que t'as vraiment une... c'est pas... c'est pas forcément que le gérant qui... tu vois, t'as... t'as un organisme avec plusieurs administrateurs qui sont garants que on applique heu... ce qu'on avait décidé, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 47, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["... ce", "en fait", "« G<PERSON><PERSON> bénévoles", "« garants bénévoles", "vraiment une... c'est pas... c'est pas forcément que le gérant", "un organisme", "plusieurs administrateurs", ", c'", "... ce", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 119, "end": 121}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_121", "text": "Du coup, oui, qu'il soit... qu'il ait autant de pouvoirs heu... enfin que ce soit un contre-pouvoir au... au dirigeant, quoi. Bah j'imagine, oui. C'est comme ça que je le perçois. D'accord. « Clients ». Je regarde juste un peu. « Concurrents ».", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", "que ce", "Comme ça", "du coup", "du coup", "du coup", "du coup", "de pouvoirs heu", "... au dirigeant", "« Concurrents", "Comme ça", "comme ça", "du coup", "comme ça", "comme ça", "que ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 120, "end": 128}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_122", "text": "Alors ça, c'est un vrai problème, hein, parce que du coup, heu... c'est ce qu'on disait, enfin je me dis heu... en fait, en fonction de nos propres objectifs, là, qu'on a mis en haut, une bonne boîte, si c'est une boîte qui fait toujours mieux, bah tu vois, tu mettais par exemple « innovante », etc., tu vois, l'innovation, ça peut être dans le bon sens, y'a un besoin, et ça peut être « bah l'autre il a fait ça, donc moi, je vais innover, mais du coup, je vais créer quelque chose qui... qui a pas une plus-value » et comment on se positionne envers les autres. « Communauté locale, créanciers » . Ah oui, ça, c'est important aussi, est-ce que je vais heu... je vais donner un prêt à une entreprise qui détruit la planète ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 139, "thematic_indicators": {"performance_density": 0.7194244604316548, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "une boîte", "par exemple", "<PERSON><PERSON> moi", "une boîte", "une boîte", "bon sens", "du coup", "une entreprise", "c'est ce", "quel<PERSON> chose", ", je", "mais du coup", "parce que du coup", "parce que du coup", "le bon sens", "quel<PERSON> chose", ", hein", "une entreprise", "parce que du coup", "une boîte", "du coup", "du coup", "une entreprise", "du coup", "en fait", ", hein", "du coup", "quel<PERSON> chose", "nos propres objectifs", "une bonne boîte", "le bon sens", "un besoin", "donc moi", "quel<PERSON> chose", "Communauté locale", ", créanciers", "un prêt", "une entreprise", "la planète", "mais du coup", "la planète", "une entreprise", "quel<PERSON> chose", "une entreprise", "<PERSON><PERSON> du <PERSON>", "une boîte", "une entreprise", "quel<PERSON> chose", ", c'", "du coup", "une boîte", "une entreprise", "quel<PERSON> chose", "une entreprise", ", en fonction", "Ah oui", "bon sens", "quel<PERSON> chose", "une boîte", ", hein", "en fait", "<PERSON><PERSON> du <PERSON>", "<PERSON><PERSON> du <PERSON>", ", ça", "mais du coup"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 121, "end": 126}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_123", "text": "Tu vois, nous, on se posait la question de... à l'IAE, parce que <PERSON> le Master entrepreneuriat, donc qui est en train d'être remodelé, mais peu importe, bah est-ce qu'on accepte tout type de projet ? Ou est-ce que heu... bah il faut a minima que ce soit pas une activité", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 52, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la question", "la question", "la question", "la question", "la question", "la question", "que ce", "la question", "la question", "La question", "la question", "la question", "La question", "la question", "... à l'IAE", "le Master", "tout type", "la question", "la question", "que ce"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 122, "end": 124}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_124", "text": "<PERSON> vois, c'est... c'est d'où émane la... moi je trouve ça assez intéressant de se dire : les banques, qui sont... qui sont des pollueuses... un des premiers facteurs de pollution, c'est les banques, et beaucoup de gens, d'ailleurs, ont pas conscience de ça. Oui, bah moi ... Parce qu'en fait, l'argent que tu mets à la banque, heu... eux, ils s'en servent pour des projets heu... dégueulasses, en fait.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", en fait", ", c'est...", "en fait", "la banque", "la banque", "ça assez intéressant", "les banques", "... un", "premiers facteurs", "la banque", "des projets heu", "... d<PERSON><PERSON><PERSON><PERSON>", ", en fait", "les banques", ", c'", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 123, "end": 127}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_125", "text": "Et donc c'est un des facteurs les plus polluants sans qu'on s'en rende compte. <PERSON><PERSON>, qui financent heu... D'accord.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["facteurs les plus polluants"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 124, "end": 126}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_126", "text": "<PERSON><PERSON>, ta bouteille en plastique, finalement, tu sais quoi, au pire heu... je dis pas qu'il faut s'en foutre, hein. <PERSON><PERSON> <PERSON><PERSON>, si tu mets sur ton livret A, heu... officiellement, c'est censé financer la construction des HLM, typiquement.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["c'est ce", ", hein", ", hein", "ta bouteille", "ton livret", "la construction", ", c'", ", hein"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 125, "end": 127}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_127", "text": "Et puis avec leurs trucs heu... livret développement durable. Là, ils te font croire que, tu sais que du coup, ton argent va tellement aider, pas du tout. Mais heu... moi je kiffe bien cette carte. « Défense des droits des conso, environnement ».", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": [", environnement", "du coup", "du coup", "du coup", "du coup", "du coup", "puis avec leurs trucs heu", "ton argent", "cette carte", "« Défense", "du coup"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 126, "end": 131}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_128", "text": "« Environnement ». « Employés », on en a pas mal parlé.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["pas mal", "« Environnement", "« Employés", "« Employés"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 127, "end": 129}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_129", "text": "Non. Parce que tu vois, ça pourrait être une boîte, les médias.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une boîte", "une boîte", "une boîte", "une boîte", "les médias", "les médias", "les médias", "les médias", "les médias", "une boîte", "une boîte", "une boîte", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 128, "end": 130}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_130", "text": "Ça pourrait être une boîte légitime, les médias, tu vois. « ONG ». Je vous propose qu'on coupe pour le temps de la légitimité. « Partenaires, socia... ».", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 28, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le temps", "le temps", "une boîte", "une boîte", "une boîte", "la légitimité", "La légi", "la légitimité", "la légitimité", "une boîte légitime", "une boîte légitime", "une boîte", "une boîte légitime", "la légitimité", "les médias", "les médias", "la légitimité", "les médias", "les médias", "le temps", "la légitimité", ", socia", "la légitimité", "les médias", "une boîte", "le temps", "une boîte", "une boîte", "le temps", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 129, "end": 133}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_131", "text": "Donc il est temps de passer au prochain, c'est ça ? Critères de performance...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "C'est ça", "C'est ça", "Donc il est temps de passer au prochain, c'est ça", ", c'est ça", ", c'est ça", ", c'", "il est", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 130, "end": 132}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_132", "text": "Mais pas plus de dix minutes, s'il vous plaît. Je vous demande de tenir ce temps, s'il vous plaît.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce temps", "Mais pas plus de dix minutes", "ce temps"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 131, "end": 133}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_133", "text": "<PERSON><PERSON>, mais le fait est que les médias aujourd'hui, ils sont pas... enfin ils sont politiquement heu... orientés. <PERSON><PERSON>, je suis d'accord . On prend deux minutes ? Apparemment, c'est chrono. Allez, c'est ça, on les utilise pas.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le fait", ", je", "le fait", "C'est ça", "C'est ça", "les médias", "les médias", "C'est ça", "le fait", "deux minutes", "les médias", "les médias", "deux minutes", "les médias", "le fait", "deux minutes", "Apparemment, c'est chrono", ", c'est ça", "le fait", ", c'est ça", ", c'", "le fait", "le fait", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 132, "end": 138}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_134", "text": "Vous avez pas du tout utilisé les cartes ? Non.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les cartes", "Les cartes", "tout utilisé les cartes", "les cartes", "les cartes", "les cartes", "Les cartes"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 133, "end": 135}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_135", "text": "Vous êtes trop forts sans les cartes. Au bout d'un quart d'heure, de la même façon... Trois, travail collectif.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["Travail collectif", "un quart", "travail collectif", "les cartes", "Les cartes", "les cartes", "un quart", "la même façon", "un quart", "les cartes", "les cartes", "Les cartes"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 134, "end": 136}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_136", "text": "... je vais vous donner des cartes « critères de performance », c'est la même logique, vous les utilisez, vous les utilisez pas, y'en a un certain nombre dessus, celles que vous utilisez, vous les mettrez à la fin à côté de votre feuille et vous pourrez en rajouter. Je vous donne l'info tout de suite, comme ça après je vous embête pas dans votre heu... discussion.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 67, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "Comme ça", "la fin", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", ", vous", "un certain nombre", "votre feuille", "Comme ça", "des cartes", "un certain nombre", ", celles", "la fin", "votre feuille", "tout de suite", "votre heu", "... discussion", "comme ça", ", ce", ", c'", "un certain nombre", "comme ça", "comme ça", "des cartes", "la fin", "un certain nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 135, "end": 137}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_137", "text": "Non, c'est bon. Tu avais bien relancé ? Parce que j'avais coupé. Est", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 136, "end": 140}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_138", "text": ", comme le... Comme pour la légitimité, enfin à peu près. <PERSON><PERSON>, le critère de performance, c'est : qu'est-ce que heu... là, on est plutôt en interne, si je peux le dire comme ça de l'entreprise, c'est-à-dire comment l'entreprise va considérer qu'elle est performante sur son marché vis-à-vis de l'environnement ? En fait, c'est aussi un peu lié aux parties prenantes.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 1.0}, "noun_phrases": ["la légitimité", "La légi", "parties prenantes", "la légitimité", "la légitimité", "la légitimité", "parties prenantes", "parties prenantes", "Comme ça", "en fait", "la légitimité", "la légitimité", "la légitimité", "Comme ça", "le critère", "comme ça", "son march<PERSON>", "vis-à-vis de l'environnement", "parties prenantes", ", c'", "comme ça", "comme ça", "en fait", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 137, "end": 139}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_139", "text": "D'ailleurs, les collègues l'ont fait de façon un peu plus systémique. C'est les critères de réussite ? C'est les critères de réussite.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "les collègues", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 138, "end": 141}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_140", "text": "Comment on considère qu'elle est... donc performante et qu'elle est heu... utile, qu'elle ait r<PERSON><PERSON>, qu'elle marche, en fait, qu'elle peut et qu'elle est, surtout, peut-être je vais dire ce mot-là, « pé<PERSON>ne », en fait ? Du coup, c'est des critères heu... c'est des critères mesurables forcément ou...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 0.6}, "noun_phrases": ["C'est des critères", "du coup", ", en fait", "du coup", "du coup", "des critères", "du coup", "en fait", "du coup", ", en fait", "qu'elle marche", "ce mot-là", "des critères", ", c'", "du coup", "en fait", "des critères"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 139, "end": 141}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_141", "text": "Allez. Go, go, go !", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 5, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", go", ", go"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 140, "end": 142}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_142", "text": "En 2050 ? Si on se projette en 2050 ? Oui ? <PERSON><PERSON>, non.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 141, "end": 145}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_143", "text": "Toi t'en enlèverais ? Heu", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 5, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 142, "end": 144}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_144", "text": "<PERSON> vois, dans l'idée de ce... de ce qu'on disait tout à l'heure, le but, c'est pas toujours produire plus pour vendre plus, et on... et on produit plus intelligemment, derri<PERSON>, pour pouvoir également heu... r<PERSON><PERSON><PERSON> que<PERSON><PERSON> chose qui peut l'être. Donc c'est plus forcément la productivité à tout prix qui heu... qui l'emporte.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le but", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "qui heu", "le but", "quel<PERSON> chose", "tout prix", "quel<PERSON> chose", ", c'", "quel<PERSON> chose", "quel<PERSON> chose", "tout à l'heure"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 143, "end": 145}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_145", "text": "Don<PERSON> celui-<PERSON><PERSON>, je pense que... Et du coup, par rapport à ce que tu viens d'évoquer, je me dis, est-ce qu'un critère de performance, ça serait pas... je sais pas si ça... je sais pas comment le dire, mais en gros, que justement l'entreprise disparaisse parce que justement, y'a plus besoin de cette mission ? Ma mission... alors je... je prends un exemple grossier, hein, mais ma mission, c'est de... c'est d'agir contre la faim dans le monde, bah y'a plus personne qui a faim dans le monde, je n'ai plus lieu d'exister.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 93, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", ", je", "le monde", "et du coup", "Et du coup", ", hein", "du coup", "du coup", "Et du coup", "du coup", ", hein", "du coup", "le monde", "qu'un critère", "cette mission", "Ma mission", "un exemple grossier", "ma mission", "la faim", "le monde", "le monde", "un critère", ", c'", "du coup", ", hein", "un exemple", "cette mission", "un critère", "un critère", ", ça", "un critère"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 144, "end": 146}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_146", "text": "Aujourd'hui, oui, les boîtes publiques qui disparaissent, c'est parce qu'elles n'ont plus lieu d'être, justement, on a rempli... on a rempli la mission. La mission, oui, c'est ça. Voilà. Mais pour les boîtes publiques. Je serais d'accord avec toi pour ça.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 41, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "les boîtes", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "la mission", "<PERSON><PERSON>, c'est ça", ", c'est ça", "les boîtes publiques", "la mission", "La mission", "oui, c'est ça", "Mais pour les boîtes publiques", "<PERSON><PERSON>, c'est ça", ", c'est ça", ", c'", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 145, "end": 150}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_147", "text": "Mais du coup, par contre, en effet, du coup, on peut redescendre d'un étage, donc elle a rempli sa mission, bah ça serait quoi les critères qui ferait qu'elle aurait rempli sa mission ? Parce que du coup, ce serait des critères de performance... tu vois ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 47, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "du coup", "en effet", "mais du coup", "parce que du coup", "parce que du coup", "parce que du coup", "du coup", "du coup", "des critères", "du coup", ", du coup", "du coup", "mais du coup", "les critères", ", ce", "<PERSON><PERSON> du <PERSON>", "un étage", "sa mission", "les critères", "sa mission", "des critères", "du coup", ", du coup", "<PERSON><PERSON> du <PERSON>", "des critères", "<PERSON><PERSON> du <PERSON>", "mais du coup"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 146, "end": 148}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_148", "text": "Au final, il demande une rentabilité, quoi. <PERSON><PERSON>, si on peut ess... éviter de... parce que là, je pense qu'on a beaucoup de choses à dire, mais on sait pas forcément dans quel domaine aller, on peut peut-être essayer d'identifier genre heu... une... des critères pour des produits, des critères pour des... des services et des critères pour des personnes, pour essayer... parce que c'est vrai que si on sait pas trop où aller.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 74, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des produits", ", je", "des critères", "des services", "... un", "une rentabilité", "quel domaine", "des produits", ", des critères", "des... des services", "des critères", "des personnes", "des produits", "des services", "au final", "des critères"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 147, "end": 149}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_149", "text": "Ah oui, bah l'indice de réparabilité, là, qu'ils mettent. Voilà, par exemple, exactement.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["par exemple", "Ah oui"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 148, "end": 150}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_150", "text": "<PERSON><PERSON>, on peut mettre ça. Je sais pas si on attend le retour de Stéphanie ou... oui, un... un indice de réparabilité élevé, oui. T'as pas des produits jetables, mais des produits qui durent dans le temps. <PERSON><PERSON>, c'est... c'est super, ce que tu dis, c'est limiter le... limiter le nombre de produits jetables.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des produits", "le temps", "le temps", "le... limite", ", c'est...", "... un", "le temps", ", ce", "des produits", "le retour", "<PERSON><PERSON><PERSON><PERSON> ou", "oui, un... un indice", "réparabilité élev<PERSON>", "des produits jetables", "des produits", "le temps", "le nombre", "produits jetables", ", c'", "Le nombre", "le temps", "le nombre", "le nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 149, "end": 154}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_151", "text": "<PERSON><PERSON>, c'est ça, ne pas faire du jetable, enfin de l'obsolescence programmée, on en revient, hein, mais... après, oui, si on met ça sous forme de cri... de critères de... critères de performance, donc c'est des indices, quoi, c'est quelque chose qui serait mesurable, en fait, chiffrable. Donc heu... un indice de réparabilité, le... le bilan carbone.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "quel<PERSON> chose", "le bilan carbone", "C'est ça", ", en fait", "<PERSON><PERSON>, c'est ça", "obsolescence programmée", "C'est ça", "quel<PERSON> chose", ", hein", "du jetable", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est quelque chose", "en fait", ", hein", "quel<PERSON> chose", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "quel<PERSON> chose", "... un", ", en fait", "<PERSON><PERSON>, c'est ça", ", c'est ça", "quel<PERSON> chose", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", "enfin de l'obsolescence programmée", "... crit<PERSON>", "quoi, c'est quelque chose", "quel<PERSON> chose", ", c'", "<PERSON><PERSON>, c'est ça", "quel<PERSON> chose", "... crit<PERSON>", "quel<PERSON> chose", ", hein", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 150, "end": 152}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_152", "text": "On a... on a simplement des... des doléances par l'État, « ce serait bien de faire ceci ou cela » et finalement, c'est pas respecté. Donc qui va surveiller, quelles sont les conséquences ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des doléances", "ce serait bien de faire ceci ou cela » et finalement, c'est pas respecté", "les conséquences", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 151, "end": 153}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_153", "text": "Voilà, donc on considère que jusqu'à 100, si tu gagnes 100 fois plus que... ce qui est déjà énor<PERSON>, heu... c'est la limite. Par contre, heu... si tu dépasses 100, ça devient vraiment inquiétant et c'est pas normal.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["... ce", "à 100", "... ce", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 152, "end": 154}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_154", "text": "Voilà, celui qui était dans une boîte spécifique et qui gagnait 15 000 euros, et donc par rapport à celui-là, évidemment, il gagnait pas 350 fois, sauf que c'est... c'est biaisé, en fait. Et donc on a interrogé les salariés lambda, genre un caissier, un technicien et tout ça, ils étaient tous... il y en avait une, notamment, elle avait heu... 25 ans d'ancienneté, elle était au SMIC. Toujours au SMIC, oui, bah oui. 25 ans d'ancienneté. C'est... c'est beaucoup, hein.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 81, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une boîte", "une boîte", "une boîte", "tout ça", ", en fait", ", hein", "une boîte", "en fait", ", hein", ", en fait", ", ce", "une boîte", "les salariés lambda", "15 000 euros", "350 fois", "les salariés lambda", "un caissier", "un technicien", "tout ça", "25 ans", "Toujours au SMIC", "25 ans", "tout ça", "une boîte", "tout ça", "Ah oui", "une boîte", ", hein", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 153, "end": 158}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_155", "text": "Et t'as le... t'as le PDG qui doit être dans les millions, donc je sais même pas combien de fois ça fait plus, heu... Voilà. Donc on était partis tous les deux sur un critère de heu... Réparabilité. C'est ça, tu parlais de ça ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 45, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "tous les deux", "C'est ça", "C'est ça", "le PDG", "les millions", "un critère", "C'est ça", "un critère", "un critère", "un critère"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 154, "end": 157}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_156", "text": "Ah oui, donc ça, c'est produits, c'est ça ? Vous vouliez faire des catégories ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 15, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "C'est ça", "C'est ça", ", c'est ça", ", c'est ça", "des catégories", ", c'", "Ah oui", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 155, "end": 157}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_157", "text": "Ah bah oui, oui , oui. Je mets quoi ? «", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Ah oui"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 156, "end": 160}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_158", "text": "<PERSON>eu de maladies, de turn-over. Ça, c'est pour l'entreprise.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["Peu de maladies", ", c'est pour l'entreprise", ", c'"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 157, "end": 159}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_159", "text": "<PERSON><PERSON>, et je vais mettre deux points, et puis je vais mettre les... Bien-être au travail. Et donc sachez que sur TikTok, vous pouvez avoir une ordonnance, une prescription médicale pour heu... pour pas aller bosser.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 36, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", vous", "deux points", "une ordonnance", "une ordonnance", "une prescription"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 158, "end": 160}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_160", "text": "Ah oui, d'accord. Et donc tu cliquais gentiment, tu disais « qu'est-ce que tu penses avoir ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", d'accord", ", d'accord", "Ah oui"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 159, "end": 161}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_161", "text": "À quoi ça ressemble, est-ce que c'est une grippe, le <PERSON><PERSON>, tout ça ? Heu... pendant combien de temps tu penses que tu dois être arrêté ? Un jour, deux jours, trois jours, cinq jours ? ». Ah oui, c'est sur demande, quoi. Voilà.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", tout ça", "le co", "tout ça", ", tout ça", "le Covid", "le Covid", "le Covid", ", tout ça", "tout ça", ", c'", "cinq jours", ", tout ça", "le Covid", "tout ça", "Un jour", "deux jours", ", cinq jours", "tout ça", "Ah oui"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 160, "end": 166}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_162", "text": "Et donc il a un document qu'il envoie à l'Assurance maladie. Et donc le journaliste disait « c'est gros, quand même », bon, c'est un vrai faux.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", c'", "un document", "le journaliste", "le journaliste", "Et donc le journaliste"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 161, "end": 163}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_163", "text": "<PERSON><PERSON> quand ils l'ont eu entre les mains, les gens de l'Assurance maladie se sont dit « y'a un truc qui va pas » et franchement, il est super bien fait, tout est super bien fait. Et le journaliste lui dit « mais ça, je peux pas avoir... je peux pas être indemnisé avec ce document ? Vous dites qu'il est faux ». C'est normalement non.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce document", ", je", "un truc", "un truc", "un truc", "un truc", "un truc", "mais ça", "mais ça", "en fait", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "<PERSON><PERSON>a", "le journaliste", "les mains", "les gens", "un truc", "le journaliste", "ce document", "les gens", "il est", "les gens", "en fait", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 162, "end": 166}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_164", "text": "Et donc le journaliste l'a quand même envoyé à la CPAM et il a été indemnisé grâce à ce truc qu'il a eu sur Internet. Sérieux ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce truc", "le journaliste", "le journaliste", "Et donc le journaliste", "la CPAM", "grâce à ce truc", "ce truc"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 163, "end": 165}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_165", "text": "Eh bah... <PERSON><PERSON> v<PERSON>. Alors je pense que ça transparaît, comme on est en train de le dire, sur le bien-être des salariés, mais le fait que les arrêts maladie, enfin les arrêts de travail, c'est vraiment ceux qui seraient fictifs ou etc., enfin vraiment indépendants... dépendants du fait que voilà. O<PERSON>, mais apr<PERSON>, apr<PERSON>, après, pourquoi les gens se mettent en... après, tu peux être en arrêt de travail parce que t'es malade, mais t'en as aussi qui se mettent... qui se sentent mal au travail. Ils sont pas bien. Mais c'est clair.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 94, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le fait", "le bien", "le fait", "que ça", "les gens", "les gens", "les gens", "le fait", "les gens", "les gens", "les gens", "le fait", "le fait", ", c'", "les gens", "le bien-être", "le fait", "les arrêts", "les gens", "le fait", "le bien-être", "le bien-être", "les gens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 164, "end": 169}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_166", "text": "Voilà, donc ça, si tu... si tu fais attention au bien-être de tes salariés... Tout à fait. C'est des choses évitables. Complètement.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des choses", "des choses", "des choses", "des choses", "des choses", "des choses", "Voilà, donc ça", "tes salar<PERSON>", "C'est des choses évitables", "des choses", "des choses"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 165, "end": 168}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_167", "text": "Et puis toutes les... tous les métiers par exemple manutentionnaires où on fait pas attention... <PERSON><PERSON>, c'est ça, où t'es toujours plus dans le chiffre.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "par exemple", "le chiffre", "le chiffre", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", ", c'", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 166, "end": 168}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_168", "text": "...y'a des études qui ont montré aussi justement que amener plus de femmes dans ces métiers-là fait que les heu... les dirigeants se posent davantage de questions sur la santé, parce qu'ils partent du principe qu'elles sont moins capables physiquement de faire et en fait, eh bah c'est bénéfique pour tout le monde. Et heu... et du coup, moins aussi d'arrêts maladie pour les hommes, qui du coup pesaient... enfin transportaient des choses bien trop lourdes alors que il existe des outils pour les aider, par exemple.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 87, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["par exemple", "des choses", "les dirigeants", "des choses", "du coup", "des choses", "des choses", "tout le monde", "tout le monde", "le monde", "tout le monde", "et du coup", "Et du coup", "des choses", "du coup", "du coup", "Et du coup", "tout le monde", "les hommes", "et en fait", "les hommes", "du coup", "tout le monde", "en fait", "du coup", "le monde", "tout le monde", "des choses", "le monde", "le monde", "des études", "ces métiers", "les heu", "les dirigeants", "la santé", "tout le monde", "du coup", "moins aussi d'arrêts maladie", "les hommes", "choses bien trop lourdes", "des outils", "des choses", "tout le monde", "tout le monde", ", moi", "tout le monde", "des choses", "en fait", "tout le monde", "tout le monde", "les heu"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 167, "end": 169}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_169", "text": "<PERSON><PERSON> en effet, y'a la... y'a la santé heu... mentale, comment tu te sens au travail, puis physique. <PERSON><PERSON>, y'a le physique aussi, c'est sûr, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["en effet", ", c'", "la santé", "la santé heu"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 168, "end": 170}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_170", "text": "<PERSON> <PERSON> <PERSON>, de... <PERSON><PERSON>, oui, possibilité d'évolution , oui, c'est sûr. <PERSON><PERSON>, c'est clair que... c'est ce que je disais tout à l'heure, à voir cette femme, là, qui travaille depuis presque 30 ans dans une boîte...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une boîte", "une boîte", "une boîte", "c'est ce", "une boîte", "une boîte", ", c'", "cette femme", "presque 30 ans", "une boîte", "une boîte", "tout à l'heure"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 169, "end": 173}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_171", "text": "<PERSON><PERSON>, bah oui, après, est-ce que c'est subi ou est-ce que... enfin... C'est inconcevable, quand même. Non, mais tu peux... tu peux rester sur le même poste et avoir une meilleure rémunération pour heu... pour ton ancienneté, surtout. Oui. Comme tu dis, il faut le vouloir. Y'a des gens qui sont très bien dans ce qu'ils font. Bien sûr, exactement.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 60, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le même poste", "une meilleure rémunération", "ton ancienneté", "des gens", "Ah oui", "des gens", "des gens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 170, "end": 176}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_172", "text": "Voilà, faut pas que ce soit... tu vois. <PERSON><PERSON><PERSON> que d'ailleurs, on en arrive à des situations dramatiques, me semble-t-il, où comme c'est bien vu de monter dans les grades, eh bah on se retrouve avec des managers hyper toxiques, parce qu'en fait, en vrai, ils avaient pas envie d'être là, mais ils voulaient un poste à responsabilités et ils ont pas les compétences, quoi. Bien sûr. Tout à fait.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["que ce", "en fait", "des situations dramatiques", "les grades", "des managers", "un poste", "les compétences", "que ce", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 171, "end": 175}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_173", "text": "Donc heu... donc j'ai mis « possibilité d'évolution si souhaitée, de formation », « d'évolution de salaire » aussi ? Oui. En gros, bah après, je vais mettre « de reconnaissance », en fait.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", je", ", en fait", "en fait", ", en fait", "« d'évolution", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 172, "end": 175}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_174", "text": "Enfin tout ça, je trouve que c'est... ça va avec. <PERSON><PERSON>, oui, ça va... ça va de soi, oui. <PERSON><PERSON>, que le salarié ne soit plus qu'un... ne soit plus qu'un simple pion, quoi, en fait. <PERSON><PERSON>, ça, c'est vraiment changer... changer de mentalité, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 45, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le salarié", ", je", "tout ça", ", en fait", "en fait", ", en fait", "tout ça", ", c'", "tout ça", "tout ça", "le salarié", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 173, "end": 177}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_175", "text": "<PERSON><PERSON>, ça me fait exactement penser à ce que je suis en train d'analyser, mes entretiens, moi je travaille sur les étudiants et les étudiantes entrepreneurs, du coup, ils ont vraiment cette volonté heu... de bien-être de leurs salariés, de créer une entreprise qui va... alors après, là ils sont en début de projet, donc c'est un peu... la volonté, voilà. Et ceux qui ont commencé, qui se confrontent au réel, là où on leur dit « non, non, mais... », bref.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 82, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "du coup", "une entreprise", "une entreprise", "du coup", "du coup", "une entreprise", "du coup", ", du coup", "du coup", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "du coup", "mes entretiens", "les étudiants", "et les étudiantes entrepreneurs", "cette volonté heu", "leurs salariés", "une entreprise", "<PERSON>t ceux", "une entreprise", "les étudiants", ", du coup", "<PERSON>t ceux", ", moi", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 174, "end": 176}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_176", "text": "Donc y'aurait « produits », y'aurait « services ». On avait dit « services » aussi, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Donc y'aurait « produits", "« services"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 175, "end": 177}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_177", "text": "Non, complètement. Donc les produits, critère de réparabilité.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 8, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 176, "end": 178}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_178", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, production locale. Je sais pas comment mesurer ça. Le nombre de kilomètres parcourus, que<PERSON><PERSON> chose comme ça.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["quel<PERSON> chose", "quel<PERSON> chose", "Comme ça", "quel<PERSON> chose", "quel<PERSON> chose", "Comme ça", "comme ça", "quel<PERSON> chose", "le nombre", "quel<PERSON> chose", "Oui, production locale", "Le nombre", "quel<PERSON> chose", "comme ça", ", quelque chose", "comme ça", "quel<PERSON> chose", "le nombre", "le nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 177, "end": 181}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_179", "text": "<PERSON><PERSON>, c'est ce que j'allais dire, parce qu'en fait, je pense qu'il faut aussi faire attention à cette... il me semble, hein, à cette notion de local, parce que en gros, si t'es à <PERSON> ou si t'es, je sais pas, dans... vraiment heu... je sais pas moi, le sud-est, est-ce qu'il vaut mieux aller acheter en Italie à la frontière, par exemple, ou à Quimper ? Enfin ou même à l'autre bout de... tu vois ? Oui, non, c'est sûr. Non, mais tu vois, cet aspect de kilomètres, il est... il me semble intéressant. Oui, d'autant plus, parce que y'a des... comment dire ?", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 105, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.5714285714285714}, "noun_phrases": ["par exemple", "c'est ce", ", je", "cette notion", ", hein", "l'autre bout", "l'autre bout", "l'autre bout", "l'autre bout", "en fait", ", hein", ", ce", ", c'", "<PERSON><PERSON>, c'est ce", "l'autre bout", "<PERSON><PERSON>, c'est ce", "le sud-est", "la frontière", "cet aspect", "il est", "cette notion", "l'autre bout", ", hein", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 178, "end": 183}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_180", "text": "Tu vas aller sur certains produits, heu... tu vas en trouver partout, heu... de l'alimentaire notamment, m si tu vas sur des produits un peu plus spécifiques, genre un médicament ou ce genre de choses, t'as pas non plus 150 boîtes qui font ça, donc forcément, heu ... ce... cette notion de kilomètres, voilà, être de plus en plus large.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 60, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des produits", "ce genre", "... ce", "cette notion", "certains produits", "certains produits", "des produits", "des produits", "ce genre", "certains produits", "des produits un peu plus spécifiques", "un médicament", "ce genre", "pas non plus 150 boîtes", "... ce", "cette notion"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 179, "end": 181}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_181", "text": "<PERSON><PERSON>, et à la durée de vie. Ah oui, sur l'ensemble de la durée de vie, oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["la durée", "à la durée", "Ah oui, sur l'ensemble", "la durée", "Ah oui", "la durée"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 180, "end": 182}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_182", "text": "Après, un service en kilomètres parcourus ? Je pose la question.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la question", "la question", "la question", "la question", "la question", "la question", "la question", "la question", "La question", "la question", "la question", "La question", "la question", "la question", ", un service", "la question"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 181, "end": 183}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_183", "text": "Pour moi, un service, si tu fais... je sais pas, du... <PERSON>, ça peut, parce que ça peut être heu... Tu fais du jardinage, j'allais dire.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pour moi", "que ça", ", un service", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 182, "end": 184}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_184", "text": "Bah par exemple, tu fais du jardinage, heu... une femme de ménage pour une entreprise, heu... si elle doit faire une heure de voiture. Ah oui, par rapport au...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "par exemple", "une entreprise", "une heu", "une entreprise", "une heu", "<PERSON><PERSON>, par rapport", "une entreprise", "une heu", "une entreprise", "... un", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "... une femme", "une entreprise", "une heure", "Ah oui"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 183, "end": 185}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_185", "text": "On a beau dire, t<PERSON><PERSON>travail ou... tu peux le dire en parcimonie, mais bon, au final, les gens, après avoir passé deux ans en distanciel, les étudiants étaient super contents de revenir sur les campus aussi, en général, hein. Y'a un juste milieu, je pense. Exactement. C'est pour ça que je pense qu'il faut... c'est un peu ce que je voulais dire tout à l'heure par heu... décision un peu à 360, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 73, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un peu ce", "deux ans", "un peu ce", ", je", ", hein", "un peu ce", "les gens", "les gens", ", hein", "les gens", "les gens", "les gens", "les gens", "un peu ce", "les gens", "les gens", "les étudiants", "deux ans", "les étudiants", "les campus", "un juste milieu", "un peu à 360, quoi", "au final", ", au final", ", hein", "les gens", ", au final", "tout à l'heure"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 184, "end": 189}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_186", "text": "Je regarde mon... mon projet, quoi, je regarde un peu de tous les angles et puis je prends la décision la... la moins pire, quoi, entre guillemets, tu vois, par rapport à tous les avantages que je peux gagner. Heu... oui, complètement.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["mon projet", ", je", "mon projet", "un peu de tous les angles", "la décision", "tous les avantages"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 185, "end": 188}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_187", "text": "Et puis dans les... ça peut être services ou produits, mais heu... par rapport à, comme on disait, à l'adaptation des conditions de travail, en fonction de... des températures, par exemple, etc. Ah oui . Mais en fait, ça reviendrait au bien-être des salariés, en fait. Oui. On peut tirer une carte, hein.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["par exemple", ", en fait", ", hein", "en fait", ", hein", ", en fait", ", en fonction", "de... des températures", "Ah oui", "une carte", ", hein", "en fait", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 186, "end": 192}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_188", "text": "Ou je sais plus, on tire des cartes ou ? Oui, on peut. Elle va tirer pour nous, non ? Non, non, c'est nous. C'est nous.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des cartes", ", c'", "des cartes", "C'est nous"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 187, "end": 192}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_189", "text": "On en tire une ? Oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 6, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 188, "end": 190}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_190", "text": "<PERSON> vois, encore une fois, c'est le juste milieu, c'est se dire que je peux... enfin je vais viser de gagner de l'argent sans que ce soit indécent, tout en respectant ces critères-là. Bah je pense que ça va être le vrai challenge, en fait, de ces prochaines années. Ça va être une question de bon sens que les entreprises vont devoir se... Oui. Et ça rejoint aussi heu... le fait que heu... si je crée quelque chose qui est réparable ou qui va durer plus longtemps, est-ce que c'est pas moins grave que j'ai un coût de production qui soit plus élevé ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 104, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.38461538461538464}, "noun_phrases": ["encore une fois", "bon sens", "Le fait", "quel<PERSON> chose", "le fait", "que ce", ", en fait", "quel<PERSON> chose", "que ça", "encore une fois", "en fait", "le fait", "quel<PERSON> chose", "quel<PERSON> chose", ", en fait", "le fait", "quel<PERSON> chose", "le fait", "quel<PERSON> chose", ", c'", "le fait", "quel<PERSON> chose", "encore une fois", "ces critères", "être le vrai challenge", "bon sens", "les entreprises", "le fait", "quel<PERSON> chose", "un coût", "que ce", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 189, "end": 195}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_191", "text": "Mon produit sera plus cher, OK, mais au final, sur heu... si j'utilise beaucoup 20 ans, au lieu d'en acheter trois, bah comme ton téléphone, au final, à la fin, t'es gagnant.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la fin", "20 ans", "20 ans", "la fin", "Mon produit", "au final", "beaucoup 20 ans", "ton téléphone", ", au final", "la fin", ", au final", "20 ans"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 190, "end": 192}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_192", "text": "<PERSON><PERSON> finalement, heu... tu vois, les coûts opérationnels, on pourrait se dire, le critère de perf... enfin... oui, mais là, ce serait plutôt la négative.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les coûts", "le critère", ", ce", "Coûts opérationnels", "les coûts opérationnels"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 191, "end": 194}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_193", "text": "Enfin j'allais dire, en gros, c'est pas un critère de performance de se dire que ton coût opérationnel est faible, dans le sens où heu... bah si t'as rempli d'autres objectifs, qui amènent le client ou utilisateur final de toute manière à faire quand même des économies sur de la grande échelle, c'est pas si grave.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le sens", "de toute manière", "le sens", "où heu", "le sens", "un critère", ", c'", "le sens", "ton coût opérationnel", "le sens", "d'autres objectifs", "le client", "toute manière", "des économies", "de la grande échelle", "le sens", "le sens", "dans le sens", "un critère", "un critère", "un critère", "le sens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 192, "end": 194}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_194", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, l<PERSON>, on a parlé éventuellement d'évolution de salaire sur le bien-être des salariés. Et là, c'est quoi, cette carte ? C'est plus « croissance des revenus ». Ah non, des revenus de l'entreprise, oui, pardon.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["le bien", ", pardon", "C'est quoi", "cette carte", ", ce", ", c'", "le bien-être", "des revenus", "le bien-être", ", pardon", "le bien-être"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 193, "end": 197}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_195", "text": "Donc croissance des revenus de l'entreprise. Le problème, c'est que tu peux pas dire « je suis écolo », mais te dire « je vais viser 10 % de plus chaque année », quoi.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["le problème", "le problème", "le problème", "le problème", "le problème", ", c'", "des revenus", "chaque ann<PERSON>", "<PERSON><PERSON> croissance", "Le problème", "10 %", "plus chaque année", "10 %", "plus chaque année", "10 %", "plus chaque année", "le problème", "10 %"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 194, "end": 196}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_B_seg_196", "text": "Enfin... si tu... si tu construis des panneaux photovoltaïques et des éoliennes et que tu construis 10 % de plus chaque année, heu... peut-être, je sais pas, mais tu t'appelles Coca Cola et tu veux... tu veux produire 10 % de plus chaque année, c'est contre... c'est pour ça que pour moi, la... la légitimité, c'est un service ou un produit utile à la société. Oui. Bah sauf si tu utilises les moyens... les moyens qu'on a cités pour heu... pour y arriver.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 83, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["la société", "un produit", "pour moi", "la... la légitimité", "la légitimité", "les moyens", "la société", "La légi", "la légitimité", "la légitimité", ", je", "la légitimité", "les moyens", "la société", "la légitimité", "la légitimité", "la légitimité", ", c'", "chaque ann<PERSON>", "10 %", "plus chaque année", "panneaux photovoltaïques et des éoliennes", "10 %", "plus chaque année", "10 %", "plus chaque année", "la société", "les moyens", "les moyens", "10 %", "les moyens", "la société", "la légitimité", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 195, "end": 198}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_197", "text": "Sauf si tu te dis que on... je sais pas, en relocalisant, en... en allant chercher des gens qui sont heu... qui sont proches, en... en capitalisant sur le bien-être des salariés, ça fait qu'y'en a moins qui tombent malades et donc heu... tu as moins, apr<PERSON>, de nouveaux salariés, à en former. À former, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["le bien", "le bien-être", "des gens", "le bien-être", "des gens", "le bien-être", ", de nouveaux salariés", "des gens", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 196, "end": 198}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_198", "text": "Voilà, si... si toutes ces petites... ces nouvelles règles, en gros, qui finalement vont dans... dans le sens du bien-être, de l'environnement et du salarié, ça emmène de la performance contre toute attente, parce que rien ne dit qu'en fait, ça ne fonctionnerait pas. O<PERSON>, mais rien ne dit que ça fonctionnerait aussi, hein.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le sens", ", de l'environnement", "... dans le sens", "... ce", ", hein", "le sens", "que ça", "en fait", ", hein", "le sens", "la performance", "... ce", "le sens", "le sens", "ces nouvelles règles", "le sens", ", de l'environnement", "du salarié", "la performance", "toute attente", ", hein", "le sens", "en fait", "dans le sens", ", ça", "le sens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 197, "end": 199}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_199", "text": "Je crée une boîte ici, je la mets dans des... dans ces conditions-là, enfin finalement que ce soit une expérience heu... scientifique en tant que telle, voir comment cette boîte-là, elle fonctionnerait si jamais je mettais des bonnes conditions, par rapport à une autre boîte heu... c'est vrai que ça fait peur, aujourd'hui, quand on entend parler de l'environnement, du bien-être du salarié, on a l'impression que c'est heu... un principe comme ça pour... pour faire joli, pour... mais on... peut-être que ça marcherait mieux, hein. <PERSON><PERSON>, de toute façon, si tout le monde est conscient, enfin si tu te dis : tout le monde est... a conscience de ces problématiques et qu'ils y font... qu'ils y font gaffe, bah normalement, t'achètes pas un produit heu... qui va contre tes valeurs.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 131, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.3053435114503817}, "noun_phrases": ["un produit", "une boîte", "une boîte", "une boîte", "c'est heu", "tout le monde", "tout le monde", ", je", "le monde", "que ce", "tout le monde", ", hein", "une boîte", "que ça", "Comme ça", "toute façon", "tout le monde", "tout le monde", ", hein", "le monde", "... un", "Comme ça", "comme ça", "tout le monde", "le monde", "le monde", "une boîte", "tout le monde", "une boîte", "comme ça", "comme ça", "du salarié", "une boîte", "ces conditions", "enfin finalement que ce soit une expérience heu", "cette bo<PERSON><PERSON>-là", "des bonnes conditions", "une autre boîte heu", ", du bien-être", "... un principe", "<PERSON><PERSON>, de toute façon", "tout le monde", "tout le monde", "ces problématiques", "un produit heu", "tes valeurs", ", hein", "que ce", "tout le monde", "tout le monde", "tout le monde", "cette bo<PERSON>te"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 198, "end": 200}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_B_seg_200", "text": "Parce qu'aujourd'hui, heu... je sais d'où sont issus un certain nombre de ses produits, c'est pas très loin de là où je suis née, ce sont des gosses qui travaillent là-dedans, donc oui, je peux me dire que je suis une personne horrible, en fait. Oui, mais par exemple, si t'as un concurrent qui arrive et qui dit « nous, on garantit que y'a 0 % de... de mat... ».", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un certain nombre", "ses produits", "par exemple", "un certain nombre", "un certain nombre", ", je", "un certain nombre", ", en fait", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "Mais par exemple", "un certain nombre", "en fait", ", en fait", "un certain nombre", ", ce", ", c'", "un certain nombre", "où sont issus un certain nombre", "ses produits", "un concurrent", "un certain nombre", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 199, "end": 201}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_201", "text": "Je me dis : bon bah à moins qu'on me dise « c'est 10 000 euros au lieu de 100 », là tu te poses plus de questions, mais si c'est heu... 10 % plus cher... bah typiquement, c'est le bio. Aujourd'hui, le bio, ça coûte heu... ça coûte plus cher.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 51, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["c'est heu", ", c'", "10 %", "10 %", "10 %", "10 %", "le bio", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 200, "end": 202}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_202", "text": "Et oui, toute la contradiction, au final. Bah oui. <PERSON> fois, on veut, mais on peut pas, en fait.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", en fait", "des fois", "des fois", "en fait", "des fois", ", en fait", "Ah oui", "au final", ", au final", "Et oui, toute la contradiction", "<PERSON> fois", ", au final", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 201, "end": 204}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_203", "text": "<PERSON><PERSON>, ou alors, tu dis « bah je mange moins, je mange mieux, mais je mange moins », comme la viande, quoi, au final. Heu ... oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", je", "au final", ", au final", "la viande, quoi", ", au final"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 202, "end": 206}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_204", "text": "<PERSON><PERSON> cette croissance de revenus, ça nous pose aussi une colle, je crois.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": [", je", "cette croissance", "une colle", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 203, "end": 205}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_205", "text": "Ah bah oui, bah ça, on pourrait en parler, justement. Est -ce que il faut pas que ce soit du open access, quoi ? Oui. Alors oui, hein, on... on va pas dans le sens de la concurrence. Du coup, moi, j'ai plus de métier, hein. Ah bah oui, ah bah oui, c'est vrai.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le sens", "du coup", "que ce", ", hein", "le sens", "du coup", "du coup", "du coup", ", hein", "du coup", "le sens", ", c'", "du coup", "Ah oui", "le sens", "le sens", "le sens", ", hein", "le sens", "la concurrence", ", moi", "que ce", "dans le sens", "le sens"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 204, "end": 213}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_206", "text": "C'est vrai. O<PERSON>, mais il faut... il faut se sac... on a dit qu'il fallait qu'y'ait des... bon é<PERSON>ute, ça tombe sur toi, ça tombe sur toi, c'est comme ça.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Comme ça", "Comme ça", "comme ça", ", c'", "comme ça", "comme ça", "se sac", "y'ait des... bon écoute", ", c'est comme ça", ", ça"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 205, "end": 207}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_207", "text": "-ce que... est-ce que... après, il faut voir la nature humaine, aussi.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la nature", "la nature", "... Est", "la nature humaine"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 206, "end": 208}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_208", "text": "<PERSON><PERSON>, c'est ça, aujourd'hui, est-ce qu'on est pas trop individualistes de base pour faire ça ? Et puis pareil, pourquoi ça existe aujourd'hui, c'est que... ces matières-là, heu... pourquoi on récompense l'innovation, c'est parce qu'on... on sent aussi que ça... ça permet de... de générer un certain nombre d'idées, heu... est-ce que ça disparaîtrait pas, en fait, le... comment on dit ? Est", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un certain nombre", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "un certain nombre", "... ce", "un certain nombre", "un certain nombre", "C'est ça", ", en fait", "<PERSON><PERSON>, c'est ça", "C'est ça", "que ça", "<PERSON><PERSON>, c'est ça", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "un certain nombre", "C'est ça", "un certain nombre", "en fait", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", ", en fait", "<PERSON><PERSON>, c'est ça", ", c'est ça", "un certain nombre", "... Est", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'est ça", ", c'", "un certain nombre", "<PERSON><PERSON>, c'est ça", "... ce", "<PERSON><PERSON>, c'est ça", "C'est ça", ", c'est ça", "un certain nombre", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 207, "end": 210}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_209", "text": "-ce que ça ne ferait pas disparaître en fait cette envie d'innover, si jamais on se rend compte que finalement, y'a pas de récompense derrière ? Ou alors, est-ce qu'il faut laisser le choix ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["que ça", "en fait", "le choix", "le choix", "cette envie", "de récompense der<PERSON>", "le choix", "le choix", "le choix", "en fait"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 208, "end": 210}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_210", "text": "La récompense, c'est la... être érigé en héros, la reconnaissance heu... de la société. Mais aujourd'hui, t'as le choix de dire heu... donc là aussi, je vois qu'ils... qu'ils bossent sur des projets open source, machin, qu'ils font ça uniquement pour la satisfaction.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["la société", "la société", ", je", "la société", "le choix", "le choix", ", c'", "la société", "le choix", "La récompense", ", la reconnaissance", "la société", "le choix", "des projets open", "la satisfaction", "le choix", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 209, "end": 211}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_211", "text": "Donc comme tout à l'heure, vous laissez les cartes que vous avez utilisées de côté, sur votre support. T'as le choix...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", vous", "le choix", "le choix", "les cartes", "Les cartes", "les cartes", "le choix", "le choix", "tout à l'heure", "les cartes", "votre support", "le choix", "les cartes", "Les cartes"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 210, "end": 212}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_212", "text": "Et puis à côté, bah t'as des gens qui découvrent le vaccin pour x heu... la première chose à laquelle ils pensent, c'est déposer le brevet pour heu... pour empocher des milliards. Ou alors, raccourci... raccourcir les périodes de protection. Aujourd'hui, par exemple, qu'y'ait que... quelque chose, mais quand même que ce soit limité dans le temps pour que à un moment donné, que tout le monde puisse l'utiliser.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 69, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le temps", "le temps", "par exemple", "un moment", "un moment", "tout le monde", "quel<PERSON> chose", "tout le monde", "des milliards", "des milliards", "le monde", "que ce", "tout le monde", "quel<PERSON> chose", "des milliards", "tout le monde", "tout le monde", "quel<PERSON> chose", "le monde", "quel<PERSON> chose", "le temps", "tout le monde", "quel<PERSON> chose", "le monde", "le monde", "le temps", "quel<PERSON> chose", ", c'", "tout le monde", "des gens", "quel<PERSON> chose", "quel<PERSON> chose", "des gens", "tout le monde", "tout le monde", "que ce", "tout le monde", "tout le monde", "puis à côté", "des gens", "le vaccin", "... la première chose", "le brevet", "des milliards", "les périodes", "le temps", "un moment", "tout le monde", "des milliards", "des milliards"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 211, "end": 214}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_213", "text": "Un brevet, aujourd'hui, heu... c'est protégé 20 ans, donc pourquoi pas se dire : maintenant, c'est 5 ans. Vous avez des bénéfices, mais ça ne durera que 5 ans. <PERSON><PERSON>, oui. Mais du coup, en fait, je suis complètement d'accord avec ça, tout ce qu'on vient d'évoquer, mais pour moi, c'est pas un critère de performance , parce que tu peux...", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["tout ce", "pour moi", "du coup", ", je", "mais du coup", ", en fait", "mais ça", "du coup", "du coup", "mais ça", "20 ans", "du coup", "20 ans", "tout ce", "en fait", "du coup", "mais du coup", ", en fait", "<PERSON><PERSON>a", "<PERSON><PERSON> du <PERSON>", "un critère", ", c'", "du coup", "en fait", "Un brevet", "20 ans", "des bénéfices", "que 5 ans", "<PERSON><PERSON> du <PERSON>", "tout ce", "un critère", "un critère", "<PERSON><PERSON> du <PERSON>", "un critère", "<PERSON><PERSON>a", "un brevet", "un brevet", "mais du coup"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 212, "end": 217}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_214", "text": "parce qu'en fait, tu peux pas dire : cette bo<PERSON><PERSON>, elle est performante que parce qu'elle a fait de l'open access ou parce que heu... elle a pas privatisé son... enfin... Bah t'as des boîtes qui... bah c'est un critère de... aujourd'hui, c'est un critère de performance, quand même, le nombre de brevets déposés, t'as plein de boîtes qui disent « on est... ».", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 64, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des bo<PERSON>", "en fait", "parce que heu", "des bo<PERSON>", "le nombre", "un critère", ", c'", "Le nombre", "de brevets", "en fait", "cette bo<PERSON>te", "l'open access", "des bo<PERSON>", "le nombre", "un critère", "un critère", "un critère", "le nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 213, "end": 215}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_215", "text": "Heu... bah surtout heu... bah « innovation ». Est -ce qu'on les a... est-ce qu'on les a vraiment utilisées ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["... Est", "Heu... bah surtout heu... bah « innovation"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 214, "end": 218}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_216", "text": "On l'a lue, mais on n'a rien écrit sur la feuille. Bah finalement, ça nous permet pas de... C'est vrai qu'on enlève plus des critères de performance que on arrive à en écrire. Sur les heu... critères de performance ? Par exemple, là, sur l'innovation, on se disait : admettons heu... bah on serait plus sur de l'open access. Mais du coup, ça... ça ne devient plus un critère de performance, mais du coup, on sait pas comment le matérialiser.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 80, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la feuille", "par exemple", "du coup", "mais du coup", "du coup", "du coup", "des critères", "la feuille", "du coup", "du coup", "mais du coup", "<PERSON><PERSON> du <PERSON>", "des critères", "... crit<PERSON>", "un critère", "les heu", "du coup", "... crit<PERSON>", "<PERSON><PERSON> du <PERSON>", "l'open access", "un critère", "un critère", "la feuille", "des critères", "les heu", "<PERSON><PERSON> du <PERSON>", ", ça", "un critère", "mais du coup"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 215, "end": 220}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_217", "text": "Donc heu... on est embêtés. C'est pour ça que ça vous fait réfléchir sur la légitimité. Oui. Mais ça, c'est intéressant, ce que tu viens de dire sur le nombre d'années de... comment tu as appelé ça, excuse-moi, le... ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la légitimité", "La légi", "la légitimité", "la légitimité", "que ça", "la légitimité", "mais ça", "mais ça", "la légitimité", "la légitimité", "la légitimité", "<PERSON><PERSON>a", ", ce", "le nombre", ", c'", "Le nombre", "le nombre", "la légitimité", "<PERSON><PERSON>a", "le nombre"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 216, "end": 220}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_218", "text": "Enfin re... réduire la durée de protection. Ou même en montant.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la durée", "la durée", "la durée", "Ou même en montant"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 217, "end": 219}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_219", "text": "Tu te dis : OK, sur un brevet, tu peux te faire un million, 10 millions, 100 millions, un milliard, mais est-ce que c'est humain de se faire des milliards et des milliards sur un brevet, quoi ? Est-ce qu'une partie ne peut pas être reversée à... pour des causes ou...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 51, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des milliards", "des milliards", "des milliards", "des milliards", "Un brevet", "un brevet", "un million", "10 millions", ", 100 millions", "un milliard", "des milliards", "des milliards", "un brevet", "une partie", "... pour des causes", "une partie"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 218, "end": 220}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_220", "text": "Comment on pourrait dire ? Mais heu... mais du coup, c'est toujours pas... Bah la participation heu... à la société. Ah oui, oui, tu as raison, oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["la société", "du coup", "la société", "mais du coup", "du coup", "du coup", "la société", "du coup", "du coup", "mais du coup", "<PERSON><PERSON> du <PERSON>", ", c'", "du coup", "Ah oui", "la société", "la société", "<PERSON><PERSON> du <PERSON>", "<PERSON><PERSON> du <PERSON>", "mais du coup", "<PERSON>h la <PERSON> heu", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 219, "end": 223}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_221", "text": "Le... le taux ou la... le niveau de participation à la... à la société. Dans le sens où... oui, que tu peux gagner autant d'argent que tu veux, dans ce que tu vas gagner, y'aura toujours une partie qui sera pour la collectivité. Oui.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["le sens", "la société", "la société", "le sens", "la société", "le sens", "le sens", "le sens", "la société", "le sens", "le sens", "la société", "dans le sens", "une partie", "la collec", "la collectivité", "la société", "Le... le taux", "... le niveau", "la société", "le sens", "une partie", "la collectivité"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 220, "end": 223}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_B_seg_222", "text": "Heu... donc exemple... heu... là, c'était qu... ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 8, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", c'", "c'était qu"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 221, "end": 223}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_223", "text": "Date : 29/12/2023 Nom du fichier : « B1 »", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["« B1"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 222, "end": 224}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_B_seg_224", "text": "Commanditaire : <PERSON> : 90 minutes Remarques particulières : en italique les modératrices du groupe global. Quelques time codes.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["<PERSON>", "90 minutes", "les modératrices", "groupe global", "Quelques time codes"]}, "metadata": {"source": "data_renamed\\Table_B.docx", "segment_lines": 1, "position": {"start": 223, "end": 225}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}]}