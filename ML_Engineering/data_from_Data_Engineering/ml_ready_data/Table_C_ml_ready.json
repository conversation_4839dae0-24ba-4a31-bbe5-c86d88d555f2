{"source_file": "data_renamed\\Table_C.docx", "processed_timestamp": "2025-06-12T14:05:27.260194", "ml_target_format": "data_json_compatible", "segments": [{"id": "Table_C_seg_001", "text": "Début de la retranscription : … 2050 et donc notamment du scénario que vous avez... Travail collectif 1 matinée.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la retranscription", "donc notamment du scénario", "Travail collectif", "1 matinée", "travail collectif", "travail collectif"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 0, "end": 3}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_002", "text": "… vous allez remplir dans un premier temps, mais je vous dirai, je vous donnerai le top départ, la partie légitimité en 2050, et puis ensuite... donc là, vous aurez à peu près 20 minutes, une demi-heure, et de la même façon sur la partie critères de performance. OK ? Et ça par groupe. Et vous avez compris, je remets des petits codes couleurs là -dessus pour voir comment évoluent au fur et à mesure vos avis .", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 78, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 1.0, "performance_indicators": 2, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un premier temps", "le top départ", "la partie", "peu près 20 minutes", "une demi-heure", "la partie", "Et ça", "des petits codes couleurs", "la partie", "20 minutes", "une demi-heure", "la partie légitimité", "la partie", "la même façon", "une demi-heure", "la même façon", "de performance"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 1, "end": 9}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_003", "text": "<PERSON><PERSON> <PERSON>, par contre, ce que je vous demande vraiment pour ce premier temps, pendant que je prends en photo, c'est vraiment de parler de 2023, donc essayez de pas tout de suite projeter sur 2050, mais dé<PERSON><PERSON> d'apprendre à vous connaître, de discuter de ça et je vous dis quand on passe à ce... à ce moment-là. <PERSON><PERSON> voil<PERSON>, je vous laisse papoter, du coup.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["contre, ce", "ce premier temps", "ce moment", "du coup", ", du coup", ", du coup", "du coup", ", du coup", "tout de suite", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", c'", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 2, "end": 4}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_004", "text": "<PERSON><PERSON>, je suis jeune diplômée depuis quelques mois, donc c'est tout récent pour moi, j'ai eu mon Master en intervention et développement social. Et du coup, voilà, là actuellement je suis à ESS'quiSS, une association qui est au sein de l'Université, mais heu... voilà, première petite expérience, c'est encore tout nouveau, et encore plus ce sujet.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["quelques mois", "mon <PERSON>", "première petite expérience", "encore plus ce sujet", "pour moi", "Et du coup", "du coup", "Et du coup", "Et du coup", "du coup", "Et du coup", "Et du coup", "Et du coup", "une association", "une association", "Et du coup", "Et du coup", "du coup", "du coup", "du coup", "du coup", ", c'", "une association"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 3, "end": 5}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_005", "text": "En fait, moi je venais surtout pour écouter, mais apparemment, faut que je parle aussi, donc v<PERSON>. <PERSON><PERSON>, je travaille dans un cabinet d'expertise comptable, et donc le cabinet s'est engagé dans une démarche RSE et je suis le référent RSE de mon cabinet, donc je suis en phase d'étude pour devenir expert -comptable.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un cabinet", "expertise comptable", "le cabinet", "une démarche", "mon cabinet", "un cabinet", "expertise comptable", "Un cabinet", "un cab", "mon cabinet", "en fait", "une démarche", "une démarche", "mon cabinet", "le cabinet"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 4, "end": 7}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_006", "text": "Les études, ça doit faire les... les trois années de stage dans un cabinet d'expertise comptable et mon sujet donc de thèse à la fin sera très probablement sur la RSE. OK, oui, donc là, c'est... C'est en cours, enfin voilà, moi, c'est dans le sujet.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un cabinet", "expertise comptable", "Les études", "les... les trois années", "un cabinet", "expertise comptable", "mon sujet", "la fin", "sera très probablement sur la RSE", "Un cabinet", "un cab", "le sujet", "le sujet", "là, moi", ", ça", "la fin", ", ça", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 5, "end": 7}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_007", "text": "<PERSON><PERSON>, enfin c'est des cabinets de taille intermédiaire, y'a les gro... les gros sont de plusieurs centaines, milliers de personnes. Nous, on est... enfin dans la région, enfin sur Saint-Étienne, on est le deuxième plus gros si on enlève les... ceux qui sont internationaux.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les gro", "enfin dans la région", "enfin sur Saint-Étienne", "plusieurs centaines"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 6, "end": 9}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_008", "text": "En effectifs, sur un cab... un site, on est le deuxième plus gros, mais voilà, on n'est pas non plus... enfin y'a encore une taille encore au-dessus, largement. Super. Alors... <PERSON><PERSON>, il faut... je pense qu'il faut écrire de suite ou on parle ? Non, ça, c'est pour 2050.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un cab", "un site", "encore une taille", "Non, ça, c'est pour 2050.", ", ça", ", ça", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 7, "end": 11}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_009", "text": "Il faut d'abord qu'on se dise... <PERSON><PERSON>, d'abord, qu'est-ce que vous avez noté.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 8, "end": 10}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_010", "text": "Je vais commencer, donc les cinq qualités, donc une entreprise qui prenne en compte le bien-être de ses collab... enfin de ses salariés. Un juste partage de la valeur, donc de la richesse produite entre les salariés, les actionnaires et le réinvestissement dans la société, pour son développement, sa croissance, enfin sa croissance ou non.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 2, "tension_strength": 1, "total_indicators": 3}, "croissance_decroissance": {"side_a": 2, "side_b": 0, "tension_strength": 2, "total_indicators": 2}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "croissance_decroissance", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["les cinq qualités", "une entreprise", "le bien-être", "ses collab", "enfin de ses salariés", "Un juste partage", "la valeur", "donc de la richesse", "les salariés", ", les actionnaires", "le réinvestissement", "la société", "son dével<PERSON><PERSON><PERSON>", ", sa croissance", "enfin sa croissance", "les salariés", "ses salariés", "le bien-être", "ses salariés", "le bien-être", "son dével<PERSON><PERSON><PERSON>", "les salariés", "ses salariés", "les salariés", "les salariés", "les salariés", "la valeur", "ses salariés", "ses salariés", "enfin de ses salariés", "les salariés", "ses salariés", "la société", "une entreprise", "la société", "une entreprise", "ses salariés", "les actionnaires", "les actionnaires", "les actionnaires", "les actionnaires", "les actionnaires", "la valeur", "la valeur", "la société", "la société", "les actio", "les salariés", "les salariés", "ses salariés", "une entreprise", "le bien-être", "la valeur", "le bien-être", "ses salariés", "la valeur", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 9, "end": 11}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_011", "text": "Heu... une implication positive sur le territoire, c'est-à-dire qui cherche à travailler d'abord avec des acteurs locaux avant de chercher toujours des profits heu... à l'étranger, plus loin, qui cherche à s'impliquer sur heu... au niveau local. Heu... la mini... minimisation de son impact sur l'environnement, au sens heu... large, aussi bien par le rejet qu'elle-même,", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 2, "side_b": 0, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["Heu... une implication positive", "le territoire", "des acteurs locaux", "profits heu", "niveau local", "la mini", "son impact", ", au sens", "aussi bien par le rejet", "son impact", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 10, "end": 12}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_012", "text": "<PERSON><PERSON>, je suis dans le milieu de l'entreprise, donc forcément, heu... <PERSON><PERSON>, non, c'est normal. Trop bien. Alors moi, j'ai mis que des mots.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["<PERSON><PERSON> moi", "que des mots", "<PERSON><PERSON> moi", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 11, "end": 15}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_013", "text": "<PERSON><PERSON> moi, j'ai mis : la flexibilité, donc heu... en général, que ce soit pour son évolution, sa croissance, et puis aussi son environnement. Et puis aussi envers ses salariés, qu'ils soient flexibles, parce qu'on est tous unique, donc s'adapter aussi, pas forcément individuellement, mais voilà, de manière collective.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": [", sa croissance", "<PERSON><PERSON> moi", "<PERSON><PERSON> moi", "la flexibilité", "son évolution", "ses salariés", "manière collective", "ses salariés", "la flexibilité", "son environnement", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "la flexibilité", "ses salariés"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 12, "end": 14}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_014", "text": "La bienveillance, donc ça va avec le bien-être de l'entreprise, forcément. Donc l'écoute de ses salariés, que ce soit en interne ou en externe aussi, donc de ses clients, du marché.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["le bien-être", "ses salariés", "La bienveillance", "le bien-être", "Donc l'écoute", "ses salariés", "ses clients", "le bien-être", "ses clients", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "la bienveillance", "ses salariés", "le bien-être", "le bien-être", "ses salariés"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 13, "end": 15}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_015", "text": "L'adaptabilité, donc pareil, hein, ça va avec la flexibilité, s'adapter à l'envir... à son environnement, à... à son évolution aussi, donc à ses salariés, à ses besoins. La responsabilité, donc pareil, là c'est plus envers peut-être l'écologie, aussi responsable auprès de ses clients, et puis aussi de ses salariés, bien évidemment, qu'ils soient d'accord avec ses valeurs et qu'ils soient responsables aussi de ses erreurs, hein. <PERSON><PERSON>, c'est aussi important.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la flexibilité", "son évolution", "ses salariés", "ses salariés", "la flexibilité", "son environnement", "... à son évolution", "donc à ses salariés", "ses besoins", "La responsabilité", "aussi responsable", "ses clients", "ses valeurs", "ses clients", "ses erreurs", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "la flexibilité", ", ça", "ses salariés", ", ça", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 14, "end": 17}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_016", "text": "Et ensuite, c'est l'éthique et le respect, donc heu... pareil, envers l'humain et l'environnement, qu'y'ait une sorte de... d'égalité. Et voilà, je pense que le mot est assez large pour qu'il soit compris.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le respect", ", envers", "une sorte", "... d'égalité", "le mot", "le respect", "le respect", "le respect", "le mot", "que le mot", "le respect", "le mot", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 15, "end": 17}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_017", "text": "<PERSON>h moi, j'ai séparé du coup l'éthique et le respect, en fait, je sais pas si... enfin pour moi, l'éthique, je l'entendais plus au niveau... oui, du coup, du bien-être des salariés, plus à ce niveau-là. Et le respect, j'ai plus mis par rapport à l'environnement, le respect de l'environnement.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le respect", "coup l'éthique", "le respect", "enfin pour moi", ", du bien-être", "le respect", ", le respect", "pour moi", "des salariés", "Enfin pour moi", ", le respect", "le respect", "des salariés", "du coup", ", du coup", "en fait", ", du coup", "du coup", "<PERSON><PERSON>, du coup", "ce niveau", ", du coup", "des salariés", "enfin pour moi", ", du coup", ", du coup", ", du coup", "du coup", "du coup", "enfin pour moi", ", du coup", "le respect", "du coup", "du coup", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 16, "end": 19}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_018", "text": "<PERSON><PERSON>, c'est vrai que ça... <PERSON><PERSON><PERSON> l'éthique, c'est plus large, je trouve, donc juste le bien-être des salariés. L'éthique, ça va même au-delà, c'est avoir une co... quelque chose de respectueux, ça prend en compte les Droits de l'Homme, ça prend en compte une... des achats qui sont éthiques, c'est-à-dire pas des achats... ne pas aller se fournir dans... sur des... des fournisseurs qui ne respecteraient pas les Droits de l'Homme, enfin l'éthique est plus large que juste le bien-être des salariés.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 83, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le bien-être", "le bien-être", "Même l'éthique", "donc juste le bien-être", "une co", "quel<PERSON> chose", "les Droits", "des achats", ", c'est-à-dire pas des achats", "des fournisseurs", "les Droits", "le bien-être", "des salariés", "quel<PERSON> chose", "des salariés", "des achats", "quel<PERSON> chose", "des salariés", ", ça", "le bien-être", "le bien-être", "que ça", "enfin l'éthique", ", ça", "quel<PERSON> chose", ", c'", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 17, "end": 19}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_019", "text": "Donc ça va même au-delà. Donc ça... au pire, oui, voilà, ça peut englober. J'avais parlé aussi des capacités à s'adapter aux besoins et à répondre aux besoins quand même du territoire. Ça paraît la base, mais pour moi, voilà.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["des capacités", "quand même du territoire", "la base", "pour moi", ", ça", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 18, "end": 23}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_020", "text": "Et enfin oui, c'est à peu près tout ce que j'ai mis, parce que j'ai pas forcément plus d'idées que ça non plus. C'est vraiment nouveau pour moi, je connais pas trop le... J'avoue que j'ai jamais fait de ce genre d'exercice non plus. Ah oui, première fois que je fais aussi. C'est vrai ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pour moi", "ce genre", "première fois", "que ça", ", c'", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 19, "end": 23}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_021", "text": "<PERSON><PERSON>, par contre, du coup, j'avais... j'ai déjà... <PERSON><PERSON>, parce qu'y'a l'expérience aussi.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", ", du coup", ", du coup", "du coup", ", du coup", "contre, du coup", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 20, "end": 22}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_022", "text": "Mon... mon expérience qui... mon mémoire de Bac+... enfin pour le Master, c'était sur le bien-être du collaborateur comptable, donc là aussi, j'avais déjà... j'ai déjà fait, enfin c'était de la documentation, enfin je... je suis déjà impliqué, enfin voil<PERSON>, sur la démarche RSE du cabinet, comment est-ce qu'on peut la développer, même nous, au sein du réseau, comment est-ce qu'on peut le faire vis-à-vis de nos parties prenantes également, la communi... la communication auprès de... enfin de nos clients. Les fournisseurs, aller prendre des fournisseurs éthiques ou alors qui travaillent... enfin on travaille beaucoup avec le médico-social, donc heu... on essa... on va essayer de se fournir au maximum chez eux, on est impliqués également dans plusieurs associations, enfin on fait des... mais c'est tout plein de cho... enfin voil<PERSON>, mais... C'est quand même du coup très ancré dans le sujet, là, quand même. Oui, voilà, c'est pour ça. C'est... c'est en lien avec le sujet.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 157, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.2738853503184713, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 0.7643312101910827}, "noun_phrases": ["le bien-être", "le bien-être", "des fournisseurs", "Mon... mon expérience", "enfin pour le Master", "c'était sur le bien-être", "collaborateur comptable", "la démarche", "même nous", "nos parties prenantes également", ", la communi", "la communication", "... enfin de nos clients", "Les fournisseurs", "des fournisseurs éthiques", "le médico-social", "plusieurs associations", "le sujet", "<PERSON><PERSON>, voilà, c'est pour ça", "le sujet", "le bien-être", "nos clients", "chez eux", "du coup", "parties prenantes", "du coup", "les fournisseurs", "les fournisseurs", "la co", "la communication", "la communication", "les fournisseurs", "parties prenantes", "le bien-être", "le bien-être", "du coup", "du coup", "du coup", "du coup", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 21, "end": 25}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_023", "text": "Enfin moi personnel<PERSON>, j'essaie de pousser pour que mon cabinet aille le plus possible dans... dans la bonne direc... enfin la direction qui me semble être la bonne direction, non pas qu'on sache forcément la bonne direction, mais... et puis même dans la manière d'interagir avec les parties prenantes ou de communiquer, d'essayer de faire... enfin la bienv... comme vous avez dit, la bienveillance, je l'ai pas notée", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 68, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 1, "side_b": 1, "tension_strength": 0, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["mon cabinet", "La bienveillance", "<PERSON><PERSON> moi", "mon cabinet", "la bonne direc", "la direction", "la bonne direction", "les parties prenantes ou de communiquer", ", la bienveillance", "la Direction", "parties prenantes", "les parties pre", "la Direction", "la Direction", "la bienveillance", "les parties prenantes", "les parties prenantes", "parties prenantes", "enfin moi", "mon cabinet"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 22, "end": 24}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_024", "text": "<PERSON> apr<PERSON>, ça va dans la continuité, hein, j'imagine. <PERSON><PERSON> apr<PERSON>, oui, moi, y'a des choses qui sont revenues, du coup, je trouve, par rapport à en haut, enfin tout ce qui est l'éthique aussi, le respect de l'environnement, les choses comme ça, c'est...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le respect", "le respect", "le respect", ", le respect", "la continuité", "des choses", ", le respect", "le respect", "des choses", "comme ça", "comme ça", "du coup", "a<PERSON><PERSON>ès, ça", ", du coup", "des choses", "Comme ça", "comme ça", ", du coup", "comme ça", "des choses", "du coup", ", du coup", "Comme ça", "la co", "Comme ça", ", ça", "comme ça", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "le respect", "du coup", "du coup", ", ça", ", c'", "les choses", ", du coup", "comme ça", "comme ça", "des choses", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 23, "end": 25}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_025", "text": "À exercer ou même à remporter les marchés publics, j'en connais certaines, alors que par ailleurs, tout n'est pas forcément respecté, mais ça, c'est... C'est vrai. Les boîtes font... enfin le chef d'entreprise gère lui-même sa boîte, il fait... enfin si il ne veut pas respecter, il ne respectera pas.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["les marchés publics", "<PERSON> b<PERSON>", "le chef", "sa boîte", "sa boîte", "les boîtes", "<PERSON><PERSON>a", ", c'", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 24, "end": 26}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_026", "text": "Enfin nous, nous avons quand même une limite, donc... c'est pas nous, les gérants, nous sommes que le conseiller, donc le gérant qui veut... enfin... donc voilà. Et les critères de performance, là, je pense, y'a plus de choses à dire, enfin je sais pas si vous avez noté plus de choses. <PERSON><PERSON>, pas plus que ça. <PERSON><PERSON>, moi, sur la légitimité, j'avais marqué heu... l'écoute, à la fois l'écoute de son marché, de ses clients et des salariés collaborateurs et partenaires.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 83, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["ses clients", "quand même une limite", "les gérants", "le géra<PERSON>", "les critères", "<PERSON><PERSON>, moi", "la légitimité", "... l'écoute", "la fois", "son march<PERSON>", "ses clients", "des salariés", "la légitimité", "La légi", "la légitimité", "des salariés", "la légitimité", "la fois", "la fois", "la légitimité", "les gérants", "le conseil", "le conseil", "le conseil", "le conseil", "des salariés", "le conseil", "le conseil", "la fois", "la légitimité", "la légitimité", "les critères", "les critères", "son march<PERSON>", "les critères", "que ça", "de performance", "c'est pas", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 25, "end": 29}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_027", "text": "Donc au final, r<PERSON><PERSON><PERSON><PERSON> à des besoins. Je pense que c'est assez important.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des besoins", "des besoins"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 26, "end": 28}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_028", "text": "Heu... une boîte aussi qui sait se remettre en question et donc changer et évoluer. Et ça va de... de pair avec le fait de reconnaître ses erreurs et faire en sorte de s'améliorer.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Et ça", "Heu... une boîte", "le fait", "ses erreurs", "le fait", "une boîte", "le fait", "une boîte", "le fait", "une boîte", "Le fait", "une boîte", "le fait"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 27, "end": 29}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_029", "text": "Et ensuite, je parlais d'engagement et d'investissement. Et quand je parle d'engagement, c'est des engagements concrets, qui peuvent être visibles, que ce soit par son développement, donc interne/externe, et l'environnement en général.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["son dével<PERSON><PERSON><PERSON>", "son dével<PERSON><PERSON><PERSON>", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 28, "end": 30}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_030", "text": "<PERSON><PERSON>, j'ai... enfin du coup, en lien, c'est... moi j'ai parlé du turn... enfin du turn-over, un faible taux de turn-over, g<PERSON><PERSON><PERSON>, sous-entend des bonnes conditions de travail, parce qu'une boîte... enfin nous, vu que nous, on fait aussi la... les paies de salariés, on voit également les taux de turn-over de nos clients, on a les... en interne, on a les bases de données de l'Ordre qui nous donne le taux de turn-over moyen des... par secteur, par type de clients, du coup nous on peut comparer et voir ceux qui ont des mauvais taux de turn-over. Souvent, c'est que derrière, y'a des mauvaises conditions de travail, soit pas des conditions de travail, enfin pas rémunérés à leur juste valeur, les condi... les horaires de travail heu... non respectueuses vis-à-vis des salariés, trop de demande d'implication des salariés, un management brutal, enfin... y'a tout plein de choses derrière quand on voit un taux de turn-over.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 156, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.25641025641025644}, "noun_phrases": ["des salariés", "bonnes conditions", "parce qu'une boîte", "les paies", "les taux", "nos clients", "les bases", "le taux", "des mauvais taux", "des mauvaises conditions", ", soit pas des conditions", "leur juste valeur", "les condi", "les horaires", "travail heu", "trop de demande", "un management brutal", "un taux", "des conditions", "une boîte", "des salariés", "des conditions", "des conditions", "une boîte", "du coup", ", du coup", ", du coup", "du coup", ", du coup", "des salariés", "une boîte", ", du coup", ", du coup", ", du coup", "un taux", "un taux", "une boîte", "qu'une boîte", "du coup", "du coup", "enfin du coup", ", du coup", "du coup", "du coup", ", c'", ", du coup", "de client", "un taux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 29, "end": 31}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_031", "text": "Et donc recrutement derrière. Non-renouvellement de CDD, tout ça, c'est tout... en gros, c'est toutes les sorties... les entrées et toutes les salariés de l'entreprise.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les salariés", "les salariés", "tout ça", "gros, c'est toutes les sorties", "toutes les salariés", "les salariés", "les salariés", "les salariés", "les salariés", "tout ça", "tout ça", "les salariés", "tout ça", ", tout ça", "tout ça", "tout ça", "les salariés", ", tout ça", "les salariés", "tout ça", "tout ça", "tout ça", "tout ça", "... Les ent", ", c'", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 30, "end": 32}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_032", "text": "Et donc généralement, plus on a un turn-over... Est-ce que vous avez fait à peu près le tour ou est-ce qu'il vous faut encore un peu de temps ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un turn", "un turn", "un turn", "peu près le tour", "un peu de temps", "un turn"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 31, "end": 33}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_033", "text": "Et donc plus on a un turn-over faible, plus ça veut dire que l'effectif est stable, donc ça veut normalement dire que les personnes, si elles restent dans l'entreprise, c'est qu'elles s'y sentent bien. C'est que du coup, elles sont bien et oui, que ça fonctionne plutôt bien, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un turn", "un turn", "un turn", "un turn", "du coup", "dans l'entreprise", "du coup", "que ça", "du coup", "du coup", "c'est qu'", "du coup", "du coup", ", c'", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 32, "end": 34}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_034", "text": "Voilà, soit c'est qu'ils ont des avantages qui font que même si ils ont... enfin au global, ils trouvent un équilibre. C'est-à-dire soit ils ont une rémunération très avantageuse, qui compense des conditions de travail qui peuvent être moins intéressantes qu'ailleurs, ou c'est que leurs conditions de travail sont très avantageuses et du coup, ils vont pas aller chercher une meilleure rémunération ailleurs.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["des avantages", "un équilibre", "une rémunération très avantageuse", "des conditions", "leurs conditions", "une meilleure rémunération", "des conditions", "leurs conditions", "Et du coup", "des conditions", "du coup", "Et du coup", "Et du coup", "du coup", "Et du coup", "Et du coup", "Et du coup", "Et du coup", "Et du coup", "du coup", "du coup", "c'est qu'", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 33, "end": 35}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_035", "text": "Ou souvent, c'est un mix de l'ensemble et qui fait que au final, les salariés, s'ils ne partent pas de l'entreprise, c'est qu'ils estiment qu'ils ne trouveront pas mieux ailleurs. Oui, donc en fait, si les équipes changent régulièrement, c'est qu'y'a quelque chose qui va pas dans le fonctionnement ou dans le... Quelque part dans la boîte.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["les salariés", "les salariés", "quel<PERSON> chose", "les salariés", "donc en fait", "les équipes", "quel<PERSON> chose", "le fonctionnement", "Quelque part", "la boîte", "la boîte", "les salariés", "les salariés", "les salariés", "la boîte", "la boîte", "les salariés", "quel<PERSON> chose", "en fait", "la boîte", "la boîte", "la boîte", "la boîte", "la boîte", "la boîte", "les salariés", "les salariés", "la boîte", "c'est qu'", "quel<PERSON> chose", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 34, "end": 36}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_036", "text": "Ça peut être heu... enfin ça peut être multi-factoriel, enfin souvent c'est multi-factoriel, ça peut être une mésentente avec la... les choix de la Direction, des problèmes de rémunération, des problèmes de... enfin... Ça peut être plein de choses, en fait. Voilà, tout peut... C'est ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la direction", "les choix", "la Direction", "des problèmes", "des problèmes", "C'est ça", "C'est ça", "des problèmes", "des problèmes", "des problèmes", "des problèmes", "en fait", "des problèmes", "des problèmes", "la Direction", "la Direction", ", ça", "C'est ça", ", ça", "des problèmes", "des problèmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 35, "end": 38}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_037", "text": "J'ai mis : de la transparence dans la prise de décisions et dans le partage de valeurs, enfin dans les... que la boîte communique à ses salariés ce... comment se porte la boîte, que... quelles sont les rémunérations de... enfin quelle est la part heu... voilà, « notre boîte, on en est là, on voudrait faire ça, qu'est-ce que vous en pen... ? », enfin communiquer avec les salariés pour heu... pas forcément que ce soit les salariés qui décident de tout, mais a minima communiquer et changer avec eux, et non pas que ce soit le chef d'entreprise qui décide. Qui prend toutes les décisions sans consulter.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 108, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.9259259259259258, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 3, "tension_strength": 3, "total_indicators": 3}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["les salariés", "la prise", "les salariés", "la prise", "la prise", "ses salariés", "ses salariés", "le chef", "les salariés", "la transparence", "la prise", "dans le partage", "la boîte", "ses salariés", "la boîte", "les rémunérations de", "la part heu", "notre bo<PERSON>te", "les salariés", "les salariés", "non pas que ce soit le chef", "toutes les décisions", "les salariés", "la transparence", "la boîte", "ses salariés", "ses salariés", "la boîte", "les salariés", "la prise", "la boîte", "ses salariés", "la boîte", "les décisions", "la boîte", "ses salariés", "les décisions", "la boîte", "la boîte", "la boîte", "Le partage", "les décisions", "les salariés", "les décisions", "les salariés", "la transparence", "ses salariés", "la transparence", "Le partage", "ses salariés", "la prise", "la boîte", "le partage"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 36, "end": 39}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_038", "text": "Voilà, « on a versé tant de dividendes, la rémunération, bon bah je vais verser une prime de 200 euros pour les salariés », enfin.... <PERSON><PERSON>, de la transparence, quoi. <PERSON><PERSON>, c'est d'impliquer un peu plus les salariés dans le... Voilà. De la transparence et de la communication avec eux, de la prise en compte de leur... du point de vue des salariés.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["les salariés", "la prise", "les salariés", "la prise", "la prise", "la communication", "des salariés", "les salariés", "la transparence", "la prise", "les salariés", "les salariés", "la rémunération", "une prime", "200 euros", "les salariés", "un peu plus les salariés", "la transparence", "de la communication", ", de la prise", "des salariés", "les salariés", "la prise", "un peu plus les salariés", "des salariés", "les salariés", "les salariés", "la co", "la transparence", "la communication", "la transparence", "la communication", "la prise", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 37, "end": 41}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_039", "text": "Heu... une recherche permanente à s'améliorer, enfin tu en as parlé, tu en as parlé, toi, dans la légitimité, moi c'était dans les critères de perfor... Apr<PERSON>, ça se rejoint aussi un peu, je pense. Ça se rejoint, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "la légitimité", "... une recherche permanente", ", toi", "la légitimité", "La légi", "la légitimité", "a<PERSON><PERSON>ès, ça", "la légitimité", "la légitimité", "la légitimité", ", ça", "la légitimité", "les critères", "les critères", "les critères", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 38, "end": 41}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_040", "text": "Et le fait de porter des valeurs qui impliquent heu... et impliquer ses parties prenantes dans une démarche RSE, enfin dans une démarche d'amélio... que eux aussi s'engagent dans la même heu... stra... démarche et pas uniquement que l'entreprise se dise pas « bon bah c'est bon, moi je me comporte bien, heu... je m'en fous, je me préoccupe pas des autres », essayer d'impliquer également ses parties prenantes, les autres entreprises, ses fournisseurs, ses clients, dans... dans une démarche d'amélioration. OK, oui, donc tout son environnement, en fait.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 89, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une démarche", "que eux", "son environnement", "ses clients", "ses clients", "le fait", "le fait", "des valeurs", "ses parties prenantes dans une démarche RSE", "enfin dans une démarche", "la même heu", "ses parties prenantes", ", les autres entreprises", ", ses fournisseurs", ", ses clients", "... dans une démarche", "le fait", "parties prenantes", "en fait", "une démarche", "le fait", "parties prenantes", "Le fait", "une démarche", "des valeurs", "ses parties prenantes", "le fait"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 39, "end": 41}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_041", "text": "Enfin pour moi, ça va en lien, quoi. Heu", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["enfin pour moi", "pour moi", "Enfin pour moi", "enfin pour moi", ", ça", "enfin pour moi", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 40, "end": 42}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_042", "text": "... les actions environnementales, donc ça peut être heu... des trucs très basiques comme le tri, le développement durable, donc les démarches RSE, le respect de la biodiversité, l'utilisation des matériaux éco-responsables aussi. Heu... aussi le fait de placer l'humain en première place, donc ça peut être lié au temps de travail, donc au bien-être du travail, et le respect de la vie pro/perso. Là, y'a pas longtemps, j'avais rencontré le... le dirigeant de LDLC qui est passé à la semaine de quatre jours et il nous avait... dans une conférence, il avait expliqué les bienfaits que ça avait faits dans sa boîte.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 103, "thematic_indicators": {"performance_density": 0.970873786407767, "legitimacy_density": 0.970873786407767, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["le respect", "le respect", "le respect", ", le respect", "une co", "sa boîte", "le fait", "le fait", "les actions environnementales", "le tri", ", le développement", "les démarches", ", le respect", "la biodiversité", "mat<PERSON>riaux éco-responsables", "aussi le fait", "première place", "donc au bien-être", "le respect", "la vie pro/perso", "le dirigeant", "la semaine", "quatre jours", "une conférence", "les bienfaits", "sa boîte", "quatre jours", "La semaine", "quatre jours", "la semaine", "quatre jours", "le fait", "des trucs", "le dirigeant", "les actio", "le fait", "<PERSON> tru<PERSON>", "Le fait", "que ça", "la biodiversité", "des trucs", "la biodiversité", "le respect", "le fait", "des trucs", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 41, "end": 44}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_043", "text": "Donc en imposant à ses salariés de travailler moins, donc que quatre jours et donc 32 heures, il avait eu une augmentation de ses performances... De la productivité. De la productivité.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["ses salariés", "ses salariés", "ses salariés", "quatre jours", "ses salariés", "quatre jours", "donc 32 heures", "une augmentation", "ses performances", "la productivité", "la productivité", "quatre jours", "quatre jours", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "ses salariés"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 42, "end": 45}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_044", "text": "Donc je trouvais que c'était un bon, aussi, heu... vecteur de performance. Et au final, eux, ils se sentaient mieux aussi au niveau pro/perso, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["niveau pro/perso", "de performance", ", eux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 43, "end": 45}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_045", "text": "Enfin tout était lié, au final. Y'a eu une réduction normalement des arrêts, enfin des études montrent des réductions des arrêts de travail, une augmen... une réduction du turn-over, enfin une amélioration des conditions de travail, de la productivité, enfin y'a tout plein de...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["des conditions", "la productivité", "la productivité", "une réduction normalement", "enfin des études", "des réductions", ", une augmen", "une réduction", "enfin une amélioration", ", de la productivité", "des conditions", "des conditions"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 44, "end": 46}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_046", "text": "Une banque heu... enfin une néo-banque qui est... elle aussi s'est lancée dans la semaine de quatre jours, donc ça commence petit à petit. Après, ça avance doucement.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 28, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la semaine", "quatre jours", "quatre jours", "La semaine", "quatre jours", "Une banque heu", "la semaine", "quatre jours", "a<PERSON><PERSON>ès, ça", ", ça", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 45, "end": 47}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_047", "text": "Alors, ce que je vous propose maintenant, c'est heu... est-ce que vous avez... alors c'est peut-être pas évident, mais toujours en tête ce qui s'est dit heu... ce que Alexis nous a raconté sur le scénario 2050, en petit aide ou rappelle-mémoire, on a des effets sur les... vous avez des feuilles comme ça, qui est la slide qu'il vous a présentée, et donc là, ce que je vais vous demander, c'est... enfin ce qu'on vous demande , c'est donc sur cette... sur ce document-là vierge, heu... de répondre à la partie 5, donc la première case à gauche : quelle sera une boîte légitime en 2050, quand on observe les effets que ça produit ? Clap 2, travail collectif.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 120, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["Travail collectif", "la partie", "la partie", "Alors, ce", "toujours en tête", "le scénario 2050", "petit aide", "des effets", "des feuilles", "ce document", "la partie", "donc la première case", "quelle sera une boîte légitime", "les effets", "travail collectif", "Une boîte légitime", "une boîte", "comme ça", "une boîte", "comme ça", "Comme ça", "comme ça", "comme ça", "enfin ce", "Comme ça", "la partie", "Comme ça", "une boîte", "travail collectif", "comme ça", "que ça", "une boîte", ", c'", "comme ça", "comme ça", "que ça", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 46, "end": 49}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_048", "text": "Donc en considérant que y'a notamment ces effets-là, mais aussi tous ceux que vous imaginez, hein, heu... finalement, une boîte légitime en 2050, qu'est-ce qu'elle est ? Peut-être qu'y'a des choses que vous avez déjà écrit en 2023, que vous allez pouvoir reverser à 2050, mais peut-être que il faudra aussi imaginer d'autres choses.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des choses", "y'a notamment", "des choses", "d'autres choses", "Une boîte légitime", "une boîte", "une boîte", "des choses", "des choses", "tous ceux", "tous ceux", "une boîte", "d'autres choses", "une boîte", "des choses"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 47, "end": 49}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_049", "text": "La légi... enfin j'espère que c'est clair sur la notion de légitimité pour tout le monde, sinon je peux aussi heu... donner un peu d'exemple, mais en tout cas, c'est... voil<PERSON>, comment on la considère légitime, donc heu... utile, intéressante, et visible, en fait. Peut-être, on peut parler de visible par l'ensemble des parties prenantes autour de ça. Si déjà on se dit ça, heu... c'est pas la définition très exacte de la légitimité, mais c'est ce qui vous parle.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 80, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["tout le monde", "la légitimité", "la légitimité", "La légi", "la notion", "tout le monde", "parties prenantes autour de ça", "la légitimité", "c'est ce", "parties prenantes", "en fait", "la légitimité", "la légitimité", "la co", "la légitimité", "la légitimité", "parties prenantes", "tout le monde", ", c'", "c'est pas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 48, "end": 51}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_050", "text": "Heu... voilà, donc on a 20 minutes, une... enfin on a une demi-heure pour faire ça. Juste qu'y'en ait un, s'il vous plaît, qui écrive sur cette feuille.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 28, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une demi-heure", "20 minutes", "une demi-heure", "cette feuille", "une demi-heure"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 49, "end": 51}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_051", "text": "Al<PERSON>, légitimité ? Une boîte légitime ? Heu... En 2050... En 2050, ça voudra dire que... Sachant que canicule, heu...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON>ors, légitimité", "Une boîte légitime", "que canicule", "une boîte", "une boîte", "une boîte", ", ça", "une boîte", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 50, "end": 54}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_052", "text": "Bah pour moi, du coup, la... la prise en compte des... enfin des problèmes de santé de ses salariés, une boîte devra beaucoup plus, à l'avenir y prendre en compte, parce que là, actuellement, on n'a pas de... enfin les boîtes sont peu confrontées à des salariés qui auraient des problèmes de... de nutrition, des problèmes ne serait-ce que de... à survivre, à vivre, qui ont des conditions de travail enfin décentes. Actuellement, les boîtes sont que peu, pour le moment, confrontées à cette problématique, mais à l'avenir, ça... les... les intempéries, les risques climatiques, la... enfin la migration de... des nouvelles populations fait qu'on va devoir intégrer tous ces enjeux.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 111, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.36036036036036034}, "noun_phrases": ["la prise", "la prise", "la prise", "ses salariés", "ses salariés", "pour moi", "<PERSON> b<PERSON>", "des salariés", "des conditions", "des problèmes", "des problèmes", "la prise", "ses salariés", "ses salariés", "ses salariés", "une boîte", "de... enfin les bo<PERSON>tes", "des salariés", "des problèmes", "... de nutrition", "des problèmes", "des conditions", "les boîtes", "le moment", "cette problématique", "à l'avenir", "les... les intempéries", ", les risques climatiques, la... enfin la migration", "des nouvelles populations", "tous ces enjeux", "des conditions", "une boîte", "du coup", ", du coup", "des problèmes", "des problèmes", "Les risques", "la prise", ", les risques", ", les risques", "les risques", "les risques", "ses salariés", "des problèmes", ", du coup", "des problèmes", "du coup", "ses salariés", ", du coup", "des salariés", "ses salariés", "une boîte", ", ça", ", du coup", "ses salariés", ", du coup", "la prise", ", du coup", "une boîte", "du coup", "du coup", ", du coup", "du coup", "du coup", ", ça", ", du coup", "des problèmes", "des problèmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 51, "end": 53}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_053", "text": "Du coup, la boîte devra beaucoup plus intégrer ces probléma... enfin à mon sens. Les prendre en compte.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la boîte", "la boîte", "la boîte", "la boîte", "ces probléma", "enfin à mon sens", "du coup", "la boîte", "la boîte", "la boîte", "du coup", "la boîte", "la boîte", "la boîte", "la boîte", "du coup", "du coup", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 52, "end": 54}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_054", "text": "Les prendre en compte dans... enfin pour le bien-être des... enfin de ses salariés. Ça sera beaucoup plus compliqué, l'accueil et l'intégration, la... le partage des connaissances, partage des... la mixité des cultures va encore plus augmen... enfin il va y en avoir de plus en plus, et du coup, il faudra davantage trouver des stratégies pour... enfin pas des stratégies, mais oui, une politique d'intégration de partage de valeur pour les nouvelles personnes qui vont rentrer, pour les intégrer, pour leur... et leurs conditions de travail qui seront forcément dégradées.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 91, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["le bien-être", "enfin de ses salariés", "ses salariés", "le bien-être", "ses salariés", "le bien-être", "leurs conditions", "ses salariés", "ses salariés", "ses salariés", "enfin pour le bien-être", "enfin de ses salariés", ", l'accueil", ", la... le partage", "la mixité", "des stratégies", "... enfin pas des stratégies", "oui, une politique", "les nouvelles personnes", "leurs conditions", "Et du coup", "Une politique", "du coup", "les nouvelles personnes", "une politique", "Et du coup", "Et du coup", "ses salariés", "du coup", "Et du coup", "ses salariés", "des connaissances", "des connaissances", "Et du coup", "Le partage", "Et du coup", "ses salariés", "Et du coup", "le bien-être", "Le partage", "le bien-être", "ses salariés", "Et du coup", "du coup", "du coup", "le partage", "une politique", "une po", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 53, "end": 55}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_055", "text": "Je sais pas comment le formuler, enfin y écrire. <PERSON><PERSON>, bah déj<PERSON>, on peut prendre les problèmes individuellement et c'est réfléchir à une légitimité en fonction de chaque problème, non ? Ce serait peut-être un peu plus clair pour développer tout ça. <PERSON><PERSON>, je pense, parce que oui, là, moi je suis passé droite-gauche.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["tout ça", "les problèmes individuellement", "une légitimité", "chaque problème", "tout ça", "tout ça", "tout ça", "Les problèmes", "ce serait", "tout ça", "tout ça", "là, moi", "tout ça", "tout ça", "tout ça", "tout ça", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 54, "end": 58}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_056", "text": "<PERSON><PERSON>, ça peut rentrer dans pas mal de trucs, hein, mais... <PERSON><PERSON>, on a... voilà, y'a plein de... <PERSON><PERSON>, le fait que heu... on y va de... du climat, il risque d'y avoir des ralentissements de l'activité économique, donc soit par des grosses chaleurs, ou à l'inverse des... des moments très froids aussi, hein. Du coup, ne pas viser systématiquement une heu... croissance, parce qu'on aura naturellement un ralentissement de l'activité économique. Oui.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 74, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["le fait", "le fait", "pas mal", "le fait", "... du climat", "des ralentissements", "l'activité économique", "donc soit par des grosses chaleurs", "à l'inverse", "des moments très froids aussi", "une heu", "... croissance", "un ralentissement", "l'activité économique", "du coup", "pas mal", "du coup", "une heu", "une heu", "le fait", ", ça", "Le fait", "du coup", "du coup", "une heu", "une heu", "du coup", "du coup", ", ça", "le fait"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 55, "end": 58}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_057", "text": "Après, heu... peut-être en termes de durabilité, au final ? Non, bah de... enfin la croissance de l'activité, ne pas chercher... enfin heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["enfin heu", "la croissance", "la croissance", "la croissance", "la croissance"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 56, "end": 58}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_058", "text": "<PERSON><PERSON>, mais avoir plus une activité durable, donc heu... fluctuante, mais heu... pas forcément pointer l'excellence à chaque fois, quoi. Enfin ne pas chercher toujours à croître son activité, son chiffre d'affaires, ses... parce qu'on sait qu'il y aura forcément des périodes basses.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["une activité durable", "chaque fois", "son activité", "son chiffre", "p<PERSON><PERSON><PERSON> basses", "p<PERSON><PERSON><PERSON> basses", "des périodes", "chaque fois", "chaque fois", "chaque fois"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 57, "end": 59}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_059", "text": "Donc on dit : ne pas toujours accroître, tu disais ? Heu... ne pas toujours viser la croissance et avoir une politique plus durable, une stratégie plus durable. Et du coup, heu... avoir aussi des... Une politique de pro... du coup, oui, j'allais dire protection des salariés vis-à-vis de la... des risques... <PERSON><PERSON>, ou peut-être une flexibilité par rapport au temps de travail, à la présence, tout ça, pas forcément les obliger de venir sur place, si c'est possible. Oui, voilà.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 81, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["croissance_decroissance", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["des salariés", "tout ça", "des salariés", "tout ça", "la croissance", "une politique plus durable", "Et du coup", "Une politique", "la... des risques", "la présence", "tout ça", "du coup", "une politique", "tout ça", "Et du coup", ", tout ça", "Et du coup", "tout ça", "tout ça", "du coup", "Et du coup", "Et du coup", "Et du coup", "des salariés", ", tout ça", "Et du coup", "tout ça", "tout ça", "tout ça", "... du coup", "Et du coup", "du coup", "du coup", "une politique", "la croissance", "la croissance", "la croissance", "tout ça", "une po", "du coup", "du coup", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 58, "end": 64}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_060", "text": "Bah des périodes basses et des périodes hautes, les autoriser à... pendant la période basse, y'a pas besoin, enfin parce qu'il fait trop chaud, « bah restez chez vous », sauf si... « est-ce que votre logement est adapté ? Enfin est-ce que vous pouvez rester chez vous ? Est-ce que... », parce que typiquement, s'il fait 46 degrés et que c'est une passoire thermique, bah...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["p<PERSON><PERSON><PERSON> basses", "p<PERSON><PERSON><PERSON> basses", "des périodes", "la période basse", "votre logement", "46 degrés"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 59, "end": 62}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_061", "text": "Bah dans ce cas-là, on peut imaginer en 2050 que les entreprises, là, ils peuvent investir dans le télétravail, donc heu... fournir des ordinateurs pour leurs salariés, mais au-delà de ça, ce serait fournir heu... des trucs type... je sais pas, une clim, un ventilateur, des trucs un peu basiques comme ça, mais... pour qu'ils puissent aussi rester chez eux. Oui, voilà, pour heu... bah oui, fournir un... des conditions de travail aussi à domicile heu... adéquates, je sais pas comment...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 81, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des conditions", "des conditions", "ce cas", "les entreprises", "le télétravail", "des ordinateurs", "leurs salariés", "mais <PERSON><PERSON><PERSON><PERSON> de <PERSON>a", "des trucs type", "une clim", "un ventilateur", ", des trucs", "comme ça", "des conditions", "chez eux", "comme ça", "ce serait", "ce cas", "Comme ça", "comme ça", "des trucs", "comme ça", "ce cas", "ce cas", "Comme ça", "Comme ça", "<PERSON> tru<PERSON>", "comme ça", "les entreprises", "ce cas", "ce cas", "des trucs", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "les entreprises", "les entreprises", "ce cas", "comme ça", "les entreprises", "des trucs", "comme ça", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 60, "end": 62}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_062", "text": "<PERSON><PERSON>, je pense que... O<PERSON>, ou proposer une aide pour heu... équiper leur logement, quoi, pour qu'ils puissent travailler de chez eux si jamais c'est... pareil, difficultés dans les transports, je pense que ça va un peu de mise aussi, enfin du moins transports individuels, Bah transport.... oui, transport des marchandises, mais ça dépend, parce que si on est sur le... du tertiaire, le transport des marchandises est moins important que si on est sur une activité secondaire dans l'industrie, le... la raréfa... enfin les difficultés d'approvisionnement en matières premières peut amener les... l'industrie à devoir heu... être... couper sa chaîne de... enfin stopper sa chaîne de production, mais ça dépend totalement si on se place dans... voilà, dans l'industrie secondaire ou tertiaire, enfin dans l'économie... <PERSON><PERSON>, bah oui, oui, forcément. <PERSON><PERSON>, je pense qu'il faut voir un peu pour tous les cas, non ? En général ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 148, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6756756756756757}, "noun_phrases": ["une aide", "leur logement", "chez eux", "si jamais c'est... pareil, difficultés", "les transports", "enfin du moins transports individuels", "Bah transport", "oui, transport", ", le transport", "une activité secondaire", "le... la raréfa", "matières premières", "sa chaîne", "sa chaîne", "l'industrie secondaire ou tertiaire", "enfin dans l'économie", "un peu pour tous les cas", "matières premières", "les trans", "matières premières", "matières premières", "matières premières", "les transports", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "que ça", "matières premières", "Matières premières", ", transport", "<PERSON><PERSON>a", "tous les cas", "que ça", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 61, "end": 66}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_063", "text": "<PERSON><PERSON>, raré<PERSON>ction des matériaux, matières premières, donc là, ce serait heu... C'est mini... minimiser les trans... enfin pour le pétrole, minimiser les transports heu... de ses salariés. En<PERSON> optimiser, donc favoriser le covoiturage, favoriser les salariés locaux, enfin... <PERSON><PERSON>, on peut même imaginer mettre en place des navettes collectives pour heu... pour une boîte.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les salariés", "les salariés", "ses salariés", "ses salariés", "les salariés", "ses salariés", "les salariés", "les salariés", "les salariés", "ses salariés", "ses salariés", "une boîte", "les transports", "matières premières", "Alors, raréfaction", "matières premières", "les trans", "les transports heu", "le covoiturage", "les salariés locaux", "navettes collectives", "une boîte", "matières premières", "matières premières", "matières premières", "les transports", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "les salariés", "ce serait", "ses salariés", "ses salariés", "les salariés", "les salariés", "ses salariés", "une boîte", "ses salariés", "matières premières", "une boîte", "Matières premières"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 62, "end": 65}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_064", "text": "<PERSON><PERSON>, v<PERSON><PERSON>, le covoiturage collecti... enfin déplacement collectif. Inciter avec par exemple des remboursements à 100 % des frais de transport pour ceux qui utilisent des transports collectifs plutôt que les 50 % notamment, ça peut être une heu...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une heu", "le covoiturage", "le covoiturage collecti", "enfin déplacement collectif", "par exemple", "100 %", "des transports collectifs", "les 50 %", "une heu", "100 %", "une heu", ", ça", "100 %", "une heu", "une heu", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 63, "end": 65}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_065", "text": "Donc pour éviter d'utiliser heu... ceux-là.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 6, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 64, "end": 66}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_066", "text": "Et augmentation des coûts de matières premières, alors là heu... ce serait heu... je sais pas. De toute façon, pour moi, ça va avec la raréfaction, c'est que c'est la raréfaction qui va faire l'augmentation des coûts.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["pour moi", "matières premières", "matières premières", "Et augmentation", "matières premières", "toute façon", "la raréfaction", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "ce serait", ", ça", "matières premières", "Matières premières", ", ça", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 65, "end": 67}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_067", "text": "<PERSON><PERSON>, là, on peut imaginer entre la matière première et difficultés des transports, on pourrait imaginer des genres de regroupements, des collectivités d'entreprises pour heu... mutualiser justement leurs besoins en matières premières et peut-être plus heu... revenir un peu au système du troc, quoi. <PERSON><PERSON>, aller chercher moins loin des matières premières, chercher à se fournir plus local. Oui, déjà au plus local et aussi, peut-être, mutualiser les... si par exemple, je sais pas, là, y'a plusieurs entreprises sur le même secteur qui... qui font dans le même domaine ou heu... qui veulent distribuer au même endroit, bah peut-être mutualiser les transports, les marchandises ou l'achat de matières premières. Le communisme, quoi. Non, la mise en commun des maté... en gros, que les entreprises... O<PERSON>, peut-être lier des... créer des collectivités heu... entre entreprises, quoi.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 135, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["les entreprises", "les transports", "matières premières", "matières premières", "les trans", "par exemple", "matières premières", "la matière première et difficultés", "des genres", "leurs besoins", "matières premières", "un peu au système", "matières premières", "plusieurs entreprises", "le même secteur", "qui... qui font dans le même domaine ou heu... qui veulent distribuer au même endroit,", "les transports", ", les marchandises", "matières premières", "Le communisme", "des collectivités heu", "matières premières", "... Entre entreprises", "du troc", "du troc", "même endroit", "plusieurs entreprises", "matières premières", "matières premières", "matières premières", ", la mise", "plusieurs entreprises", "matières premières", "les entreprises", "Matières premières", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "les entreprises", "les entreprises", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 66, "end": 72}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_068", "text": "<PERSON><PERSON>, comment... comment le formuler, enfin je sais pas si tu as une idée ? <PERSON><PERSON> apr<PERSON>, on peut écrire comme on l'entend, ça... pour être sûr d'être compris. Bah « mutualiser », c'est déjà pas mal. Mutualiser l'achat de matières premières entre... Entre entreprises.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 45, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["pas mal", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "une idée", "matières premières", "... Entre entreprises", "matières premières", "matières premières", "matières premières", "pas mal", ", ça", "matières premières", "Matières premières", ", ça", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 67, "end": 71}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_069", "text": "Entre entreprises, oui, enfin ou entre heu... Mutualiser les déplacements pour heu... réduire l'impact et... oui, faire du troc, enfin faire du troc, comme tu dis, de pas jeter, enfin... Mais du coup, ça se limiterait pas qu'aux déplacements, alors. Parce que tu vois, souvent, y'a des... des sortes de... enfin les entreprises, souvent, elles peuvent être heu... placées au même endroit. Là, je prends des choses différentes, tu vois souvent des agglomérations d'entreprises heu... dans les zones industrielles ou trucs comme ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 83, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des choses", "des choses", "les entreprises", "comme ça", "les déplacements", "du troc", "du troc", "qu'aux déplacements", "des sortes", "de... enfin les entreprises", "même endroit", "des choses différentes", "entreprises heu", "les zones industrielles", "comme ça", "du coup", "des choses", "Comme ça", "comme ça", "<PERSON><PERSON> du <PERSON>", "comme ça", "des choses", "du coup", "<PERSON><PERSON> du <PERSON>", "Comme ça", "<PERSON><PERSON> du <PERSON>", "Comme ça", ", ça", "comme ça", "<PERSON><PERSON> du <PERSON>", "ses heu", "les entreprises", "du coup", "du coup", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "<PERSON><PERSON> du <PERSON>", "du coup", "les entreprises", "du coup", ", ça", "les entreprises", "comme ça", "les entreprises", "comme ça", "enfin les entreprises", "des choses", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 68, "end": 72}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_070", "text": "Au final, c'est au même endroit et on peut avoir des achats de matières premières similaires.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des achats", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "même endroit", "des achats", "matières premières", "matières premières", "matières premières", "matières premières", "Matières premières", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 69, "end": 71}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_071", "text": "Après, du coup, là, c'est... enfin le coût des matières resterait toujours élevé, enfin ça... <PERSON><PERSON>, mais ce que je veux dire, plus t'achètes en gros, plus tu fais des économies d'échelle et du coup, si après tu repartages entre plusieurs entreprises, ce serait con qu'y'en ait une...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 48, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["Et du coup", "plusieurs entreprises", "<PERSON><PERSON>, du coup", "enfin le coût", "des économies", "du coup", "plusieurs entreprises", "le coût", "le coût", "le coût", "le coût", "le coût", ", du coup", "ce serait", "Et du coup", "plusieurs entreprises", "Et du coup", "le coût", ", du coup", "du coup", "Et du coup", "Et du coup", ", du coup", "Et du coup", "Et du coup", ", du coup", ", du coup", ", du coup", "Et du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", c'", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 70, "end": 72}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_072", "text": "admettons, on veut acheter du blé, y'en a une qui achète une tonne de blé, l'autre 500 grammes et qu'au final, c'est que des achats différents et donc des transports différents. Ça se fait déjà entre habitants, y'en a plein qui font ça, hein. Je sais que dans nos communes, souvent, par exemple, pour heu... je sais pas, pour le chauffage ou le bois, etc., on achète en gros et comme ça, c'est plus rentable, en fait. Voilà, on fait venir une seule fois pour économiser les coûts du transport. Voilà, c'est ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 93, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["des achats", "C'est ça", "C'est ça", "comme ça", "par exemple", "comme ça", "des achats", "une tonne", ", l'autre 500 grammes", "qu'au final, c'est que des achats", "donc des transports", "nos communes", ", souvent, par exemple", "le chauffage", "le bois", "et comme ça", "une seule fois", "les coûts", "Voilà, c'est ça", "Voilà, c'est ça", "en fait", "Comme ça", "comme ça", "comme ça", "Comme ça", "Comme ça", "comme ça", "C'est ça", "le chauffage", ", c'", ", l'autre", "comme ça", "comme ça", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 71, "end": 76}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_073", "text": "Et enfin ça fonctionne bien. Donc on dit « mutualiser les... » ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 72, "end": 74}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_074", "text": "Bah mutualiser pour optimiser les... le coût de transports et les négociations, et le coût des matières premières ? Oui, voilà.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "le coût", "les négociations", "le coût", "matières premières", "le coût", "matières premières", "le coût", "matières premières", "le coût", "le coût", "matières premières", "Matières premières"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 73, "end": 75}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_075", "text": "S'il y a des difficultés, il faut... enfin si... enfin v<PERSON>, plutôt qu'y'ait trois camions qui... trois camions qui fournissent la moitié... à moitié vides, si y'en a un... un seul, enfin... <PERSON><PERSON>, c'est ça. On peut toujours gagner... oui, c'est ça. Perturbations du marché de l'emploi et flux migratoires ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 51, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "des difficultés", "plutôt qu'y'ait trois camions", "la moitié", "moitié vides", "<PERSON><PERSON>, c'est ça", "oui, c'est ça", "flux migratoire", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "flux migratoires", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 74, "end": 79}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_076", "text": "Une politique bienveillante envers l'inclusion des personnes... enfin des étrangers, des personnes heu... qui migrent, avec heu... un... un maximum de communication avec les salariés pour qu'ils puissent, chacun... parce que forc... enfin ceux qui sont... qui travaillaient avant dans l'entreprise apprennent à travailler avec de nouvelles personnes et à... qu'ils aient une... une démarche bienveillante envers les nouvelles personnes qui vont arriver, qui n'auront pas la même culture, pas les mêmes heu... le même vécu, pour les intégrer au mieux sur le... dans l'entrepri... enfin dans l'organisation .", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 89, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une démarche", "les salariés", "les salariés", "les salariés", "les salariés", "les salariés", "les salariés", "les nouvelles personnes", "Une politique", "Une politique bienveillante", ", des personnes", "un... un maximum", "les salariés", "dans l'entreprise", "de nouvelles personnes", "une... une démarche bienveillante", "les nouvelles personnes", "la même culture", "une politique", "une démarche", "enfin ce", "un maximum", "les salariés", "les salariés", "une démarche", "des personnes", "une politique", "une po"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 75, "end": 77}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_077", "text": "Donc avoir une politique d'inclusion au sein de l'entreprise, quoi. Voilà, c'est ça. Oui. Et peut-être, heu... oui, parce que les flux migratoires... mettre en place, peut-être, des nouveaux contrats. Après, des nouveaux contrats, sous quelle fo... ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["C'est ça", "C'est ça", "Une politique", "Voilà, c'est ça", "une politique", "Voilà, c'est ça", "peut-être, des nouveaux contrats", "<PERSON><PERSON>, des nouveaux contrats", "quelle fo", "flux migratoire", "C'est ça", "flux migratoires", "une politique", "une po", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 76, "end": 81}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_078", "text": "Enfin sous quelle forme ? Je sais qu'elles peuvent être limitées en CDD, et le CDI, les gens seront de moins en moins réticents, enfin seront de plus en plus réticents, justement.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["quelle fo", "Enfin sous quelle forme", "les gens"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 77, "end": 79}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_079", "text": "Donc je sais pas trop. <PERSON><PERSON>, enfin là, c'est... Flux migratoires, je sais que là, y'a une amie expert-comptable... ah non.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une amie expert", "flux migratoire", "flux migratoires", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 78, "end": 81}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_080", "text": "Si, si, si, elle a eu du mal à recruter et elle a dû prendre un gars, un migrant, quoi, enfin un migrant étranger, mais qui n'a pas la nationalité et heu... elle en a quand même chié au niveau des papiers pour faire la demande juste pour qu'il puisse travailler chez elle, quoi. Donc peut-être heu... après, ça, c'est de la politique ... C'est la politique, c'est l'État qui fixe les conditions pour pouvoir embaucher une personne heu... C'est pas l'entreprise qui choisit, du coup.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 86, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les condi", "du coup", "du mal", "un gars", "un migrant", "enfin un migrant étranger", "la nationalité", "la demande", "a<PERSON><PERSON>ès, ça", ", c'est l'État", "les conditions", "une personne heu", ", du coup", "la politique", "une personne", "une personne", ", du coup", "de la politique", "du coup", "la politique", ", du coup", ", ça", ", du coup", ", du coup", ", du coup", "du coup", "du coup", "la politique", "la politique", ", du coup", "du coup", "du coup", ", ça", ", c'est l'État", ", c'", "une personne", "c'est pas", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 79, "end": 83}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_081", "text": "Non, c'est... l'entreprise doit se conformer au Droit du Travail, avec heu... le certificat... enfin en gros, on a toute une... un formalisme vis-à-vis de la préfecture, enfin de s'assurer que la personne ait bien le droit de... d'être en France, qu'elle ait... qu'il ait le droit de travailler en France. Et ensuite, l'entreprise, y'a des délais à respecter pour pouvoir ensuite embaucher la personne, sachant que souvent, s'il n'a... on se rend compte souvent qu'on a des problèmes avec les justificatifs qui ne sont pas forcément des documents officiels dans d'autres... enfin qui sont officiels dans d'autres pays, mais qui sont pas reconnus officiellement en France, du coup, les problèmes administratifs pour heu... pouvoir factuellement embaucher la personne, et juste être... même une personne qu'on a embauchée, qui a permis... perdu son permis, son droit à... de travailler en France, peut se retrouver... du coup, l'employeur peut se retrouver emmerdés parce que la personne qui... qu'il embauche actuellement n'est... n'est plus conforme au Droit français. Alors que la personne travaille actuellement en CDI.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 174, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.2298850574712644}, "noun_phrases": ["des problèmes", "des problèmes", "des problèmes", "des problèmes", "du coup", ", du coup", "le certificat", "un formalisme vis-à-vis", "la préfecture", "la personne", "le droit", "le droit", "des <PERSON>", "la personne", "des problèmes", "les justificatifs", "d'autres pays", "la personne", "son droit", "parce que la personne", "Droit français", "la personne", "des problèmes", "Les problèmes", "une personne", "une personne", "des problèmes", ", du coup", "des problèmes", "du coup", ", du coup", "la personne", ", du coup", ", du coup", "... du coup", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", c'", "une personne", ", du coup", "des problèmes", "des problèmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 80, "end": 83}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_082", "text": "Ah oui, oui, donc c'est... c'est un peu complexe, du coup, ce... Mais c'est pas trop l'entreprise, c'est... c'est au-dessus.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", ", du coup", ", du coup", "du coup", ", du coup", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", c'", "c'est pas", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 81, "end": 83}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_083", "text": "<PERSON><PERSON>, et surtout ceux heu... les accueillir, enfin dignement, enfin les accueillir et les intégrer, ne pas... enfin ne pas les mettre dans un coin, « tu vas faire ça », ou même ne pas leur confier les... les basses tâches, parce que soi... enfin ils auraient plus de problèmes de communication. Enfin c'est... oui, oui. C'est de les intégrer au même titre que... Au même titre qu'une personne heu... un salarié lambda. OK.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 74, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une personne heu", "ceux heu", "enfin dignement", "un coin", "les... les basses tâches", "parce que soi", "même titre", "que... Au même titre", "qu'une personne heu", "une personne", "une personne", "un salarié", "une personne"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 82, "end": 87}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_084", "text": "<PERSON><PERSON> du coup, ça rentre dans la politique d'inclusion. <PERSON><PERSON>, pour moi, l'inclusion, ça englobe tout. <PERSON><PERSON>, c'est ça. Risques environnementaux ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["pour moi", "C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "du coup", "<PERSON><PERSON>, c'est ça", "oui, c'est ça", "la politique", "<PERSON><PERSON>, c'est ça", "Risques environnementaux", "Risques environnementaux", "risques environnementaux", "<PERSON><PERSON>, c'est ça", "du coup", "la politique", "<PERSON><PERSON>, c'est ça", ", ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "du coup", "du coup", "la politique", "la politique", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "du coup", "du coup", ", ça", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 83, "end": 87}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_085", "text": "Donc heu... ça, c'est lié genre au... À la santé, je pense, des salariés, enfin... parce que les risques environnementaux, dans les faits, oui, ça va surtout être sur... la santé de ses... de son person... enfin des salariés, parce que... enfin je sais pas ce que tu en penses, tu parles moins, du coup, vu que tu écris. <PERSON><PERSON>, désol<PERSON>. Qu'est-ce que tu penses, dans les risques environnementaux, pour l'organisation ? <PERSON><PERSON>, ça me parle pas, honnêtement. Pa<PERSON>e qu'après, ça peut être type inondations, tout ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 87, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["des salariés", "tout ça", "des salariés", "tout ça", "tout ça", "du coup", "a<PERSON><PERSON>ès, ça", ", du coup", "Risques environnementaux", "... À la santé", "les faits", "être sur... la santé", "son person", "enfin des salariés", "O<PERSON>, désolée", "les risques environnementaux", "tout ça", "Risques environnementaux", "Les risques", "risques environnementaux", ", tout ça", "les risques", "tout ça", "les risques", "la santé", "tout ça", ", du coup", "la santé", "du coup", ", du coup", "les faits", "des salariés", ", tout ça", ", ça", "tout ça", ", du coup", ", du coup", "tout ça", "tout ça", ", du coup", "du coup", "du coup", ", du coup", "tout ça", "du coup", "du coup", ", ça", ", c'", ", du coup", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 84, "end": 90}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_086", "text": "Risques environnementaux, du coup, bah typiquement, il fait trop chaud, du coup toutes les lignes électriques sautent, donc plus d'électricité pendant un temps, les centrales nucléaires ne peuvent pas tourner, parce que y'a plus assez d'eau dans les fleuves, donc des problèmes d'é... d'énergies. Les risques... enfin ça fait partie des risques environnementaux.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des problèmes", "des problèmes", "des problèmes", "des problèmes", "du coup", ", du coup", "des problèmes", "Risques environnementaux", "Risques environnementaux", "toutes les lignes électriques", "un temps", "les centrales nucléaires", "les fleuves", "des problèmes", "Les risques", "risques environnementaux", "les risques", "les risques", "des problèmes", ", du coup", "des problèmes", "du coup", ", du coup", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", du coup", "des problèmes", "des problèmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 85, "end": 87}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_087", "text": "Si on a une... typiquement... Al<PERSON> juste, je... alors du coup, juste pour vous aiguiller, enfin vous faire avancer potentiellement sur la partie légitimité, on a fait des petites cartes qui sont des cartes « parties prenantes », et en fait, ce qu'on vous propose, c'est de regarder, voir si y'en a pas certaines que vous avez zappées dans la légitimité, parce que c'est notamment lié au ou que vous avez envie de rajouter, etc., enfin voil<PERSON>, vous regardez. Et ce que je vous demande juste, c'est heu... dès que vous utilisez une carte, enfin si cette carte-là vous aide ou vous questionne, même si vous mettez rien mais vous avez un débat dessus, vous la mettez de côté, comme ça on sait qu'elle vous a servi et qu'à la fin, y'a 5-6 cartes vides, donc si vous avez aussi des choses où vous dites « ça, par contre, ils l'ont pas mis, mais quand même, c'est important pour nous », et voilà. Ça va ? Oui, très bien.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 169, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.591715976331361, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la partie", "la partie", "la fin", "des choses", "la légitimité", "la légitimité", "la partie", "des choses", "La légi", "la légitimité", "comme ça", "comme ça", "du coup", "alors du coup", "la partie légitimité", "des petites cartes", "parties prenantes", "en fait", "la légitimité", "une carte", "un débat dessus", "5-6 cartes vides", "des choses", "Comme ça", "comme ça", "comme ça", "des choses", "la légitimité", "du coup", "Comme ça", "la légitimité", "la partie", "Comme ça", "des cartes", "la légitimité", "des cartes", "la fin", "comme ça", "parties prenantes", "du coup", "du coup", "du coup", "du coup", ", c'", "une carte", "comme ça", "comme ça", "des choses", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 86, "end": 90}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_088", "text": "Ensuite, une... ah, « en cas de panne ». <PERSON>, on essaie de finir en discutant. <PERSON><PERSON>, on essaie. <PERSON><PERSON>, je pense, on les défile pour voir si on en a parlé. Oui, voilà, oui. Heu...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 36, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Ensuite, une... ah, « en cas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 87, "end": 93}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_089", "text": "donc les risques environ... Du coup, c'est lié à la fois à la sécurité heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 15, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la fois", "du coup", "Les risques", "donc les risques", "la fois", "la sécurité heu", "la sécurité", "La sécurité", "les risques", "la sécurité", "les risques", "la fois", "du coup", "la fois", "... du coup", "du coup", "du coup", "du coup", "du coup", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 88, "end": 90}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_090", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sant<PERSON> des salariés. Santé et lié à l'énergie aussi. Les problèmes d'approvisionnement d'énergies, du coup, la prise en compte de ces risques,", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la prise", "la prise", "la prise", "des salariés", "la prise", "des salariés", "du coup", ", du coup", ", sant<PERSON>", "Les problèmes", ", la prise", "ces risques", "la prise", ", du coup", "du coup", ", du coup", "des salariés", ", du coup", ", du coup", "la prise", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 89, "end": 92}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_091", "text": "enfin de ces... <PERSON><PERSON>, ce serait déjà avoir des bâtiments heu... avec plusieurs sources d'énergies, pour pallier un peu à ça. Et du coup, des bâtiments assez innovants en termes de construction. En 2050, je pense qu'on a... on a le temps d'améliorer et de reconstruire, peut-être.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 47, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Et du coup", "du coup", "enfin de ces... <PERSON>i", "ce serait", "des bâtiments heu", "plusieurs sources", "un peu à ça", "Et du coup", ", des bâtiments", "le temps", "des bâtiments", "Et du coup", "du coup", "Et du coup", "Et du coup", "Et du coup", "Et du coup", "Et du coup", "du coup", "du coup", "le temps", "du coup", "du coup", "le temps"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 90, "end": 93}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_092", "text": "Bref. <PERSON><PERSON><PERSON><PERSON> qu'y'a pas mal de bâtiments avec de l'amiante, mais... <PERSON><PERSON>, voil<PERSON>, le problème de reconstruire... On peut imaginer déjà des bâtiments innovants, en termes d'énergies et de... et pareil, tu vois, cas sismiques, y'a des bâtiments qui existent là-dessus, inondations pareil, tu vois, avoir des bâtiments sûrs, qui assurent la sécurité et... La sécurité et la prise en... Et le maintien de l'énergie, tout ça.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 67, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la prise", "la prise", "la prise", "tout ça", "la prise", "tout ça", "pas mal", "tout ça", "tout ça", "de l'amiante", "le problème", "des bâtiments innovants", "cas sismiques", "des bâtiments", ", inondations", "des bâtiments sûrs", "la sécurité", "La sécurité", "la prise", "<PERSON>t le maintien", ", tout ça", ", le problème", "tout ça", "des bâtiments innovants", "la sécurité", "tout ça", "pas mal", "le problème", ", le problème", ", tout ça", "tout ça", "tout ça", "tout ça", "la prise", "le problème", "tout ça", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 91, "end": 95}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_093", "text": "<PERSON><PERSON>, et la prise en compte, enfin avec l'augmentation de la pollution, l'augmentation des problèmes de santé des salariés, du coup, la mise en place de prévention. <PERSON><PERSON>, peut-être heu... après, je sais qu'elles proposent toutes un peu déjà une prévention santé en plus, la mutuelle, tout ça. Ah oui , dans ce sens-là ? Alors ça dépend, parce que les mutu...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 62, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["la prise", "la prise", "la prise", "des salariés", "tout ça", "des problèmes", "des problèmes", "la prise", "des salariés", "des problèmes", "des problèmes", "tout ça", "tout ça", "du coup", ", du coup", "des problèmes", "tout ça", "des problèmes", "la prise", ", tout ça", "enfin avec l'augmentation", "la pollution", ", la mise", "toutes un peu déjà une prévention santé", "tout ça", "tout ça", "des problèmes", ", du coup", "des problèmes", "du coup", ", du coup", "des salariés", ", tout ça", "tout ça", ", du coup", ", du coup", "tout ça", "tout ça", "la prise", ", du coup", "du coup", "du coup", ", du coup", "tout ça", "du coup", "du coup", ", du coup", "tout ça", "des problèmes", "des problèmes", "Alors ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 92, "end": 97}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_094", "text": "les mutuelles sont obligatoires, mais pas les prévoyances, par exemple, enfin y'a... Ah oui", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["par exemple", "les mutuelles", "pas les prévoyances"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 93, "end": 95}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_095", "text": "Voilà, et faire... faire également de la prévention, enfin notamment faire venir par exemple des intervenants sur les risques heu... au travail, les risques... enfin par exemple, les emplois de bureau, les risques musculo-squelettiques, le problème de burn... les risques de burn-out, la pression . Ou même, on peut imaginer en 2050, dans un monde parfait, que toute entreprise a en... un service santé et bien-être.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["par exemple", "Les risques", "le problème", "la prévention", "les risques heu", ", les risques", ", les risques", ", le problème", "les risques", ", la pression", "un monde parfait", "toute entreprise", "un service santé", "des intervenants", "les risques", "le problème", ", le problème", "toute entreprise", "le problème", "la pression", "un monde", "la pression", "un service", "Et bien-être"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 94, "end": 98}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_096", "text": "Donc y'a peut-être potentiellement un médecin, Heu... oui, après... Utopique, hein, utopique.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un médecin", "un médecin"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 95, "end": 97}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_097", "text": "La plupart des entreprises françaises étant de moins de... enfin n'ayant que un ou deux salariés... O<PERSON>, mais on peut s'imaginer qu'à partir de... d'un certain nombre de salariés, mettre ça en place. Comme ça, ça permet... tu vois, aux déserts médicaux, aussi, des fois, y'a des entreprises qui sont dans des déserts médicaux, au moins, ils ont un médecin dans la boîte, quoi. Ou kiné ou un truc comme ça. Mais du coup ...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 75, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des entreprises", "la boîte", "la boîte", "la boîte", "la boîte", "comme ça", "comme ça", "du coup", "un médecin", "La plupart", "entreprises françaises", "que un ou deux salariés", "un certain nombre", "Comme ça", "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "au<PERSON>, des fois", "des entreprises", "des déserts médicaux", "un médecin", "la boîte", "<PERSON><PERSON> kiné", "un truc", "comme ça", "<PERSON><PERSON> du <PERSON>", "comme ça", "la boîte", "un truc", "la boîte", "un truc", "du coup", "<PERSON><PERSON> du <PERSON>", "un truc", "des entreprises", "la boîte", "la boîte", "des fois", "la boîte", "des entreprises", "Comme ça", "<PERSON><PERSON> du <PERSON>", "Comme ça", "un truc", ", ça", "un certain nombre", "comme ça", "<PERSON><PERSON> du <PERSON>", "la boîte", "des fois", "du coup", "du coup", "des entreprises", "<PERSON><PERSON> du <PERSON>", "un truc", "du coup", "du coup", ", ça", "comme ça", "comme ça", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 96, "end": 102}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_098", "text": "sauf que pour avoir une personne à temps ple... enfin voil<PERSON>, une personne à temps plein, donc ils devraient le mutualiser entre plusieurs entreprises, donc heu... il serait... le médecin serait là un jour par semaine, voire un jour par... tous les quinze jours, par mois, enfin dans l'entreprise. Oui, c'est ça. Et du coup, ce serait toutes les entreprises qui mutualiseraient le coût pour le bien-être heu... des salariés.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le bien-être", "le bien-être", "le bien-être", "des salariés", "C'est ça", "C'est ça", "des salariés", "Et du coup", "les entreprises", "<PERSON><PERSON>, c'est ça", "plusieurs entreprises", "du coup", "plusieurs entreprises", "le coût", "le coût", "le coût", "le coût", "le coût", "<PERSON><PERSON>, c'est ça", "oui, c'est ça", "dans l'entreprise", "<PERSON><PERSON>, c'est ça", "ce serait", "Et du coup", "une personne", "temps ple", "une personne", "temps plein", "plusieurs entreprises", "le médecin", "voire un jour", "... tous les quinze jours", "enfin dans l'entreprise", "<PERSON><PERSON>, c'est ça", "Et du coup", "le coût", "le bien-être heu", "... des salariés", "le médecin", "Le médecin", "le médecin", "du coup", "Et du coup", "Et du coup", "Et du coup", "toutes les entreprises", "des salariés", "<PERSON><PERSON>, c'est ça", "Et du coup", "le bien-être", "le bien-être", "<PERSON><PERSON>, c'est ça", "C'est ça", "les entreprises", "<PERSON><PERSON>, c'est ça", "Et du coup", "du coup", "du coup", "les entreprises", "<PERSON><PERSON>, c'est ça", "les entreprises", "Les entreprises", "les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "<PERSON><PERSON>, c'est ça", "du coup", "les entreprises", "du coup", "les entreprises", ", c'", "une personne", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 97, "end": 100}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_099", "text": "Donc bon courage pour écrire tout ça. Bah... tu as marqué quoi, déj<PERSON> ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["tout ça", "tout ça", "tout ça", "tout ça", "tout ça", "tout ça", "tout ça", "tout ça", "tout ça", "tout ça", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 98, "end": 100}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_100", "text": "C'est ça, prévention et tout ça. Un centre médical, ça peut comprendre à la fois les kinés, les machins...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la fois", "tout ça", "C'est ça", "C'est ça", "tout ça", "tout ça", "tout ça", "la fois", "tout ça", "un centre médical", "un centre médical", ", prévention", "tout ça", "Un centre médical", "la fois", "les kinés", ", les machins", "centre médical", "la fois", ", ça", "tout ça", "tout ça", "tout ça", "C'est ça", "tout ça", ", ça", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 99, "end": 101}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_101", "text": "<PERSON><PERSON>, kin<PERSON>, ost<PERSON><PERSON>, des diététiciens, parce que l'hygiène alimentaire va de plus en plus... enfin provoque des problèmes de santé, du coup, bah la prévention est bien mieux que le soin en lui-même. Mais ça, ça existe déjà, non ? Normalement, dans les EHPAD et tout, y'a déjà des trucs comme ça, non ? Y'en a, mais... <PERSON><PERSON>, mais que dans les boîtes au niveau social.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON> b<PERSON>", "des problèmes", "des problèmes", "des problèmes", "des problèmes", "les boîtes", "comme ça", "comme ça", "du coup", ", du coup", "des problèmes", "des problèmes", "la prévention", "Comme ça", "comme ça", ", ostéo", ", des diététiciens", "l'hygiène alimentaire", "des problèmes", ", du coup", "les EHPAD", "des trucs", "comme ça", "que dans les boîtes", "niveau social", "des problèmes", "du coup", ", du coup", "Comme ça", "Comme ça", "<PERSON> tru<PERSON>", ", ça", "comme ça", ", du coup", ", du coup", ", du coup", "du coup", "du coup", "des trucs", "<PERSON><PERSON>a", ", du coup", "du coup", "du coup", ", ça", ", du coup", "des problèmes", "comme ça", "des trucs", "comme ça", "des problèmes", "<PERSON><PERSON>a", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 100, "end": 105}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_102", "text": "OK, pas dans les autres ? Non, les socio-médicaux, oui, mais les entreprises privées, t'as pas ça. Après, t'as le médecin du travail qui est mutualisé, mais c'est que quand t'as un problème et c'est pour avoir une certification.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["les entreprises", "le médecin", "Non, les socio-médicaux", "le médecin", "un problème", "une certification", "Le médecin", "le médecin", "les entreprises", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "les entreprises", "les entreprises", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 101, "end": 104}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_103", "text": "Le médecin du travail, c'est une obligation légale, donc si un... un salarié a un arrêt de travail de plus de x jours, il a l'obligation d'aller voir le médecin du travail avant de reprendre le travail, ou la visite heu... T'as une visite annuelle, aussi. <PERSON><PERSON> dépend, la visite annuelle, c'est uniquement pour ceux qui travaillent la nuit, pour heu... ceux qui travaillent en poste, sinon c'est des visites tous les trois ans, enfin ça dépend, trois ou cinq ans, je m'en rappelle plus, enfin... Oui, bon, c'est... c'est assez limité, quoi.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 93, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le médecin", "le médecin", "Le médecin", "un salarié", "un arrêt", "plus de x jours", "le médecin", "le travail", "une visite annuelle", "la visite annuelle", ", c'est uniquement pour ceux", "la nuit", "sinon c'est des visites", "tous les trois ans", "le travail", "le travail", "le travail", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 102, "end": 105}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_104", "text": "Voilà, c'est... et puis pour avoir fait une... la visite en visio, « vous avez des problèmes de santé ? Non ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des problèmes", "des problèmes", "des problèmes", "des problèmes", "des problèmes", "des problèmes", "des problèmes", "c'est... et puis pour avoir fait une... la visite en visio,", "des problèmes", ", c'", "des problèmes", "des problèmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 103, "end": 105}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_105", "text": "Vous regardez... votre... est-ce que vous avez un ou deux écrans ? Deux ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un ou deux écrans"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 104, "end": 106}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_106", "text": "Bah négoc... enfin du coup, oui.... Nouvelles attentes, bah après, on a pas mal parlé, entre la santé... Bah la participation des salariés dans la gestion de... de l'entreprise. Oui. Avoir peut-être heu... un peu plus de respon... de responsabilités, parce que y'a aussi de moins en moins de gens qui veulent de... des responsabilités, hein.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la gestion", "des salariés", "des salariés", "pas mal", "du coup", "la santé", "... nouvelles attentes", "Nouvelles attentes", "la santé", "Bah la participation", "la gestion", "... de l'entreprise", "... un peu plus de respon", "... des responsabilités", "la gestion", "pas mal", "du coup", "la gestion", "des salariés", "du coup", "du coup", "enfin du coup", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 105, "end": 110}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_107", "text": "Mais sans forcément leur donner... enfin dire : si vous voulez... Leur donner l'opportunité de participer à... à la politique de l'entreprise. Voilà, de participer à la gestion de l'entreprise.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la gestion", "la politique", "la gestion", "... à la politique", "la gestion", "la politique", "la gestion", "la politique", "la politique"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 106, "end": 108}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_108", "text": "<PERSON>h typiquement, dé<PERSON><PERSON>, la transparence, communiquer avec eux, comment sont prises les décisions. Et ensuite, avant les prises de décisions, écouter heu... échanger avec les parties pre... enfin les salariés ou les représentants des salariés sur « comment est-ce que on a pris cette décision, qu'est-ce que vous en pensez ? ». Vous hésitez pas, si y'a des choses à rajouter, à... à écrire à l'arrière de la feuille, hein. Ah oui, oui, ça marche.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 75, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["les salariés", "les salariés", "des choses", "des salariés", "les salariés", "la transparence", "les salariés", "les salariés", "les salariés", "la transparence", "des choses", "des salariés", "les salariés", "des choses", "les prises", "déjà, la transparence", "les décisions", "Et ensuite, avant les prises", "les parties pre", "enfin les salariés", "les représentants", "cette décision", "des choses", "la feuille", ", oui, ça marche", "... enfin les salariés", "les décisions", "les décisions", "des salariés", "les salariés", "les décisions", "les salariés", "la transparence", "la transparence", ", ça", "la feuille", ", ça", "des choses"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 107, "end": 112}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_109", "text": "<PERSON><PERSON>, et puis heu... Nouvelles attentes de la société, ça va... Y'a pas mal de choses qu'on a déjà dites, qui peuvent rentrer là-dedans. Oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["la société", "pas mal", "... nouvelles attentes", "Nouvelles attentes", "et puis heu... Nouvelles attentes", "la société", "pas mal", "la société", "la société", "la société", ", ça", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 108, "end": 110}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_110", "text": "Par con<PERSON>, « renforcements sécuritaires, nouveaux conflits » ? <PERSON><PERSON>, « nouvelles attentes des consommateurs », bah pfff...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Nouvelles attentes", "contre, « renforcements sécuritaires", ", nouveaux conflits", "<PERSON><PERSON>, « nouvelles attentes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 109, "end": 111}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_111", "text": "<PERSON><PERSON>, consommate<PERSON>, socié<PERSON>, pour moi ça va plutôt en... enfin les salariés font partie... enfin pour moi, c'est lié, c'est les nouvelles attentes. Bah tu vois, là, moi, dans mes critères de performance, j'avais mis « participation aux actions locales et investissement dans la ville ».", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["les salariés", "les salariés", "enfin pour moi", "pour moi", "les salariés", "les salariés", "les salariés", "les salariés", "Enfin pour moi", "les salariés", "Nouvelles attentes", "enfin les salariés", "... enfin les salariés", "là, moi", "mes critères", "actions locales", "la ville", "les salariés", "enfin pour moi", "les salariés", "de performance", "enfin pour moi", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 110, "end": 112}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_112", "text": "Donc ça peut être aussi ça dans les... dans la légitimité, est-ce qu'une entreprise doit s'investir localement et dans la ville ? Donc ça rentre pour les consommateurs et la société, quoi. Y'a un truc du local, je crois, je sais plus où ? Bah on avait marqué « chercher à se fournir le plus localement possible ». Pour les matières. Fournir et produire heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 65, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "la société", "la légitimité", "la légitimité", "La légi", "la légitimité", "la légitimité", "un truc", "la société", "la ville", "être aussi ça", "la légitimité", "une entreprise", "et dans la ville", "les consommateurs", "la société", "un truc", "les matières", "un truc", "un truc", "une entreprise", "la société", "la société", "la légitimité", "un truc", "la légitimité", "une entreprise", "si ça", "une entreprise", "un truc"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 111, "end": 117}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_113", "text": "oui, enfin du coup, ça va ensemble. Peut-être, en... oui, mais ça peut être en investissement, heu... oui, social, tu vois. <PERSON><PERSON>, ou s'engager à vendre typiquement ses produits uniquement localement. C'est ça, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["C'est ça", "C'est ça", "du coup", "ses produits", "du coup", ", ça", "C'est ça", "du coup", "du coup", "<PERSON><PERSON>a", "enfin du coup", "du coup", "du coup", ", ça", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 112, "end": 116}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_114", "text": "C'est-à-dire même si on pro... on aurait des... Tu peux mettre « en investissement heu... de l'entreprise local et sociétal ». Et « renforcements sécuritaires », bah... avoir aussi un service militaire dans.... dans la boîte, à côté du centre médical. Ou près d'un bunker ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["la boîte", "la boîte", "la boîte", "la boîte", "la boîte", "... de l'entreprise", "la boîte", "investissement heu", "Et « renforcements sécuritaires", "un service militaire", "la boîte", "centre médical", "Ou près d'un bunker", "la boîte", "la boîte", "la boîte", "la boîte", "un service"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 113, "end": 117}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_115", "text": "Alors je... je dirais bien un truc, mais là, on a... on arrive sur de la politique, ce serait communiquer, enfin faire de la pédagogie politique, enfin sur les... la situation politique, généralement, plus les personnes se... s'informent, moins elles sont inspirées, enfin attirées par l'extrême droite et du coup, moi ça... ou pour le populisme, plus... en gros, y'a de démocratie, plus les gens goûtent à la démocratie, plus ils ont envie de... enfin il faut inciter la démocratie à faire de la pédagogie, enfin de pas hésiter à communiquer, à échanger sur les... même sur de la politique en entreprise, ce qui peut être très compliqué. Mais du coup, pour heu... afin de minimiser, mais même si ça restera à l'échelle de l'organisation, minimiser les...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 127, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["Et du coup", "du coup", "les gens", "la politique", "ce serait", "Et du coup", "un truc", "<PERSON><PERSON> du <PERSON>", "Et du coup", "un truc", "un truc", "de la politique", "la pédagogie politique", "la situation politique", "généralement, plus les personnes", "l'extrême droite", "du coup", "ou pour le populisme", "de démocratie", "plus les gens", "la démocratie", "la démocratie", "la pédagogie", "même sur de la politique", "<PERSON><PERSON> du <PERSON>", "extrême droite", "la pédagogie", "la pédagogie", "Et du coup", "la politique", "un truc", "la démocratie", "plus les personnes", "Et du coup", "Et du coup", "<PERSON><PERSON> du <PERSON>", "un truc", "Et du coup", "<PERSON><PERSON> du <PERSON>", "si ça", "Et du coup", "du coup", "du coup", "la politique", "la politique", "<PERSON><PERSON> du <PERSON>", "un truc", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 114, "end": 116}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_116", "text": "ce qu'on pourrait appeler les votes populistes, les votes d'extrême droite, pour heu... limi... limiter le... les politiques sécuritaires et la propa... enfin la propagande heu... <PERSON><PERSON>, du coup, ce serait quoi ? Avoir heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", ", du coup", "ce serait", ", du coup", "du coup", "les votes populistes", "les votes", "extrême droite", "le... les politiques sécuritaires", "la propa", "enfin la propagande heu", "<PERSON><PERSON>, du coup", ", du coup", "les votes", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 115, "end": 118}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_117", "text": "Bah soit consacrer des temps d'échanges informels, enfin des échanges sur l'actualité pour heu... mais le problème, c'est que là, factuellement... Après, l'entreprise... après, ce serait plus les entreprises publiques, dans ce cas-là ? Ça peut être des entreprises publiques, mais les entreprises publiques ont un énorme droit... devoir de réserve vis-à-vis de la politique publique.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des entreprises", "ce cas", "les entreprises", "la politique", "ce serait", "le problème", "ce cas", "des entreprises", "de la politique", "des temps", "enfin des échanges", "le problème", "Après, l'entreprise", "ce cas", "les entreprises publiques", "un énorme droit", "réserve vis-à-vis", "la politique publique", "ce cas", "la politique", "des entreprises", "des entreprises", "les entreprises", "le problème", "ce cas", "ce cas", "les entreprises", "la politique", "la politique", "des entreprises", "les entreprises", "Les entreprises", "les entreprises", "les entreprises", "les entreprises", ", c'", "ce cas", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 116, "end": 118}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_118", "text": "<PERSON><PERSON>, v<PERSON><PERSON>, de la pédagogie, donner... enfin typiquement, je sais que moi, j'ai mis en place des newsletters et dans ma newsletter, y'en a certaines où je mets sur les... les financements des énergies fossiles de cer... des banques. Et du coup, donc là, c'est de la sensibilisation, même si ça va... je ne touche pas... forcément, je peux pas toucher à la politique heu... enfin il me faut l'aval, quand même, de mon président pour heu... pour envoyer la... donc je peux pas mettre des trucs purement politiques, mais on pourrait envisager, voilà, une situation où la pers... où l'organisation communique, « bah ça, ce serait... enfin il ne faut pas continuer à aller dans une démarche... », enfin là, le problème de la politique, c'est que c'est très clivant.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 131, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une démarche", "Et du coup", "du coup", "la politique", "ce serait", "Et du coup", "le problème", ", le problème", "Et du coup", "des trucs", "de la politique", "du coup", "la pédagogie", "le problème", "la pédagogie", "la pédagogie", "dans ma newsletter", "les financements", "énergies fossiles", "des banques", "Et du coup", "la politique heu", "quand même, de mon président", "des trucs purement politiques", "une situation", "une démarche", ", le problème", "la politique", "Et du coup", "Et du coup", "<PERSON> tru<PERSON>", "Et du coup", "une démarche", "si ça", "le problème", "Et du coup", "du coup", "du coup", "des trucs", "la politique", "la politique", "du coup", "du coup", ", c'", "des trucs"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 117, "end": 119}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_119", "text": "Pour le... l'intérêt de la démocratie, qui générale... Il faut que j'aille aux toilettes encore, avec le café et tout. Ah bah trop de thé.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la démocratie", "la démocratie", "le... l'in<PERSON>r<PERSON><PERSON>", "la démocratie", "qui générale", "le café", "Trop de thé"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 118, "end": 121}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_120", "text": "Trop de thé, café. Mais oui, à part... mis à part ça, c'est... c'est compliqué.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 15, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Trop de thé", ", café", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 119, "end": 121}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_121", "text": "Enfin je vois... parce que oui, le renforcement sécuritaire, quel peut être le rôle d'un... d'une entreprise à ce niveau-là ? Tu dis, c'était sur quoi ? D'information ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "le renforcement sécuritaire", "être le rôle", "une entreprise", "ce niveau", "une entreprise", "une entreprise", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 120, "end": 123}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_122", "text": "De formation, d'information sur les... sur les heu... choix, enfin sur la politique, enfin donner une culture d'entre... enfin une culture politique à ses salariés, enfin pour qu'ils... enfin qu'ils aient des connaissances.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ses salariés", "ses salariés", "ses salariés", "ses salariés", "ses salariés", "la politique", "ses salariés", "la politique", "les heu", "... choix", "enfin sur la politique", "une culture", "enfin une culture politique", "ses salariés", "des connaissances", "des connaissances", "ses salariés", "ses salariés", "la politique", "la politique"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 121, "end": 123}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_123", "text": "Parce que généralement, plus les personnes ont des connaissances, moins ils sont attirés par heu... enfin ce qu'on pourrait appeler des votes extrêmes. On va voir si on a eu des pannes.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["généralement, plus les personnes", "des connaissances", "plus les personnes", "des connaissances", "enfin ce", "des votes extrêmes", "des pannes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 122, "end": 124}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_124", "text": "enfin pour les petites entreprises, souvent, ces deux postes sont liés, parce que souvent, ceux qui gèrent et qui posent cette entreprise sont les mêmes, mais quand on est dans des groupes inter... enfin nationaux, internationaux, souvent, c'est : les actionnaires heu... n'exercent pas eux -mêmes la gestion de l'entreprise.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la gestion", "la gestion", "la gestion", "les actionnaires", "enfin pour les petites entreprises", "ces deux postes", "cette entreprise", "des groupes", "les actionnaires heu", "-mêmes la gestion", "les actionnaires", "les actionnaires", "les actionnaires", "les actionnaires", "ces deux postes", "la gestion", "les actio", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 123, "end": 125}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_125", "text": "Il y a les grands... enfin les grands patrons du CAC40, c'est pas eux les actionnaires. D'accord, OK.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", OK", "les actionnaires", "Il y a les grands", "enfin les grands patrons", "accord, OK", "les actionnaires", "accord, OK", "les actionnaires", "les actionnaires", "les actionnaires", ", OK", "les actio", "accord, OK", "accord, OK", ", c'", "c'est pas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 124, "end": 126}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_126", "text": "Par contre, quand on commence à avoir des tailles... des entreprises de taille nationale, internationale, c'est deux personnes différentes et souvent, l'actionnaire vise souvent des objectifs de rentabilité, là où l'administrateur vise plus à remplir des objectifs, enfin il bosse pour les actionnaires. D'accord, OK.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 45, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["des entreprises", ", OK", "des entreprises", "les actionnaires", "accord, OK", "les actionnaires", "accord, OK", "des tailles", "des entreprises", "taille nationale", "des objectifs", "les actionnaires", "les actionnaires", "les actionnaires", ", OK", "souvent, l'actionnaire", "des entreprises", "les actio", "accord, OK", "des entreprises", "accord, OK", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 125, "end": 127}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_127", "text": "En gros, c'est les patrons, ils sont heu... le PDG de... d'une boîte, enfin de <PERSON>, par exemple, <PERSON>... <PERSON><PERSON><PERSON><PERSON>, lui, il est nommé par le conseil d'administration. Et les... le conseil d'administration est élu par les actionnaires.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une boîte", "une boîte", "par exemple", "les actionnaires", "les actionnaires", "les actionnaires", "gros, c'est les patrons", "le PDG", "... d'une boîte", "<PERSON>", "le conseil", "le conseil", "les actionnaires", "le conseil", "le conseil", "les actionnaires", "le PDG", "le conseil", "les actio", "le conseil", "une boîte", "une boîte", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 126, "end": 128}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_128", "text": "Donc c'est les actionnaires qui élu... élisent le conseil d'administration qui eux - mêmes décident de... du gérant. Du coup, en décidant de qui est le gérant, on fixe des objectifs.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le géra<PERSON>", "du coup", "du coup", "les actionnaires", "les actionnaires", "des objectifs", "les actionnaires", "le conseil", "le conseil", "les actionnaires", "Donc c'est les actionnaires", "le conseil", "qui eux", "le conseil", "les actionnaires", "le conseil", "les actio", "le conseil", "du coup", "du coup", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 127, "end": 131}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_129", "text": "Et donc en gros, c'est... ce serait l'équivalent du responsable. Mais celui qui gère dans les faits l'entreprise, c'est le dirigeant. C'est celui qui prend les décisions.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le dirigeant", "c'est ce", "les faits", "ce serait", "le dirigeant", "les décisions", "les faits", "les décisions", "les décisions", "les décisions", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 128, "end": 131}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_130", "text": "Et à l'inverse, s'il prend des mauvaises décisions, à tout moment, il peut se faire remercier par heu... par le conseil d'administration, enfin... par les... par les actionnaires, si justement ils sont pas contents de... des performances, le PDG peut sauter. D'accord, OK.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", OK", "à l'inverse", "les actionnaires", "accord, OK", "les actionnaires", "accord, OK", "les actionnaires", "le PDG", "le conseil", "le conseil", "les actionnaires", "le conseil", "des mauvaises décisions", "tout moment", "le conseil", "les actionnaires", "... des performances", "le PDG", ", OK", "le conseil", "les actio", "le conseil", "accord, OK", "accord, OK"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 129, "end": 131}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_131", "text": "Voi... enfin voilà la différence entre heu... entre les actionnaires et les administrateurs. Donc heu... pour moi, on a vu, y'a « administrateurs, actionnaires », heu... pour les petites organisations, ces deux postes sont... c'est la même personne.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["pour moi", "les actionnaires", "ces deux postes", "les actionnaires", "les actionnaires", "les actionnaires", "les actionnaires", "la différence", ", y'a « administrateurs", ", actionnaires", "les petites organisations", "ces deux postes", "... c'est la même personne", "les actio", ", actionnaires"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 130, "end": 132}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_132", "text": "Enfin une petite boîte, heu... l'administrateur, c'est celui qui gère la... je te laisse lire, c'est celui qui gère la boîte. Donc", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la boîte", "la boîte", "la boîte", "c'est ce", "la boîte", "la boîte", "la boîte", "la boîte", "Enfin une petite boîte", "heu... l'administrateur", "la boîte", "la boîte", "la boîte", "la boîte", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 131, "end": 133}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_133", "text": "toutes les TPE, PME, à chaque fois, c'est la même personne, enfin même mes clients qui ont heu... plusieurs centaines de salariés ne... n'ont... c'est la même personne. Celui qui possède la boîte... apr<PERSON>, des fois, il possède pas 100 % de la boîte, mais souvent, l'actionnaire majoritaire, c'est le gérant. <PERSON><PERSON> <PERSON>tre, quand on arrive sur des groupes nationaux, internationaux, bah c'est plus les mêmes personnes.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 67, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le géra<PERSON>", "la boîte", "la boîte", "la boîte", "la boîte", "chaque fois", "100 %", "la boîte", "la boîte", "la boîte", "des groupes", "la boîte", "toutes les TPE", ", PME", "chaque fois", "enfin même mes clients", "plusieurs centaines", "... c'est la même personne", "la boîte", "des fois", "100 %", "la boîte", "souvent, l'actionnaire", "des groupes nationaux", "bah c'est plus les mêmes personnes", "chaque fois", "la boîte", "100 %", "des fois", "chaque fois", "mes clients", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 132, "end": 136}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_134", "text": "Donc nous, effectivement, on n'a pas pris... fait de distinction à chaque fois quand on disait la Direction, on disait la Direction au sens large, on prenait pas en compte la diffé... la distinction entre les deux. Je sais pas si vous voulez faire une distinction entre les deux ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la direction", "la Direction", "chaque fois", "les deux", "chaque fois", "chaque fois", "la Direction", "la Direction", "sens large", "la distinction", "les deux", "une distinction", "les deux", "les deux", "les deux", "chaque fois", "les deux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 133, "end": 135}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_135", "text": "Partage des valeurs. Parce que c'est les actionnaires qui prennent la... les décisions, justement, act... enfin dans une entreprise, c'est eux qui prennent la décision de : est-ce qu'on verse des dividendes, est-ce qu'on met des rémunérations, quelle est l'enveloppe qu'on met d'augmentation des rémunérations ? Est -ce qu'on fait", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}, "croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "des valeurs", "les décisions", "une entreprise", "une entreprise", "les actionnaires", "les actionnaires", "les actionnaires", "les actionnaires", "les décisions", "les actionnaires", "les décisions", "enfin dans une entreprise", "la décision de", "des dividendes", "des rémunérations", "les actio", "les décisions", "une entreprise", "des valeurs", "une entreprise", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 134, "end": 138}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_136", "text": "... est-ce qu'on va... donne comme objectifs... enfin souvent, c'est l'administrateur qui propose une heu... une stratégie sur la... à l'avenir, aux actionnaires et c'est les actionnaires qui décident ou non. D'un autre côté, si là on... on implique un peu plus les salariés dans la gestion de l'entreprise, au final, ça peut être remonté auprès des actionnaires. Et du coup, heu... bah après, c'est sur la bienveillance des actionnaires, hein. J'imagine que t'as déjà des entreprises qui redistribuent équitablement, mais ce serait peut-être forcer pour... histoire d'avoir heu... d'être sûr que toutes les entreprises le font. C'est avoir un pourcentage obligatoire de répartition financière.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 105, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.9523809523809523, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.38095238095238093}, "noun_phrases": ["les salariés", "la gestion", "les salariés", "La bienveillance", "des entreprises", "les salariés", "les salariés", "les salariés", "les salariés", "un peu plus les salariés", "à l'avenir", "une heu", "Et du coup", "les entreprises", "du coup", "les salariés", "un pourcentage", "ce serait", "Et du coup", "des entreprises", "Et du coup", "la gestion", "la gestion", "du coup", "Et du coup", "une heu", "les actionnaires", "les actionnaires", "Et du coup", "des entreprises", "les actionnaires", "les actionnaires", "les actionnaires", "une heu", "la... à l'avenir", "un autre côté", "un peu plus les salariés", "la gestion", "Et du coup", "la bienveillance", "des entreprises", "... histoire", "toutes les entreprises", "un pourcentage obligatoire", "répartition financière", "les actio", "les salariés", "les salariés", "Et du coup", ", ça", "les entreprises", "Et du coup", "du coup", "du coup", "les entreprises", "des entreprises", "les entreprises", "Les entreprises", "les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "une heu", "une heu", "du coup", "les entreprises", "du coup", ", ça", "les entreprises", ", c'", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 135, "end": 139}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_137", "text": "<PERSON><PERSON> ap<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, par exemple, dans les conseils d'administration, ils ont l'obligation d'avoir des représentants des salariés, mais ce... sur les 18 membres, un conseil d'administration peut avoir jusqu'à 18 membres, sauf situation particulière, mais 18 membres, il y en a que 2, 2 ou 3, qui sont obligatoirement des sala... enfin représentants du personnel. Ça fait pas beaucoup sur... enfin dans les votes, c'est les... 3 sur 18, c'est pas eux qui... enfin ils sont loin de la majorité.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 80, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["des salariés", "des salariés", "par exemple", "les votes", "les conseils", "des représentants", "les 18 membres", "un conseil", "à 18 membres", "situation particulière", "18 membres", "les votes", "des salariés", "les conseils", "un conseil", ", c'", "c'est pas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 136, "end": 138}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_138", "text": "Donc on pourrait envisager qu'il y ait une plus grande participation des... des salariés dans les conseils d'administration. Également, maintenant, ils ont imposé qu'il y ait une égalité hommes-femmes heu... enfin femmes... femmes", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des salariés", "des salariés", "... des salariés", "les conseils", "une plus grande participation", "des salariés", "les conseils", "une égalité hommes-femmes", "enfin femmes", "... femmes", "égalité hommes-femmes", "Égalité hommes-femmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 137, "end": 139}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_139", "text": "Donc heu... déjà... Bah je sais pas, on peut.... on peut plus jouer sur les pourcentages.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les pourcentages"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 138, "end": 140}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_140", "text": "Bah que dans... dans l'entreprise, que ses statuts prévoient qu'il... son conseil d'administration, parce que ça, ça peut être imposé par les statuts, que le conseil d'administration « soit composé de » et là, on peut mettre heu... donc ça, ça peut être imposé par les statuts. Donc effectivement, on peut mettre que la... la société... la société ait dans ses statuts...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 62, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la société", "dans l'entreprise", "la société", "la société", "le conseil", "le conseil", "le conseil", "le conseil", "ses statuts", "son conseil", "parce que ça", "les statuts", "le conseil", "les statuts", "la société", "la société", "ses statuts", "ses statuts", "ses statuts", "ses statuts", "ses statuts", "son conseil", "le conseil", "les statuts", "les statuts", ", ça", "que ça", "parce que ça", ", ça", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 139, "end": 141}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_141", "text": "Comme ça, ça inclut aussi bien les salariés que les clients, les fournisseurs. Ça peut... les parties prenantes. Ce soit pas uniquement... que l'entreprise pas uniquement des décisions elle-même pour... dans ses intérêts, mais qu'elle prenne en compte... Dans ses statuts ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les salariés", "des décisions", "les salariés", "Les fournisseurs", "les salariés", "les salariés", "les salariés", "les salariés", "comme ça", "comme ça", "les salariés", "parties prenantes", "Comme ça", "comme ça", "comme ça", "les parties pre", "ses statuts", "ses statuts", "ses statuts", "Comme ça", "les salariés", "les parties prenantes", "... dans ses intérêts", "ses statuts", "ses statuts", "ses statuts", "les fournisseurs", "les clients", "les fournisseurs", "les salariés", "Comme ça", "les parties prenantes", "les fournisseurs", ", ça", "comme ça", "parties prenantes", ", ça", "les clients", "les clients", "comme ça", "comme ça", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 140, "end": 144}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_142", "text": "Dans ses statuts, que par exemple heu... on peut imposer dans... on peut envisager une société, que dans ses statuts, elle prévoie que son conseil d'administration heu... doit obtenir en cas de changement, enfin que les fournisseurs, ses principaux fournisseurs et ses principaux clients participent au conseil d'administration. Et que ses salariés aussi.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": ["ses salariés", "ses salariés", "Les fournisseurs", "ses salariés", "ses salariés", "ses salariés", "par exemple", "ses salariés", "ses salariés", "ses statuts", "son conseil", "ses statuts", "ses statuts", "ses statuts", "ses statuts", "exemple heu", "une société", "ses statuts", "son conseil", "administration heu", "les fournisseurs", ", ses principaux fournisseurs", "ses principaux clients", "les fournisseurs", "ses salariés", "les fournisseurs", "ses salariés"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 141, "end": 143}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_143", "text": "<PERSON><PERSON>, mais l'objectif, c'est de... L'entreprise, enfin pour moi, un conseil d'administration, c'est pour les grandes entreprises et ça se passe en interne, pas forcément externe, non ? Bah le conseil d'administration, c'est lui qui prend les principales décisions, enfin les décisions de gestion, dans quelle direction veut aller l'entreprise. Ou moi, je mettrais plus en termes de... de financier, d'avoir des pourcentages obligatoires dans la répartition plus égalitaire de... des finances, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 73, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["Et ça", "enfin pour moi", "pour moi", "des entreprises", "Enfin pour moi", "des entreprises", "les décisions", "des entreprises", "le conseil", "le conseil", "le conseil", "les décisions", "le conseil", "les décisions", "des entreprises", "un conseil", "le conseil", "mais l'objectif", "enfin pour moi", "un conseil", ", c'est pour les grandes entreprises", "le conseil", "les principales décisions", "les décisions", "quelle direction", "<PERSON><PERSON> moi", "des pourcentages obligatoires", "la répartition plus égalitaire", "... des finances", "la répartition", "la répartition", "la répartition", "la répartition", "la répartition", "des entreprises", "enfin pour moi", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 142, "end": 145}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_144", "text": "<PERSON><PERSON>, on peut... enfin voil<PERSON>, on peut l'écrire aussi. Que ce soit à la fois chez les salariés que dans les... les ressources, tout ça, quoi. Non ? Oui. Ah oui, en gros, ça fait partie du coup de la... pour la répartition, parce que typiquement, si on dit : bah on distribue... Non, mais j'essaie de suivre, moi, votre conversation.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les salariés", "les salariés", "la fois", "tout ça", "les salariés", "les salariés", "les salariés", "les salariés", "tout ça", "tout ça", "du coup", "les salariés", "tout ça", "la fois", ", tout ça", "tout ça", "tout ça", "la fois", "du coup", "les salariés", "la fois", "que dans les... les ressources", ", tout ça", "la répartition", "les salariés", "la répartition", "la répartition", "la répartition", "la répartition", ", ça", "tout ça", "tout ça", "tout ça", "du coup", "du coup", "tout ça", "du coup", "du coup", ", ça", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 143, "end": 148}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_145", "text": "C'est que la répartition donc des ressources, donc notamment vis-à-vis des salariés pour que ce soit équitable entre les salariés, mais également heu... ce qu'on réinvestit dans l'entreprise pour qu'elle puisse perdurer.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les salariés", "les salariés", "des salariés", "les salariés", "les salariés", "les salariés", "les salariés", "des salariés", "les salariés", "dans l'entreprise", "des salariés", "les salariés", "la répartition", "des ressources", "les salariés", "la répartition", "la répartition", "la répartition", "la répartition"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 144, "end": 146}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_146", "text": "Dans la réparti... dans une, tu peux mettre dans une répartition plus équitable et égalitaire des... des bénéfices. Est -ce que vous voulez mettre... ? Ça te va, toi ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", toi", "une répartition", "des bénéfices", "une répartition plus équitable et égalitaire", "des bénéfices"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 145, "end": 149}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_147", "text": "<PERSON>, oui, c'est bon. <PERSON><PERSON>, j'ai mis tous ceux qu'on a parlés plus ou moins de loin ou de près, parce que du coup, ils nous ont demandé de sortir tous ceux : employés, environnement, tous ceux, pour moi, où on en a parlé de manière directe ou indirecte.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["pour moi", "du coup", "du coup", "<PERSON><PERSON> moi", "tous ceux", "parce que du coup", "tous ceux", ": employés", ", environnement", ", tous ceux", "manière directe ou indirecte", "parce que du coup", "parce que du coup", "du coup", "du coup", "du coup", "du coup", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 146, "end": 148}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_148", "text": "On n'a pas parlé... donc ça, on en a parlé indirectement. Vite fait, oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 147, "end": 149}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_149", "text": "<PERSON><PERSON> a<PERSON><PERSON><PERSON>, ce dont pour moi on n'a pas parlé, heu... créanciers, défense des droits des consommateurs, les médias, les... l'innovateur, on n'a pas trop par... enfin... Un petit peu, quand même. Un petit peu avec la R&D, oui, je l'avais oublié. Socialement responsable, celui-là, je l'ai raté. Hop, socialement responsable.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 51, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pour moi", "ce dont", "les médias", ", les... l'innovateur", "la R&D", "les médias", ", celui"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 148, "end": 152}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_150", "text": "Et puis voilà. Et là, du coup, pour moi, c'est ceux-là dont on n'a pas parlé.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pour moi", "c'est ce", "du coup", ", du coup", ", du coup", "du coup", ", du coup", ", c'est ceux", "<PERSON>t là, du coup", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", c'", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 149, "end": 151}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_151", "text": "Est -ce que juste avant de partir pour le petit pipi, le petit café, vous pouvez mettre à côté de votre feuille les cartes que vous avez utilisées ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["votre feuille", "les cartes", "le petit pipi", ", le petit café", "les cartes", "les cartes", "les cartes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 150, "end": 152}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_152", "text": "Je marque quoi, « milieu associatif » ? Milieu associatif. <PERSON>h après, c'est pas pareil que... ? Bah les ONG, c'est... c'est uniquement les organisations non gouvernementales, alors que... c'est là, les ONG. C'était là, les ONG.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": [", « milieu associatif", "Milieu associatif", ", c'est... c'est uniquement les organisations non gouvernementales", "C'était là, les ONG.", "les organisations", "les ONG", ", c'", "c'est pas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 151, "end": 156}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_153", "text": "Les associations, le milieu associatif, c'est le... le fait de travailler davantage avec des... dans le milieu associatif, libérer du temps de travail pour ses salariés, pour qu'ils puissent s'impliquer par exemple dans le... dans le milieu associatif, la flexibilité ou même proposer heu... je sais que c'est à... dans les cabinets comptables, enfin dans... au sein du réseau heu... dans lequel on appartient, y'en a certains qui libèrent du temps à ses... à leurs collaborateurs, ils leur libèrent cinq journées pour ceux qui le veulent par an s'ils veulent travailler pour une association. Et eux, ils ont juste après à demander à l'association de faire une attestation comme quoi la personne a bossé toute la journée, mais du coup, ils libèrent pour tous leurs salariés, ils ont cinq jours par an s'ils veulent bosser dans une association. D'accord. D'accord, oui, pourquoi pas. Par contre, du coup, peut-être juste préciser, sur la répartition, ça, c'est ceux où on en a parlé, ça, c'est ceux qu'on a vaguement évoqués et ça, c'est ceux dont on n'a pas parlé.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 177, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Et ça", "la flexibilité", "ses salariés", "ses salariés", "la flexibilité", "le fait", "ses salariés", "le fait", "ses salariés", "c'est ce", "ses salariés", "le fait", "leurs salariés", "par exemple", "du coup", ", du coup", "la personne", "la personne", "la personne", "la personne", "<PERSON><PERSON> du <PERSON>", "ses salariés", ", du coup", "du coup", "<PERSON><PERSON> du <PERSON>", "ses salariés", ", du coup", ", c'est ceux", "<PERSON><PERSON> du <PERSON>", "la répartition", "le fait", "la répartition", "la répartition", "la répartition", "le milieu associatif", "Milieu associatif", "Les associations", "le milieu associatif", "le milieu associatif", "ses salariés", "le milieu associatif", "la flexibilité", "les cabinets comptables", "<PERSON><PERSON><PERSON> heu", "leurs collaborateurs", "cinq journées", "une association", "Et eux", "une attestation", "la personne", "toute la journée", "tous leurs salariés", "cinq jours", "une association", "contre, du coup", "la répartition", ", ça", ", ça, c'est ceux", ", du coup", "Le fait", "ses salariés", ", du coup", "<PERSON><PERSON> du <PERSON>", "... le fait", ", du coup", "du coup", "du coup", ", du coup", "<PERSON><PERSON> du <PERSON>", "du coup", "du coup", ", ça", "les cabinets comptables", ", c'", "le fait", ", du coup", "le milieu associatif", "une association"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 152, "end": 157}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_154", "text": "Enfin je sais pas si ça vous va, c'est moi... j'ai placé les cartes.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les cartes", "les cartes", "les cartes", "si ça", "les cartes", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 153, "end": 155}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_155", "text": "Une ligne sur la même ou ? Oui, sur la même.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Une ligne"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 154, "end": 156}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_156", "text": "O<PERSON>, OK. T'as un peu de place, non ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", OK", ", OK"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 155, "end": 157}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_157", "text": "<PERSON><PERSON>, raj<PERSON><PERSON> peut-être une ligne heu... bah transparence, communication à la fois interne et externe. Communication, voilà, avec les parties prenantes et également heu... enfin ne... parce que tu vois typiquement, ça aussi, les petites sociétés n'ont pas l'obligation légale de communiquer leurs résu... leur bilan.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 46, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la fois", "parties prenantes", "la fois", "la fois", "les parties pre", "les parties prenantes", "la fois", "Une ligne", "une ligne heu", "la fois interne et externe", "les parties prenantes", "les petites sociétés", "l'obligation légale", "leur bilan", ", ça", "parties prenantes", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 156, "end": 158}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_158", "text": ", c'est tous ceux qui ont des... Est-ce que c'est... vous êtes bons ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["tous ceux", "tous ceux", ", c'est tous ceux", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 157, "end": 159}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_159", "text": "On va du coup mettre les cartes de côté, heu... mettre cette partie de la légitimité de côté et maintenant, tra... travailler... donc de la même façon, en 2050, avec le scénario business as usual qu'a présenté Alexis et les effets pressentis sur les organisations qui sont notés sur vos feuilles et ce que vous imaginez aussi bien évidemment, quels sont donc les critères de performance d'une bonne boîte en 2050, dans ces conditions ?", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 75, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les critères", "la légitimité", "la légitimité", "les effets", "La légi", "la légitimité", "du coup", "la légitimité", "la légitimité", "du coup", "la légitimité", "les cartes", "les cartes", "les cartes", "cette partie", "la légitimité", "la même façon", "le scénario business", "les organisations", "vos feuilles", "les critères", "une bonne boîte", "ces conditions", "les critères", "la même façon", "les critères", "de performance", "du coup", "du coup", "les cartes", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 158, "end": 160}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_160", "text": "<PERSON><PERSON>, oui. Est -ce que, quand tu parles de critères de performance, est-ce qu'on peut avoir un petit heu... comme... Ah oui, comme heu... oui, le critère de performance, c'est : qu'est-ce que heu... là, on est plutôt en interne, si je peux le dire comme ça, de l'entreprise, c'est-à-dire comment l'entreprise va considérer qu'elle est performante sur son marché, vis-à-vis de l'environnement.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 1.0}, "noun_phrases": ["son march<PERSON>", "comme ça", "comme ça", "Comme ça", "comme ça", "comme ça", "Comme ça", "Comme ça", "un petit heu", "comme ça", "son march<PERSON>", "de performance", ", c'", "comme ça", "comme ça", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 159, "end": 162}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_161", "text": "En fait, c'est aussi un peu lié aux parties prenantes, d'ailleurs, vos collègues l'ont fait de façon un peu plus systémique. Critères de réussite, en gros.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["parties prenantes", "en fait", "parties prenantes", "vos collègues", "façon un peu plus systémique", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 160, "end": 162}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_162", "text": "C'est les critères de réussite, comment on considère qu'elle est donc performante, c'est qu'elle est heu... utile, qu'elle ait r<PERSON><PERSON><PERSON>, qu'elle marche, en fait, qu'elle peut et qu'elle est, je vais ajouter ce mot-là, p<PERSON><PERSON><PERSON>, en fait. Et du coup, est-ce que c'est des critères heu... c'est des critères mesurables forcément ou pas forcément ?", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 0.6}, "noun_phrases": ["les critères", "Et du coup", "du coup", "en fait", "Et du coup", "Et du coup", "du coup", "Et du coup", "Et du coup", "Et du coup", "Et du coup", "les critères", "les critères", "ce mot-là", "les critères", "critères heu", "Et du coup", "du coup", "du coup", "c'est qu'", "du coup", "du coup", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 161, "end": 163}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_163", "text": "Non, non, ah là, je réfléchissais par rapport à, du coup, ce qu'on avait déjà noté, enfin ce que j'avais noté ou même ce que vous aviez noté, qu'est-ce qui changerait, parce que du coup, c'est la même question, pour... en 2050 ? Je réfléchis, parce que... oui, les cri...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", ", du coup", ", du coup", "du coup", "enfin ce", ", du coup", "parce que du coup", ", du coup", "même ce", "parce que du coup", ", du coup", "parce que du coup", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", c'", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 162, "end": 164}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_164", "text": "Le fait de rechercher à s'améliorer et améliorer le bien-être de ses salariés sera toujours heu... enfin d'être dans une démarche de recherche de... d'amélioration continue et porter des valeurs et impliquer ses parties prenantes, ça le sera tout aussi. OK, donc ça, on peut le renoter, déjà. Bah c'est un peu l'impression que j'ai. Oui.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une démarche", "le bien-être", "ses salariés", "le bien-être", "ses salariés", "le bien-être", "le fait", "ses salariés", "le fait", "des valeurs", "ses parties prenantes", "ses salariés", "ses salariés", "le fait", "parties prenantes", "ses salariés", "une démarche", "ses salariés", "le fait", "ses salariés", ", ça", "parties prenantes", "le bien-être", "Le fait", "le bien-être", "ses salariés", "une démarche", "... d'amélioration", "des valeurs", "ses parties prenantes", ", ça", "le fait"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 163, "end": 167}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_165", "text": "Pour moi, c'est... enfin j'ai l'impression que... enfin malheureusement, ce sera toujours heu... Égalité hommes-femmes. Bien-être au travail, vous disiez ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pour moi", "égalité hommes-femmes", ", c'", "Égalité hommes-femmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 164, "end": 166}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_166", "text": "Donc je le mets dans le même truc, alors ? Oui. Bien-être au travail, faible ... … taux de turn-over.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le même truc", "... … taux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 165, "end": 169}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_167", "text": "<PERSON><PERSON>, et puis je pense, le... le temps de travail, quoi. Oui. Qui va dedans aussi, du coup ? Oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", ", du coup", "le temps", ", du coup", "du coup", ", du coup", "<PERSON>, du coup", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "le temps", "du coup", "du coup", ", du coup", "le temps"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 166, "end": 170}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_168", "text": "Temps de travail peut-être diminué. Temps de travail, vie pro/vie perso et même heu... sens", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 15, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", vie", "... sens", "... sens"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 167, "end": 169}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_169", "text": "OK, donc le sens donné à son travail. Heu... transparen... transparence et partage de la valeur.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["la valeur", "la valeur", "la valeur", "la valeur", "la valeur", "son travail", "le sens", "son travail", "Heu... transparen", "... transparence", "la valeur", "le sens"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 168, "end": 170}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_170", "text": "<PERSON><PERSON>, et puis forcément avoir des actions environnementales, hein, je pense que maintenant, toute entreprise va être obligée de le faire. J'ai pas entendu, pardon, le début de ta phrase ?", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["toute entreprise", "des actions environnementales", "toute entreprise", ", le début", "ta phrase", "Des actions environnementales"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 169, "end": 171}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_171", "text": "Des actions environnementales. Après... Des actions concrètes environnementales.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 8, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des actions environnementales", "Des actions environnementales", "... Des <PERSON> concrètes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 170, "end": 172}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_172", "text": "Heu... oui, on peut.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 4, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 171, "end": 173}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_173", "text": "<PERSON><PERSON>, c'est particularités techniques aussi, considération éthique et points d'attention particuliers. Canicule.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est particularités techniques aussi", ", considération", "attention particuliers", "Particularités techniques", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 172, "end": 174}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_174", "text": "Ah si, « considérations éthiques », oui, je l'ai dit, enfin je l'ai lu. Et du coup, je ré<PERSON><PERSON>chissa<PERSON>, parce que en... l'implication, enfin comment mesurer l'éthique, enfin faire un critère de performance en éthique, parce que pour... ça va être très important.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 1.0, "performance_indicators": 2, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Et du coup", "du coup", "Et du coup", "Et du coup", "du coup", "Et du coup", "Et du coup", "Et du coup", "Et du coup", "je l'ai lu", "un critère", "de performance", "Et du coup", "du coup", "du coup", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 173, "end": 175}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_175", "text": "Donc ça rentre aussi dans le pouvoir d'achat des consommateurs, hein, plus le niveau des salaires est... Est élevé, plus les... <PERSON><PERSON>, ou du moins, heu... pris en compte de... de l'augmentation des matières premières et tout ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["tout ça", "Le niveau", "tout ça", "tout ça", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "matières premières", "tout ça", "tout ça", "tout ça", "tout ça", "tout ça", "le pouvoir", "plus le niveau", "... de l'augmentation", "matières premières", "tout ça", "Matières premières", "tout ça", "tout ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 174, "end": 176}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_176", "text": "Donc on peut mettre heu... niveau des salaires. Oui, voilà, y'avait l'insécurité alimentaire aussi qui... ils l'avaient mis, donc la prise en compte heu...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la prise", "la prise", "la prise", "la prise", "la prise", "... niveau", "l'insécurité alimentaire", "la prise"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 175, "end": 177}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_177", "text": "<PERSON><PERSON>, c'est ça, de... enfin c'est lié à la tune, hein, après. Niveau des salaires.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 15, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "la tune", "C'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 176, "end": 178}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_178", "text": "Mais du coup, voil<PERSON>, si elle performante économiquement, donc est-ce que... est-ce que ça fait partie des critères heu... enfin la performance économique, justement, si la boîte est profitable, enfin fait des bénéfices, elle pourra les partager. Alors qu'à l'inverse, si elle fait des déficits, elle ne pourra pas augmen... enfin... Oui, aussi. Est", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 2, "tension_strength": 1, "total_indicators": 3}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["le fait", "la boîte", "la boîte", "le fait", "la boîte", "la boîte", "le fait", "à l'inverse", "du coup", "la boîte", "<PERSON><PERSON> du <PERSON>", "la boîte", "la boîte", "du coup", "<PERSON><PERSON> du <PERSON>", "la boîte", "la boîte", "la boîte", "<PERSON><PERSON> du <PERSON>", "le fait", "des bénéfices", "des bénéfices", "Le fait", "que ça", "Performance économique", "<PERSON><PERSON> du <PERSON>", "critères heu", "enfin la performance économique", "la boîte", "des déficits", "des déficits", "du coup", "du coup", "<PERSON><PERSON> du <PERSON>", "du coup", "du coup", "le fait", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 177, "end": 181}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_179", "text": "-ce que... du coup, c'est pas aussi malheureusement lié à... non pas forcément à un objectif de croissance, mais de performance, enfin... Bah après... oui, parce que du coup, dans la réflexion, je viens à penser, tu vois, les... les entreprises perf... enfin en pleine croissance, elles ne sont pas forcément bénéfiques. <PERSON><PERSON>, elles font des déficits. <PERSON><PERSON>, elles font des déficits économiques, mais heu... <PERSON>h souvent, c'est qu'y'a... Mais environnemental, elles l'ont pas. De quoi ? Elles n'ont pas forcément de bénéfice environnemental. Et souvent, c'est que si elles sont en croissance, du coup, leur impact environnemental s'aggrave, se détériore.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 101, "thematic_indicators": {"performance_density": 1.9801980198019802, "legitimacy_density": 0.9900990099009901, "performance_indicators": 2, "legitimacy_indicators": 1}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["leur impact", "les entreprises", "du coup", ", du coup", ", du coup", "un objectif", "du coup", ", du coup", "parce que du coup", ", du coup", "parce que du coup", ", du coup", "des déficits", "... du coup", "... non pas forcément à un objectif", "de performance", "parce que du coup", "la réflexion", "les... les entreprises", "enfin en pleine croissance", "des déficits", "des déficits économiques", "pas forcément de bénéfice environnemental", ", leur impact", "les entreprises", "de bénéfice", ", du coup", "du coup", "du coup", "les entreprises", "c'est qu'", "les entreprises", "Les entreprises", "les entreprises", ", du coup", "... Les ent", "du coup", "les entreprises", "du coup", "les entreprises", ", c'", "c'est pas", ", du coup", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 178, "end": 185}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_180", "text": "Donc souvent... enfin... Donc peut-être avoir... oui, un ratio, heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["oui, un ratio"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 179, "end": 182}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_181", "text": "Bah je sais pas comment... enfin comment intégrer, parce que indirectement, la partie économique est forcément liée aux critères de performance, mais comment avoir heu... si, avoir une performance éthique, c'est-à-dire avoir une performance, mais ne pas tirer tous les coûts vers le bas, typiquement, de ses fournisseurs et ne pas chercher à... à améliorer sa marge forcément, avoir heu... oui, une performance, je sais pas, une performance éthique, si ça... si ça se dit, enfin en gros, chercher une performance, mais pas au détriment des autres parties prenantes et de... Performance économique éthique, en gros.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 96, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la partie", "la partie", "la partie", "les coûts", "parties prenantes", "la partie", "parties prenantes", "Performance économique", "de performance", "la partie économique", "une performance éthique", "une performance", "tous les coûts", "le bas", "typiquement, de ses fournisseurs", "sa marge", "une performance éthique", "si ça", "une performance", "autres parties prenantes et de", "sa marge", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 180, "end": 182}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_182", "text": "Non, non, mais c'est vrai, faut... faut qu'on aie aussi... savoir comment on peut l'expliquer derrière. Parce que pour moi, ce serait lié à... le fait qu'une entreprise se développe économiquement, qu'elle répartisse après, que ce soit par ses heu... produits ou services, de manière éthique. <PERSON> vois, typiquement, une boîte dans la fast fashion, elle augmente économiquement, non, mais c'est vrai, mais pourtant, sa marge est toujours élevée et derrière, l'humain est maltraité, ni l'environnement.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 76, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "pour moi", "le fait", "le fait", "une boîte", "le fait", "une boîte", "ce serait", "une entreprise", "une entreprise", "le fait", "une boîte", "une entreprise", "Le fait", "sa marge", "... le fait", "une entreprise", "ses heu", "manière éthique", "typiquement, une boîte", "la fast fashion", "sa marge", "une boîte", "le fait"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 181, "end": 184}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_183", "text": "Donc en fait, pour vous, enfin le bénéfice économique, c'est pas forcément lié avec l'éthique. C'est ça. Tu peux faire un bénéfice économique et pourtant, au niveau de l'éthique, t'es pas... Être catastrophique.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["donc en fait", "C'est ça", "C'est ça", "en fait", "enfin le bénéfice économique", "C'est ça", "un bénéfice économique", "un bénéf", "un bénéfice", "un bénéfice économique", ", c'", "c'est pas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 182, "end": 186}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_184", "text": "Et du coup, faudrait lier les deux, c'est-à-dire que quand tu fais un bénéf... enfin il faut pousser les entreprises à faire des bénéfices économiques éthiques. Qu'en fait les deux soient liés, en fait. Oui, c'est ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["C'est ça", "C'est ça", "Et du coup", "les entreprises", "<PERSON><PERSON>, c'est ça", "les deux", "du coup", "<PERSON><PERSON>, c'est ça", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", "en fait", "Et du coup", "<PERSON><PERSON>, c'est ça", "Et du coup", "du coup", "Et du coup", "Et du coup", "les deux", "les deux", "Et du coup", "<PERSON><PERSON>, c'est ça", "des bénéfices", "des bénéfices", "Et du coup", "<PERSON><PERSON>, c'est ça", "C'est ça", "les deux", "un bénéf", "les entreprises", "des bénéfices économiques", "les deux", "<PERSON><PERSON>, c'est ça", "des bénéfices économiques", "Et du coup", "du coup", "du coup", "les entreprises", "<PERSON><PERSON>, c'est ça", "les entreprises", "Les entreprises", "les entreprises", "<PERSON><PERSON>, c'est ça", "du coup", "les entreprises", "du coup", "les entreprises", ", c'", "les entreprises", "les deux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 183, "end": 186}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_185", "text": "Voilà, que pour faire un bénéfice, pour faire un bénéfice économique, il faille heu... avoir des actions éthiques, enfin une action poli... <PERSON><PERSON>, que ce soit lié, que quand tu fais des bénéfices économiques, ce soit en rapport avec l'éthique.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["des bénéfices", "des bénéfices", "un bénéfice économique", "un bénéf", "des bénéfices économiques", "un bénéfice", "un bénéfice économique", "des actions éthiques", "enfin une action poli", "des bénéfices économiques"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 184, "end": 186}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_186", "text": "<PERSON><PERSON>, donc on peut tout simplement noter qu'il faut lier les bénéfices heu... éthiques et économiques. Non, les bénéfices économiques à l'éthique, parce que les bénéfices... <PERSON><PERSON>, plut<PERSON><PERSON>, oui. Ça va dans le sens... <PERSON><PERSON>.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le sens", "les bénéfices heu", "Non, les bénéfices économiques", "parce que les bénéfices", "le sens", "les bénéfices économiques", "les bénéfices économiques", "les bénéfices économiques", "les bénéfices"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 185, "end": 190}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_187", "text": "<PERSON><PERSON>, parce que l'éth... enfin l'éthique, c'est pas... les bénéfices éthiques, ça existe pas, c'est pour ça que j'étais gêné en disant le mot. Ah ça... ? Non. Pourquoi ça existe pas ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le mot", ", ça", "enfin l'éthique", "les bénéfices éthiques", "le mot", "les bénéfices", "c'est pas...", "le mot", ", ça", ", c'", "c'est pas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 186, "end": 191}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_188", "text": "Bah le... enfin le terme, c'est que... Tu peux pas faire de bénéfice dans le... Don<PERSON> lier les bénéfices économiques à l'éthique, pas au bénéfice éthique, à l'éthique, du coup, de manière générale. Oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", ", du coup", ", du coup", "du coup", ", du coup", ", du coup", ", du coup", "de bénéfice", "les bénéfices économiques", "bénéfice éthique", ", à l'éthique", ", du coup", "manière générale", "les bénéfices économiques", "du coup", "du coup", "les bénéfices économiques", ", du coup", "les bénéfices", "du coup", "du coup", ", c'", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 187, "end": 189}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_189", "text": "Enfin un taux... un impact. Mai<PERSON> le problème, c'est que pour certaines... voilà, pour certaines boîtes, ça peut... enfin... Ça peut être difficile ? Ça peut être difficile en fonction de leur secteur d'activités.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un taux", "le problème", "le problème", ", ça", "un taux", "un taux", "le problème", "<PERSON><PERSON> bo<PERSON>", "leur secteur", "un impact", "un impact", ", ça", ", c'", "un taux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 188, "end": 191}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_190", "text": "C'est que typiquement, une boîte dans le tertiaire, on aurait heu... juste des ordinateurs, et l'él... l'électricité et le chauffage, c'est plus facile qu'une boîte qui est dans l'industrie, qui a... enfin dans la métallurgie ou dans la fonderie, qui a besoin d'une énorme quantité d'énergie, c'est très compliqué, à elle, de lui demander de compenser 100 %, enfin... <PERSON><PERSON>, mais apr<PERSON>, tu peux pallier du coup sur autre chose, non ? Oui, tu peux faire heu... tu vois, les bénéfices économiques peuvent être répartis sur heu... des associations.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 89, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une boîte", "des ordinateurs", "une boîte", "100 %", "du coup", "le chauffage", "du coup", "100 %", "des associations", "une boîte", "typiquement, une boîte", "les bénéfices économiques", "une boîte", "le tertiaire", "juste des ordinateurs", "le chauffage", "qu'une boîte", "enfin dans la métallurgie", "dans la fonderie", "une énorme quantité", "100 %", "autre chose", "les bénéfices économiques", "la fonderie", "du coup", "du coup", "les bénéfices économiques", "les bénéfices", "autre chose", "du coup", "du coup", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 189, "end": 192}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_191", "text": "Typiquement, les Hauts Fourneaux, pour faire la fonderie, ça consomme tellement d'énergie que malheu... enfin d<PERSON>, ils essaient de réduire les émissions de CO2 , ils ne... enfin ils ne peuvent pas annuler, mettre à zéro.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 36, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", ça", "Typiquement, les Hauts", "la fonderie", "que malheu", "les émissions", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 190, "end": 192}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_192", "text": "Ils peuvent... enfin d'où la qualification « minimiser », pour ce qui peuvent pas mettre à zéro et ceux qui peuvent être en positif, bah le faire. O<PERSON>, parce que « minimiser », c'est pas forcément être... enfin c'est juste tu essaies de faire moins, mais après, tu... Tu fais le moins que tu peux.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["où la qualification", ", c'", "c'est pas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 191, "end": 193}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_193", "text": "<PERSON><PERSON>, donc c'est un terme qui est quand même... ça englobe... C'est pour ça que je disais « minimiser », apr<PERSON>, je sais pas si vous... ? <PERSON><PERSON>, on peut mettre « minimiser », oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, donc c'est un terme"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 192, "end": 194}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_194", "text": "Donc minimiser l'impact environnemental ? Voilà.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 6, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["l'impact environnemental"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 193, "end": 195}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_195", "text": "Et optimiser la... le renouvellement de la biodiversité, enfin en gros, minimiser pour heu... l'impact et favoriser... favoriser le... enfin le renouveau, je sais pas comment le dire. En gros, d'une part, réduire leur...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la biodiversité", "la... le renouvellement", "la biodiversité", "... l'impact", "le renouveau", "une part", "la biodiversité", "le renouvellement"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 194, "end": 196}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_196", "text": "... matériaux rares, de certains... des matériaux rares, là.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["... mat<PERSON><PERSON><PERSON> rares", "des matériaux rares"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 195, "end": 197}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_197", "text": "« Il faut plutôt louer ».", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 6, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 196, "end": 198}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_198", "text": "Bah y'a aussi ce truc de heu... tu vois, l'écologie culpabilisante plus que... parce qu'au final, heu... enfin les consommateurs, mine de rien, c'est pas non plus les plus gros pollueurs. Ça fait partie de la chaîne, mais heu... bon... Mais du coup, c'est pour... <PERSON><PERSON>, sensibiliser aussi le truc, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 51, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", "<PERSON><PERSON> du <PERSON>", "les consommateurs", "du coup", "<PERSON><PERSON> du <PERSON>", "<PERSON><PERSON> du <PERSON>", "<PERSON><PERSON> du <PERSON>", "ce truc", "l'écologie culpabilisante", "la chaîne", "du coup", "le truc", "du coup", "le truc", "<PERSON><PERSON> du <PERSON>", "du coup", "du coup", ", c'", "c'est pas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 197, "end": 200}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_199", "text": "Voilà, c'est sensibiliser à tous les niveaux, donc heu... Bah dans ce cas-là... Mais du coup, le truc recyclage et heu... et le partage...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["ce cas", "du coup", "ce cas", "<PERSON><PERSON> du <PERSON>", "du coup", "<PERSON><PERSON> du <PERSON>", "ce cas", "ce cas", "Le partage", "<PERSON><PERSON> du <PERSON>", "Le partage", "<PERSON><PERSON> du <PERSON>", "du coup", "le truc", "tous les niveaux", "ce cas", "du coup", ", le truc", "le partage", "ce cas", "le truc", "<PERSON><PERSON> du <PERSON>", "du coup", "du coup", ", c'", "ce cas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 198, "end": 200}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_200", "text": "<PERSON><PERSON>, le recyclage et aussi le... les circuits courts. Oui, circuits...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Le recyclage", "Le recyclage", "<PERSON><PERSON>, le recyclage", "aussi le... les circuits", ", circuits", "circuits courts", "le recyclage"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 199, "end": 201}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_201", "text": "Bah tu peux... tu peux parler de pourcentage, dans ce cas-là. <PERSON><PERSON>, parce que là, c'est les critères, les indicateurs, c'est pas la politique, c'est comment on le mesure.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "ce cas", "la politique", "ce cas", "ce cas", "ce cas", "la politique", "les critères", "les critères", "les critères", "les indicateurs", "les indicateurs", "ce cas", "ce cas", "la politique", "la politique", ", c'", "c'est pas", "ce cas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 200, "end": 202}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_202", "text": "<PERSON><PERSON>, c'est clair. <PERSON><PERSON>, si y'a des trucs où on n'a pas de... enfin en soi, c'est pas...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des trucs", "<PERSON> tru<PERSON>", "des trucs", "c'est pas...", ", c'", "c'est pas", "des trucs"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 201, "end": 203}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_203", "text": "Mais enfin... mais chaque heu... enfin l'Ordre des experts-comptables fait du lobbyisme auprès du ministère de l'Économie pour que ça aille dans... dans leur sens. Les instances professionnelles essaient à chaque fois de tirer la couverture vers eux en faisant de la pression, en démontrant... enfin même l'a... l'agriculture, avec le renouvellement des... du glyphosate pour dix ans, c'est qu'ils ont fait du lobbyisme pendant... enfin acharné, qu'ils ont... alors ils ont malheureusement gagné, eu gain de cause. Malheureusement, le lobbyisme, et même les ONG aussi font du lobbyisme auprès des politiques, justement, là, les ONG ont fait du lobbyisme auprès de l'Europe pour que l'écoci... le crime d'écocide soit reconnu. Maintenant que ça a été reconnu, il va pouvoir y avoir des condamnations.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 124, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.6451612903225807}, "noun_phrases": ["chaque fois", "chaque fois", "chaque fois", "la co", "que ça", "chaque heu", "leur sens", "Les instances professionnelles", "chaque fois", "la couverture", "la pression", "enfin même l'a... l'agriculture", "le renouvellement", "dix ans", "c'est qu'", "Malheureusement, le lobbyisme", "et même les ONG", "du lobbyisme", "les ONG", "que l'écoci", "le crime", "des condamnations", "même les ONG", "la pression", ", c'", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 202, "end": 206}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_204", "text": "<PERSON><PERSON>, c'est... même les ONG font de... enfin du lobbyisme. D'accord. Oui, c'est ça. C'est... en gros, le lobbyisme, c'est le fait d'a... de vouloir influencer sur les décisions politiques.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le fait", "C'est ça", "le fait", "C'est ça", "le fait", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "oui, c'est ça", "<PERSON><PERSON>, c'est ça", "<PERSON><PERSON>, c'est ça", "les décisions", "les décisions", "les décisions", "les décisions", "<PERSON><PERSON>, c'est ça", "le fait", "Le fait", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "du lobbyisme", "les ONG", "<PERSON><PERSON>a", "même les ONG", "... enfin du lobbyisme", "<PERSON><PERSON>, c'est ça", "gros, le lobbyisme", "les décisions politiques", "<PERSON><PERSON>, c'est ça", ", c'", "le fait", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 203, "end": 207}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_205", "text": "<PERSON><PERSON>, donc a<PERSON><PERSON><PERSON>, ça... enfin pour moi, je trouve ça compliqué de lier les entreprises au gouvernement, quoi. Je vous propose qu'on regarde les cartes, voir si on n'a pas loupé ? Alors heu... « coûts opérationnels, indicateurs de rentabilité de coûts de production, de coûts de main d'œuvre et de coûts de distribution ».", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["enfin pour moi", "pour moi", "Enfin pour moi", "les entreprises", "a<PERSON><PERSON>ès, ça", "enfin pour moi", "les cartes", "les cartes", ", ça", "les cartes", "les entreprises", "les entreprises", "enfin pour moi", "les entreprises", "les cartes", "Alors heu... « coûts opérationnels", ", indicateurs", "de <PERSON>", "Les entreprises", "les entreprises", ", indicateur", "les entreprises", ", ça", "les entreprises", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 204, "end": 207}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_206", "text": "Ça va avec le... lier les bénéfices économiques à l'éthique, enfin si j'ai bien compris ? Oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les bénéfices économiques", "les bénéfices économiques", "les bénéfices économiques", "les bénéfices"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 205, "end": 207}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_207", "text": "Non, je ne suis pas du tout d'accord avec ce point de vue. La croissance heu... parce que ça, c'est en gros, viser la croissance. Croissance des ventes ou des revenus. Croissance de revenus.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["la croissance", "parce que ça", "que ça", "de revenus", "ce point", "La croissance heu", "parce que ça", "la croissance", "des revenus", "la croissance", "la croissance", ", c'", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 206, "end": 210}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_208", "text": "Bah enfin du coup, on en a parlé quand... Qui peut être mesuré en termes de pourcentage d'augmentation d'une année à l'autre. O<PERSON>, mais ce serait le... la redistribution, oui, c'est un peu lié à l'éthique, quoi. On a parlé des salaires ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["du coup", "ce serait", "du coup", "du coup", "du coup", "une année", "enfin du coup", "une année", "le... la redistribution", "du coup", "du coup", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 207, "end": 210}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_209", "text": "C'est viser la croissance, enfin une croissance infinie dans un monde fini. Ça commence à un moment ou à un autre. À un moment, tu dois pas te stabiliser pour te développer ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["la croissance", "un moment", "la croissance", "la croissance", "enfin une croissance infinie", "un monde", "un moment", "un moment", "la croissance", "un moment", "une croissance", "un moment", "une croissance", "un moment"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 208, "end": 211}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_210", "text": "Les entreprises doivent vi... enfin il faut que le mot « décroissance » ne soit plus un gros mot et que les entreprises ne cherchent pas toujours à viser la croissance, mais peut-être à mieux produire, plutôt que produire plus. Mais c'est pas un peu ce qu'on a dit tout à l'heure, là, dans un des trucs ? Si, on en a parlé à un moment. On en a parlé, de ça, non ? Oui, mais c'est lié aussi à l'é... lier développement économique et... et éthique, du coup.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 89, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 1.0, "performance_indicators": 2, "legitimacy_indicators": 1}, "tension_patterns": {"croissance_decroissance": {"side_a": 2, "side_b": 1, "tension_strength": 1, "total_indicators": 3}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["le mot", "la croissance", "les entreprises", "du coup", ", du coup", ", du coup", "des trucs", "du coup", ", du coup", "<PERSON> tru<PERSON>", ", du coup", ", du coup", "les entreprises", "le mot", ", du coup", "du coup", "du coup", "des trucs", "les entreprises", "les entreprises", "un moment", "la croissance", "la croissance", "un moment", "un moment", "Les entreprises", "que le mot", "les entreprises", "la croissance", "un moment", "développement économique", ", du coup", "un moment", "le mot", "du coup", "les entreprises", "du coup", "les entreprises", "c'est pas", ", du coup", "un moment", "les entreprises", "des trucs"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 209, "end": 215}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_211", "text": "<PERSON><PERSON>, voil<PERSON>, enfin c'est... c'est lié avec les bénéfices. Et là, je mettrais... on a parlé, là, du niveau des salaires ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les bénéfices"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 210, "end": 212}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_212", "text": "<PERSON><PERSON>, dans le... oui. Parce que là, c'est vraiment d'un point de vue économique.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un point", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 211, "end": 213}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_213", "text": "<PERSON><PERSON>, mon... enfin la problématique et tout ça, c'est... on a énormé... enfin moi, j'ai énormément de mal à... même avec mes gérants, mes dirigeants, on peut pas viser tout le temps une croissance, toujours vouloir plus que ce que... vouloir plus, forcément, à un moment, y'a forcément un moment où ça sera moins bien. Y'a forcément... et le problème de... ça va beaucoup mieux une fois qu'on... Plus viser la stabilité et la durabilité.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 75, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["<PERSON><PERSON> moi", "tout ça", "tout ça", "tout ça", "tout ça", "le temps", "le problème", "tout ça", "tout ça", "le problème", "tout ça", "tout ça", "tout ça", "le problème", "un moment", "un moment", "un moment", "un moment", "enfin la problématique", "tout ça", "enfin moi", "même avec mes gérants", ", mes dirigeants", "le temps", "une croissance", ", à un moment", "un moment", "la stabilité", "la durabilité", "La stabilité", "une croissance", "la durabilité", "Et la durabilité", ", c'", "un moment", "tout ça", "le temps"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 212, "end": 214}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_214", "text": "La stabilité et la soutenabilité, parce que on peut pas toujours... enfin si tout le monde veut faire plus, bah forcément, si on fait plus au détriment soit d'un autre, ou alors si on produit tous plus, bah c'est au détriment de la planète. Oui, donc en gros, c'est normal de... d'être en croissance, décroissance, etc., pour arriver à se stabiliser.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 1, "tension_strength": 0, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["tout le monde", "tout le monde", "la stabilité", "La stabilité", "la soutenabilité", "tout le monde", "la planète", ", décroissance", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 213, "end": 215}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_215", "text": "<PERSON><PERSON>, v<PERSON><PERSON>, mais en gros, c'était pas le... c'était le terme « croissance » qui... Te plaisait pas trop. Oui.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": [", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 214, "end": 216}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_216", "text": "Bah du coup, viser heu... je sais pas si on l'a peut-être mis là, je sais pas ? On en a parlé de ça, tout à l'heure, ça me parle.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["du coup", "du coup", ", ça", "du coup", "du coup", "du coup", "du coup", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 215, "end": 217}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_217", "text": "Ou soutenabilité, enfin les deux vont. Plutôt que la croissance ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["la croissance", "les deux", "les deux", "les deux", "les deux", "les deux", "la croissance", "la croissance", "la croissance", "les deux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 216, "end": 218}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_218", "text": "Mais ce sera pas l'entreprise. <PERSON><PERSON><PERSON> que regarde, c'est « indicateur du succès en termes de lancement de nouveaux produits ou de brevets déposés ».", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["de nouveaux produits", "Parce que regarde, c'est « indicateur", "nouveaux produits", "de brevets", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 217, "end": 219}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_219", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, pour... innovation, pour moi, le... le danger de l'innovation, c'est de chercher toujours plus loin, mais pas forcément dans le bien. Donc ce serait plutôt l'innovation heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pour moi", "ce serait", "<PERSON><PERSON>, pour... innovation", ", le... le danger", "pas forcément dans le bien", "Donc ce serait plutôt l'innovation heu", "l'innovation heu", "l'innovation heu", "l'innovation heu", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 218, "end": 220}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_220", "text": "Je... enfin je sais pas, oui, viser une innovation heu... soutenable. Oui. Soutenable éthique, je sais pas comment... T'es pas convaincue. <PERSON><PERSON> après, je pense pas que toutes les entreprises doivent être innovantes. <PERSON><PERSON> apr<PERSON>, aller dans l'innovation...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les entreprises", "toutes les entreprises", "les entreprises", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "une innovation heu", "Soutenable éthique", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "les entreprises", "les entreprises", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 219, "end": 224}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_221", "text": "En termes de brevets et de produits, quoi, pour moi, l'innovation des entreprises, ça va plutôt être dans le... à la fois dans l'énergie, dans le... le concret, donc les bâtiments, mais aussi dans le social, dans... Les ent...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["pour moi", "des entreprises", "la fois", "la fois", "des entreprises", "la fois", "des entreprises", "des entreprises", "la fois", ", ça", "des entreprises", "de brevets", "de produits", "le... à la fois", "le... le concret", "les bâtiments", "aussi dans le social", "... Les ent", ", ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 220, "end": 222}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_222", "text": "bah oui, les entreprises innovantes heu... doivent viser des innovations éthiques, enfin je... parce que du coup, je suis d'accord avec toi, toutes les entreprises ne doivent pas chercher à innover. Heu... le maçon du coin... Mais chercher plus l'amélioration continue que l'innovation heu... Chercher plus l'amélioration que l'innovation, c'est ça que tu veux dire ? Ou on précise, on dit la phrase que vous avez dites, sinon. Faut que toutes les entreprises ne doivent pas...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 76, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["C'est ça", "C'est ça", "les entreprises", "du coup", "du coup", "toutes les entreprises", "parce que du coup", "parce que du coup", "parce que du coup", "C'est ça", "les entreprises", "du coup", "du coup", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "l'innovation heu", "toutes les entreprises", "les entreprises innovantes", "des innovations éthiques", "toutes les entreprises", "Heu... le maçon", "l'innovation heu", "que l'innovation", "la phrase", "toutes les entreprises", "toutes les entreprises", "l'innovation heu", "la phrase", "la phrase", "toutes les entreprises", "du coup", "les entreprises", "du coup", "les entreprises", ", c'", "les entreprises"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 221, "end": 226}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_223", "text": "<PERSON><PERSON>, toutes les entreprises ne doivent pas chercher à innover et ce... celles qui recherchent à innover doivent le faire dans une démarche soutenable ou respectueuse, enfin avec des produits innovants sout... soutenables ou respectueux de l'environnement, éthiques. Ça te va aussi ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une démarche", "les entreprises", "une démarche", "toutes les entreprises", "une démarche", "les entreprises", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "toutes les entreprises", "une démarche soutenable ou respectueuse", "enfin avec des produits innovants", "toutes les entreprises", "les entreprises", "les entreprises", "les entreprises", ", éthique", "... Celles"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 222, "end": 224}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_224", "text": "<PERSON><PERSON>, la rentabilité, heu... être rentable... <PERSON><PERSON>, si, mais il me semble, celui-là, on en a parlé.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la rentabilité", ", celui"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 223, "end": 225}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_225", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, je... enfin... les parts de marché. Vous avez dit, juste, c'est dans un cadre respect écologique, non ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un cadre", "Les parts", "les parts", ", c'", "Les parts"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 224, "end": 226}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_226", "text": "Les parts de marché, je le mettrais pas, lui, parce que en gros, c'est chercher à... enfin c'est chercher à faire heu... à se battre avec ses concurrents. Or, quand tu te bats avec tes concurrents, c'est forcément au détriment...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["Les parts", "ses concurrents", "tes concurrents", "tes concurrents", "les parts", ", c'", "Les parts"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 225, "end": 227}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_227", "text": "enfin... <PERSON><PERSON>.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 2, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 226, "end": 228}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_228", "text": "C'est soit que tu vas chercher à avoir une politique agressive en réduisant tes coûts, ou à l'inverse une po... une politique où tu vas acheter moins... enfin chercher à mettre la pression sur tes fournisseurs pour améliorer ta marge et pouvoir étouffer tes concurrents, enfin c'est pas... pour moi, c'est pas... ça ne fait pas partie des critères de performance à prendre en compte, la part de marché. Oui, non, enfin je sais pas ce que tu en penses, mais les parts de marché, je le mettrais pas dans les...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 91, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["pour moi", "à l'inverse", "Une politique", "une politique", "de performance", "une politique", "la pression", "Les parts", "tes concurrents", "une politique agressive", "tes coûts", "une po", "la pression", "tes fournisseurs", "ta marge", "tes concurrents", "c'est pas...", "les parts", ", c'", "c'est pas", "Les parts"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 227, "end": 229}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_229", "text": "Je dirais plutôt aller vers une heu... une entente et une intelligence heu... Bah la mise en commun des ressources et non pas dans un but de compétition, mais d'amélioration, de partage des connaissances, des compétences, des méthodes de travail, des savoir", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 3, "tension_strength": 3, "total_indicators": 3}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une heu", "une heu", "des connaissances", "des connaissances", "une heu", "des ressources", "une heu", ", des compétences", ", des méthodes", "une heu"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 228, "end": 230}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_230", "text": "<PERSON><PERSON>, c'est à l'inverse, en disant que ce soit un critère de... un critère de performance, typiquement, là, y'a... dernièrement, les tickets restaurant Sodexo, ils ont tous été condamnés pour justement un... parce qu'ils ont créé un oligopole et y'avait que eux sur le marché, ils ont... ils avaient mis des barrières à l'entrée pour leurs concurrents. Du coup, y'avait que eux, et donc ils ont été condamnés là à 300 millions de... d'euros d'amende, je crois.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 77, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["que eux", "à l'inverse", "du coup", "du coup", "un critère", "de performance", "du coup", "du coup", "<PERSON><PERSON>, c'est à l'inverse", "disant que ce soit un critère", "... un critère", "les tickets", "un oligopole", "le marché", "des barrières", "leurs concurrents", "là à 300 millions", "... d'euros", "du coup", "du coup", ", c'"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 229, "end": 231}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_231", "text": "Et même heu... quitte à... enfin aller au-delà heu... de... d'indicateurs, parce que typiquement, mon cabinet n'a pas l'obligation de faire l'index d'égalité hommes-femmes, mais j'ai poussé et on va... il va être établi au sein du cabinet. Donc même... alors qu'on n'a pas l'obligation, parce qu'on a... au niveau de l'effectif, on est en dessous, mais du coup, on va quand même le faire.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 65, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["mon cabinet", "mon cabinet", "du coup", "<PERSON><PERSON> du <PERSON>", "du coup", "<PERSON><PERSON> du <PERSON>", "<PERSON><PERSON> du <PERSON>", "<PERSON><PERSON> du <PERSON>", "du coup", "du coup", "<PERSON><PERSON> du <PERSON>", "... d'indicateurs", "mon cabinet", "égalité hommes-femmes", "du coup", "du coup", "Égalité hommes-femmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 230, "end": 232}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_232", "text": "Donc je peux mettre « voire aller plus loin » ? Voilà, voilà.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 231, "end": 233}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_233", "text": "C'est compliqué. Ah oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 4, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 232, "end": 234}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_234", "text": "Enfin chaque... par exemple, les cabinets comptables, ils le font pas du tout, parce qu'ils ont peur heu... que ça devienne le cahier des doléances des clients. Et que du coup, eux, s'ils y regardent, ils mesurent plutôt par des indicateurs externes, c'est-à-dire le nombre... le nombre de clients qui décident de quitter le cabinet.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le cabinet", "le nombre", "par exemple", "du coup", "du coup", "les cabinets comptables", "que ça", "du coup", "du coup", "du coup", "du coup", "les cabinets comptables", "le ca<PERSON>er", "des indicateurs externes", "le cabinet", "le ca<PERSON>er", ", c'", ", eux", "de client", "que ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 233, "end": 235}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_235", "text": "on n'est pas contents, enfin vous répondez pas à ça ou alors votre pièce, elle était mal faite, mais bon, on a réussi à la faire, mais c'est pas... », enfin ils ont... O<PERSON>, mais c'est hyper important, en fait, parce que c'est pas parce que t'achètes quelque chose que t'es content de ce que t'as acheté, au final. Si tu reviens... en gros, du coup, si tu reviens plus chez lui, bah c'est...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 74, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["quel<PERSON> chose", "quel<PERSON> chose", "du coup", ", du coup", "quel<PERSON> chose", "en fait", ", du coup", "du coup", ", du coup", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "c'est pas...", "du coup", "du coup", "alors votre pièce", "quel<PERSON> chose", "gros, du coup", "c'est pas", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 234, "end": 237}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_236", "text": "si tu lui commandes plus la pièce, c'est que tu n'en étais pas... enfin dans les relations professionnelles, souvent, c'est sur la durée, c'est en quantité, c'est sur le long terme. Donc si une personne ne revient plus, c'est... C'est qu'elle n'est pas... C'est qu'elle n'est pas satisfaite.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 48, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 0.6}, "noun_phrases": ["une personne", "une personne", "c'est qu'", "la pièce", ", c'", "la durée", "une personne"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 235, "end": 238}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_237", "text": "À l'inverse, si elle continue de rester, bah même... et même eux, ils disent : bah ça fait que au final, elle est pas si insatisfaite, parce que si on lui demande, forcément, elle va... <PERSON><PERSON>, c'est sûr.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["à l'inverse", ", c'", "et même eux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 236, "end": 238}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_238", "text": "Voilà. Dans ce cas-là, je sais pas, heu...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 8, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ce cas", "ce cas", "ce cas", "ce cas", "ce cas", "ce cas", "ce cas"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 237, "end": 239}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_239", "text": "a<PERSON><PERSON><PERSON>, le... le client, en soi, avec les... tu vois, typiquement, les avis Google, ils ont déjà pas mal de pouvoirs là-dessus. C'est fini.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pas mal", "pas mal", "le... le client", "le client"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 238, "end": 240}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_240", "text": "Ah oui, d'accord, tu as déjà trié. Là, par contre, on n'a rien mis sur celle-là. Les parts de marché, oui. On note quoi, du coup ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["du coup", ", du coup", ", du coup", "du coup", ", du coup", ", du coup", ", du coup", ", du coup", "du coup", "du coup", ", du coup", "Les parts", "les parts", "du coup", "du coup", "Les parts", ", du coup"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 239, "end": 243}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_241", "text": "<PERSON><PERSON> rien, a<PERSON><PERSON><PERSON>, si on l'utilise pas, on l'utilise pas, hein. <PERSON><PERSON>, on l'utilise pas.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 15, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON>u rien"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 240, "end": 242}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_242", "text": "<PERSON><PERSON>, y'a pas de souci, celui-là. Parce que du coup, l'environnement, tout ça... en gros, c'est lié à la trésorerie, quoi, la bonne gestion de la trésorerie. Tu peux mettre entre parenthèses, peut-être, une bonne gestion de la... « Bonne gestion de la trésorerie » ? Oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 47, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["tout ça", "tout ça", "tout ça", "du coup", "tout ça", ", tout ça", "tout ça", "tout ça", "du coup", ", tout ça", "parce que du coup", ", l'environnement", ", l'environnement", "tout ça", "parce que du coup", "tout ça", "tout ça", "parce que du coup", "du coup", "du coup", "tout ça", "du coup", "du coup", ", c'", "la trésorerie", "de souci", ", celui", "tout ça", "la trésorerie", "la trésorerie", "peut-être, une bonne gestion", "la... « Bonne gestion", "la trésorerie"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 241, "end": 245}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_243", "text": "Tu peux mettre dans les... avec les entreprises envisa... enfin je sais que certains groupes le font heu... quand y'a des actionnaires, enfin des dirigeants en commun. Même, je sais que dans le milieu associatif, ça se fait, enfin je suis aussi trésorier d'une association, je sais qu'on a fait un prêt à une autre association qui avait justement des problèmes de trésorerie.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 63, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["des problèmes", "des problèmes", "des problèmes", "des problèmes", "les entreprises", "des problèmes", "des problèmes", "des problèmes", "des problèmes", "le milieu associatif", "Milieu associatif", "le milieu associatif", "le milieu associatif", "le milieu associatif", "une association", "une association", ", ça", "les entreprises", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "les entreprises", ", ça", "les entreprises", "les entreprises envisa", "certains groupes", "quand y'a des actionnaires", "enfin des dirigeants", "le milieu associatif", "une association", "un prêt", "une autre association", "des problèmes", "les entreprises", "des problèmes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 242, "end": 244}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_C_seg_244", "text": "Et du coup, on leur a... enfin on a... on a fourni... enfin prêté 100000 euros à l'association et comme ça, eux, ils ont pu faire... <PERSON><PERSON>, ça, je pense qu'y'a déjà pas mal de... de dispositifs pour aider les entreprises. C'est très limité, parce que c'est le monopole des banques. Oui, mais t'as... t'as d'autres systèmes. T'as les investisseurs, t'as...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["pas mal", "Et du coup", "les entreprises", "comme ça", "comme ça", "du coup", "et comme ça", "a<PERSON><PERSON>ès, ça", "Et du coup", "Comme ça", "comme ça", "Et du coup", "comme ça", "pas mal", "du coup", "des banques", "Et du coup", "Et du coup", "Et du coup", "Comme ça", "Comme ça", "Et du coup", ", ça", "comme ça", "les entreprises", "Et du coup", "du coup", "du coup", "les entreprises", "les entreprises", "Les entreprises", "les entreprises", "du coup", "les entreprises", "du coup", ", ça", "les entreprises", ", eux", "100000 euros", "comme ça", "... de dispositifs", "les entreprises", "d'autres systèmes", "les investisseurs", "comme ça", "le monopole", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 243, "end": 248}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_245", "text": "apr<PERSON>, t'as la bourse et des trucs comme ça, à d'autres échelles, quoi, mais... après, à petite échelle, oui, c'est compliqué. Voilà, c'est beaucoup plus une entreprise...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "comme ça", "comme ça", "Comme ça", "comme ça", "des trucs", "comme ça", "une entreprise", "une entreprise", "Comme ça", "Comme ça", "<PERSON> tru<PERSON>", "comme ça", "une entreprise", "une entreprise", "des trucs", ", c'", "comme ça", "la bourse", "des trucs", "comme ça", "d'autres échelles", "<PERSON><PERSON><PERSON><PERSON>, à petite échelle", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 244, "end": 246}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_246", "text": "la PME heu... d'en face, elle a des problèmes de trésorerie, t'as confiance, tu sais qu'il va être heu... c'est juste une mauvaise passe, là, le temps, bah tu lui prêtes de l'argent et ça lui évite d'emprunter, avoir un taux d'intérêt à 5 %, alors que toi, ton argent, tu l'aurais placé sur un compte à terme, enfin les entreprises, pour pas les placer sur des comptes rémunérés, donc ils seraient pas tant rémunérés que ça, et ça fait bah que t'es solidaire et tu sais que lui, tu lui as donné un coup de main, donc il te rendra l'appareil. Mais ça, c'est plus limité, malheureusement, les... les prêts entre entreprises, parce que les banques font moins le monopole, font du lobbyisme.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 124, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.8064516129032259, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.32258064516129037}, "noun_phrases": ["Et ça", "un taux", "des problèmes", "des problèmes", "des problèmes", "des problèmes", "les entreprises", "des problèmes", "des problèmes", "le temps", "des problèmes", "des problèmes", "que ça", "les entreprises", "un taux", "un taux", "les entreprises", "du lobbyisme", "<PERSON><PERSON>a", "les entreprises", "Les entreprises", "les entreprises", "le temps", "les entreprises", "les entreprises", ", c'", "des problèmes", "les entreprises", "la PME heu", "en face", "des problèmes", "le temps", "un taux", "5 %", "alors que toi", ", ton argent", "un compte", "enfin les entreprises", "des comptes rémunérés", "que ça", "un coup", "<PERSON><PERSON>a", "les... les prêts", "les banques", "le monopole"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 245, "end": 247}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_C_seg_247", "text": "Dans deux trucs différents, du coup ? Oui. Bien-être au travail ? Oui. Éthique ? Je mets les deux ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les deux", "du coup", ", du coup", ", du coup", "du coup", ", du coup", "les deux", "les deux", ", du coup", ", du coup", "les deux", "les deux", ", du coup", "du coup", "du coup", ", du coup", "du coup", "du coup", ", du coup", "deux trucs différents", "les deux"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 246, "end": 252}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_248", "text": "<PERSON><PERSON>, bien-être au travail, éthique, ça va ensemble, enfin ... Celles-là, vous les avez utilisées aussi ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 0.6}, "noun_phrases": [", ça", ", ça", ", éthique", "... Celles"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 247, "end": 249}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_C_seg_249", "text": "Donc pas utilisé. On va mettre comme ça. Tu fais quoi ? Tu les as prises déjà ou... ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["comme ça", "comme ça", "Comme ça", "comme ça", "comme ça", "Comme ça", "Comme ça", "comme ça", "comme ça", "comme ça", "comme ça"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 248, "end": 252}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_250", "text": "Je les ai prises. Je te les laisse si tu veux les reprendre en photo. Bah comme ça, j'ai tout, peut-être, oui. Fin de la matinée.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["comme ça", "comme ça", "Comme ça", "comme ça", "comme ça", "Comme ça", "Comme ça", "comme ça", "comme ça", "comme ça", "comme ça", "la matinée"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 249, "end": 253}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_251", "text": "Date : 29/12/2023 Nom du fichier : « C1 »", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 10, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["« C1"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 250, "end": 252}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_C_seg_252", "text": "Commanditaire : <PERSON> : 92 minutes Remarques particulières : en italique les modératrices du groupe global. Quelques time codes.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["<PERSON>", "92 minutes", "les modératrices", "groupe global", "Quelques time codes"]}, "metadata": {"source": "data_renamed\\Table_C.docx", "segment_lines": 1, "position": {"start": 251, "end": 253}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}]}