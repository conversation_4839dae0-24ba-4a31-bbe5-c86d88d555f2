{"source_file": "data_renamed\\Table_D.docx", "processed_timestamp": "2025-06-12T14:10:01.878066", "ml_target_format": "data_json_compatible", "segments": [{"id": "Table_D_seg_001", "text": "Début de la retranscription : OK , bah c'est parti. Bah vas-y. Prenez bien le temps de parler du cheminement de votre réflexion, en fait, à chacun, pour vous présenter mutuellement ce que vous aviez...", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la retranscription", "le temps", "votre réflexion", "votre réflexion", "en fait", "Le temps", ", en fait", ", en fait", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 0, "end": 3}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_002", "text": "vous commentez, vous mettez en commun, dans le sens où vous partagez finalement quelle a été votre réflexion, votre fil directeur, sur ces trois points. Donc y'a un quart d'heure pour les trois points, donc on peut imaginer que collectivement, vous preniez cinq minutes pour évoquer chacun des points.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 3, "tension_strength": 3, "total_indicators": 3}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["votre réflexion", "le sens", "quelle a été votre réflexion", ", votre fil directeur", "ces trois points", "un quart", "les trois points", "cinq minutes", "cinq minutes", "cinq minutes", "cinq minutes", "votre réflexion", "cinq minutes", "le sens", "un quart"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 1, "end": 3}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_003", "text": "Donc y'avait les mots-clés, y'avait la légitimité et puis y'avait la performance. D'accord ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les mots-clés", "la légitimité", "la performance", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "La performance", "la performance", "la légitimité", "la légitimité", "la légitimité", "la performance", "La performance", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 2, "end": 4}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_004", "text": "<PERSON> vous pouvez, quand vous prenez la parole, alors quand vous rebondis<PERSON><PERSON>, vous embêtez pas à le faire, mais au moins quand vous prenez la parole, c'est de dire que vous êtes « carré vert 2 », voilà, simplement ça. Voilà, faites", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la parole", "la parole"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 3, "end": 5}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_005", "text": "<PERSON><PERSON>, atelier 1, l<PERSON><PERSON>im<PERSON>. <PERSON><PERSON>, donc je suis « rouge carré numéro 2 », donc heu... je suis avec heu ... bah <PERSON>, à toi.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>", "... bah <PERSON>, à toi."]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 4, "end": 8}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_006", "text": "Heu... bah moi, je suis donc « rouge carré 3 ». <PERSON><PERSON> moi, « rouge carré 1 »", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Et moi", "Et moi", "Et moi"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 5, "end": 7}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_007", "text": "Donc on peut aborder le thème de la bonne boîte, donc dans cet atelier légitimité. <PERSON>, qu'est-ce qu'est une bonne boîte pour toi ? Eh bien moi, je suis... je... on se dit, on va d<PERSON><PERSON>ler tout ce qu'y'a sur heu... tout ce qu'on a noté dessus pendant cinq minutes ? Ce que tu as noté, oui.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["cinq minutes", "le thème", "la bonne boîte", "donc dans cet atelier légitimité", "bien moi", "tout ce", "tout ce", "cinq minutes", "une bonne boîte", "une bonne boîte", "cinq minutes", "tout ce", "cinq minutes", "le thème", "une bonne boîte", "tout ce", "tout ce", "tout ce", "cinq minutes", "tout ce", "tout ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 6, "end": 10}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_008", "text": "<PERSON><PERSON>, je suis issu de l'économie sociale et solidaire, donc en fait, moi, ma vision, elle est essentiellement sur une économie qui est une économie non marchande ou à lucrativité limitée. Du coup, j'estime qu'une bonne boîte doit être dans l'économie sociale et solidaire.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["l'économie sociale et solidaire", "ma vision", "une économie", "une bonne boîte", "une bonne boîte", "l'économie sociale et solidaire", "de l'économie", "l'économie sociale et solidaire", "donc en fait", "une bonne boîte", "donc en fait", "en fait", ", j'", "une économie", "l'économie sociale et solidaire", "du coup", "du coup", "donc en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 7, "end": 9}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_009", "text": "Je suis aussi issu du coopérativisme, donc je... une bonne boîte doit être une coopérative, une coopérative, c'est-à-dire une entreprise qui appartient d'abord, avant tout, à ses membres, voire à ses membres et ses parties prenantes, donc ses clients et ses fournisseurs. Heu... qu'est-ce qui est important pour moi dans cette coopérative ? C'est qu'elle ait un ancrage territorial et une utilité sociale, qu'elle soit à taille humaine et qu'elle soit aussi membre d'un réseau, qu'elle soit à l'intérieur d'un réseau.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 81, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une bonne boîte", "une bonne boîte", "ses membres", "ses clients", "ses fournisseurs", "cette coopérative", "un ancrage territorial", "une utilité", "un réseau", "un réseau", "une entreprise", "ses fournisseurs", "ses membres", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "ses clients", "ses fournisseurs", "ses parties prenantes", "pour moi", "ses fournisseurs", "ses clients", "utilité sociale", "Une entreprise", "une entreprise", "une entreprise", "ses clients", "une bonne boîte", "une entreprise", "une entreprise", "une utilité", "une entreprise", "parties prenantes", "parties prenantes", "ses parties prenantes", "Une entreprise", "un réseau", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 8, "end": 12}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_010", "text": "<PERSON><PERSON> <PERSON>a, c'est les cinq raisons et les cinq principales qualités d'une entreprise en 2023. Heu... en termes de légitimité, heu... On va dire après, la légitimité. <PERSON>ui, OK, eh bien très bien, comme ça on fait un tour.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la légitimité", "ça, c'est les cinq raisons", "les cinq principales qualités", "une entreprise", ", OK", "un tour", "la légitimité", "la légitimité", "la légitimité", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "la légitimité", "comme ça", "la légitimité", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", ", OK", "la légitimité", "la légitimité", "une entreprise", ", OK", "une entreprise", "comme ça", "la légitimité", "Une entreprise", "une entreprise", "Une entreprise", "la légitimité", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 9, "end": 13}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_011", "text": "<PERSON><PERSON> moi, ce que je note, moi <PERSON> « rouge carré 2 », je note que... il peut... à tes yeux, la meilleure des sociétés est une société coopérative. C'est une société qui est dans l'économie sociale et solidaire et si elle est coopérative, c'est encore mieux. Et sous forme coopérative. Oui. D'accord.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["l'économie sociale et solidaire", "<PERSON><PERSON> moi", "tes yeux", "Et sous forme coopérative", "l'économie sociale et solidaire", "donc moi", "l'économie sociale et solidaire", "Moi, ce", "une société", "l'économie sociale et solidaire"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 10, "end": 15}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_012", "text": "Donc qui... l'économie sociale et solidaire, ça m'interpelle, ça veut dire que s'il y a d'autres sociétés qui sont pas dans l'économie sociale et solidaire en termes d'activité principale, heu... et qui ne sont pas des sociétés coopératives, selon toi, est-ce que... on ne peut pas être de bonnes boîtes ? Ou est-ce qu'il y a quand même une vie en dehors de... Alors je sais... je sais pas si c'est le lieu de débat, mais en fait, ça, c'est ce que moi j'ai noté sur ma feuille. D'accord, OK.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 90, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["l'économie sociale et solidaire", ", OK", "d'autres sociétés", "l'économie sociale et solidaire", "activité principale", "quand même une vie", "ma feuille", "accord, OK", "l'économie sociale et solidaire", ", OK", "en fait", ", OK", ", c'est ce", ", c'est ce", "l'économie sociale et solidaire", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 11, "end": 14}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_013", "text": "<PERSON><PERSON>, on pourra débattre de... du bienfondé de... de la coopération et de l'économie sociale et solidaire, mais heu... Donc on fait tous l'exposé maintenant. Je pense que là, on a juste 15 minutes.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["l'économie sociale et solidaire", "l'économie sociale et solidaire", "... du bienfondé de", "... de la coopération", "de l'économie", "tous l'exposé", "juste 15 minutes", "juste 15 minutes", "l'économie sociale et solidaire", "la coopération", "l'économie sociale et solidaire"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 12, "end": 14}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_014", "text": "<PERSON><PERSON>, une bonne boîte, avant tout, elle doit dégager du cash, faire des bénéfices ou elle n'existe plus. C'est une question de survie, hein.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une bonne boîte", "une bonne boîte", ", une bonne boîte", "des bénéfices", "des bénéfices", "une bonne boîte", "une question", "des bénéfices"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 13, "end": 15}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_015", "text": "Et je pense que être conscient de ces enjeux de marché versus des enjeux sociétaux, c'est aussi savoir s'allier, c'est aussi savoir faire ensemble, par exemple, partager un outil de production, par exemple acheter une machine à plusieurs parce qu'on sait qu'on l'utilisera pas H24. En revanche, que si on est quatre à l'utiliser en termes de production, eh bah là, on... elle sera rentabilisée et que il faut accepter de travailler ensemble.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 73, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["ces enjeux", "des enjeux sociétaux", ", c'est aussi savoir faire ensemble", "un outil", "une machine", "un outil", "de ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 14, "end": 16}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_016", "text": "<PERSON>h je pense qu'on a fait cinq minutes, là. Heu... on va partir sur la légitimité, ce qu'on entend par la légitimité.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["cinq minutes", "la légitimité", "cinq minutes", "cinq minutes", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "cinq minutes", "sur la légitimité", "la légitimité", "la légitimité", "la légitimité", "cinq minutes", "la légitimité", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 15, "end": 17}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_017", "text": "Heu... donc moi, ça découle bien évidemment des cinq critères que j'ai mis, avec les cinq qualités en tout cas que j'ai mis au début, qui sont très orientées aussi de l'économie sociale et solidaire. Donc pour moi, une bon... pour qu'elle soit légitime, il faut qu'elle soit en phase avec ses valeurs éthiques et politiques. Heu...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 2.0, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["l'économie sociale et solidaire", "<PERSON><PERSON> moi", "l'économie sociale et solidaire", "de l'économie", "donc moi", "cinq critères", "les cinq qualités", "tout cas", "l'économie sociale et solidaire", "ses valeurs éthiques et politiques", "pour moi", "ses valeurs", "l'économie sociale et solidaire"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 16, "end": 19}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_018", "text": "donc qui peut se décliner sur certains aspects comme un particulier qui répond à une raison d'être ou heu... ou à certains égards un manifeste. Heu... qui a réussi à déterminer un projet politique qui guide l'organisation. Heu... qui sait aussi prendre en compte des attentes sociales, sociétales et environnement de ses membres et de son environnement.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["ses membres", "certains aspects", "une raison", "un projet politique", "attentes sociales", "ses membres", "de son environnement", "son environnement", "son environnement", ", sociétal", "un projet", "son environnement"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 17, "end": 20}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_019", "text": "<PERSON><PERSON> ça, ça veut dire heu... de... de ne pas juste avancer sans heu... savoir que à l'intérieur de son organisation, il existe des membres et que tout ce que l'organisation fait, elle va influer forcément sur son environnement.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["tout ce", "tout ce", "son organisation", "des membres", "tout ce", "son environnement", "son environnement", "tout ce", "tout ce", "son organisation", "tout ce", "son environnement", "tout ce", "tout ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 18, "end": 20}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_020", "text": "Elle va aussi influer sur heu... de manière sociétale, donc c'est heu... dans un premier cercle, les membres intérieurs de l'organisation, son environnement proche, et puis après, la société en entier. Et donc heu... pour revenir aussi à un des critères heu... des qualités que j'ai mis au début, c'est heu... qui fait aussi partie d'un réseau relatif à son activité ou à son secteur.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 65, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["un réseau", "un réseau", "son environnement", "manière sociétale", "un premier cercle", "les membres intérieurs", ", son environnement", "puis apr<PERSON>, la société", "critères heu", "des qualités", "un réseau relatif à son activité ou à son secteur", "la société", "la société", "son environnement", "la société", "la société", "des critères", "la société", "la société", "un réseau", "des critères", "Des critères", "son environnement", "des critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 19, "end": 21}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_021", "text": "Donc je... je reviens sur un truc, c'est son aptitude à dégager du cash pour qu'une entreprise ait des réserves. Il faut que financièrement, elle soit saine.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "un truc", "une entreprise", "des réserves", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "qu'une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 20, "end": 22}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_022", "text": "-over, si on pouvait l'analyser, c'est hyper important, ses litiges sociaux qu'il peut avoir, alors c'est de la prud'homie, y'a plein d'autres choses , c'est les litiges aussi heu... commerciaux, ça peut être les litiges environnementaux, ça... ça me concerne pas, mais ça arrive quand même dans certaines boîtes.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["d'autres choses", "<PERSON><PERSON> bo<PERSON>", "d'autres choses", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 21, "end": 23}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_023", "text": "Je suis plutôt clean là-dessus, donc je suis légitime quand même. Je suis pas quelqu'un qui fait du green-washing ou qui fait autre chose. C'est son implication dans le monde économique et social. Je mets bien les deux. Une entreprise, elle re<PERSON>, elle se doit de donner.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 47, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "autre chose", "le monde économique et social", "les deux", "Une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "autre chose", "autre chose", "autre chose", "autre chose", "Une entreprise", "autre chose", "une entreprise", "autre chose", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 22, "end": 27}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_024", "text": "Donc une entreprise, à travers les hommes qui la composent, on se doit de redonner à l'économie, on se doit aussi de faire de l'inclusion, on se doit de faire de l'insertion. Voilà, ça, c'est hyper important, c'est ce qui fait la légitimité de l'entreprise pour moi. Et c'est son aptitude à traverser les crises économiques et sociétales. C'est son adaptabilité, hein, je reviens un petit peu là-dessus, mais voilà. D'accord.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 71, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["la légitimité", "une entreprise", "la légitimité", "la légitimité", "la légitimité", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Donc une entreprise", "travers les hommes", "la légitimité", "Et c'est son aptitude", "les crises économiques et sociétales", "pour moi", "la légitimité", "Une entreprise", "une entreprise", "les cri", "une entreprise", "une entreprise", "la légitimité", "la légitimité", "une entreprise", "une entreprise", ", c'est ce", ", c'est ce", "la légitimité", "Une entreprise", "une entreprise", "Une entreprise", "la légitimité", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 23, "end": 28}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_025", "text": "Donc j'ai pas trop développé derrière, mais c'est vraiment les gros critères que j'ai mis. OK.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["c'est vraiment les gros critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 24, "end": 26}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_026", "text": "Donc forcément, elle doit innover. Elle doit être heu... pour ça, il faut forcément qu'elle accepte des périodes où elle va être inconfortable, elle va être peut-être même mise en risque, pour réaliser vraiment des changements, hein et pas... des changements concrets. Il faut qu'elle... ses changements soient endogènes, hein, internes. Il faut aussi que ces changements soient externes, qu'ils soient faits en lien avec ses parties prenantes, en dehors, mais dans un comportement qui ne doit pas être un comportement de prédateur, ni en interne, ni en externe. Et comme ça, elle atteindra des objectifs qui auront... et il faut qu'ils aient heu... des valeurs d'exemplarité, des valeurs de... d'entraînement, de transmission, heu... par exemple « si je l'ai fait, toi aussi tu peux le faire », parce que voilà. C'est pour ça, une boîte légitime.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 137, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une boîte", "une boîte", "des périodes", "des changements", "pas... des changements", "ces changements", "ses parties prenantes", "être un comportement", "comme ça", "des valeurs", "... d'entraînement", ", de transmission", "des valeurs", "une boîte", "une boîte", "une boîte", "une boîte", "parties prenantes", "comme ça", "une boîte légitime", "parties prenantes", "ses parties prenantes", "une boîte", "une boîte"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 25, "end": 31}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_027", "text": "Aux yeux... légitime aux yeux donc de qui, on l'a pas dit, mais pour moi, de ses salariés, de ses fournisseurs, de ses clients, de la société, tout quoi. OK. OK, eh bah on part heu... vu qu'il reste cinq minutes, sur les critères de performance. Mo<PERSON>, j'ai commencé par dire : de quelle performance on parle ? Heu... à mon avis, y'a quand même plusieurs critères importants et j'en vois quatre : les performances économiques et financières, heu... des performances sociales et sociétales.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 84, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["cinq minutes", "cinq minutes", "ses clients", "ses fournisseurs", "ses salariés", "ses fournisseurs", "cinq minutes", "la société", "ses salariés", "ses clients", "ses fournisseurs", "pour moi", "ses fournisseurs", "ses clients", "la société", "tout quoi", "cinq minutes", "les critères", "quelle performance", "mon avis", "plusieurs critères importants", "les performances économiques et financières, heu... des performances sociales et sociétales", "les performances économiques et financières", "les performances", "les performances", "les performances", "les critères", "les performances économiques et financières", "la société", "les critères", "les cri", "ses clients", "la société", "les critères", "ses salariés", ", de ses fournisseurs", ", j'", "la société", "la société", "cinq minutes", "Les critères", "les critères", "les performances éco", "les performances économiques", "les performances", "les critères", "Les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 26, "end": 31}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_028", "text": "Et pour commencer sur les performances économiques et financières, heu... bien évidemment, il faut qu'une organisation puisse être au moins à l'équilibre, voire de dégager des excédents, mais heu... qui ne soit pas en déséquilibre avec son utilité sociale, c'est-à-dire qui soit en phase avec son utilité sociale. Et quand je parle d'utilité sociale, c'est une utilité sociale, une utilité sociétale et environnementale, que je mets heu... au même heu... au même titre et à la même heu... sans hiérarchisation.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 80, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 2.0, "performance_indicators": 1, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 1.0}, "noun_phrases": ["une utilité", "les performances économiques et financières", "une organisation", "des excédents", "son utilité sociale", "utilité sociale", ", une utilité", "... au même titre", "à la même heu", "les performances", "les performances", "les performances", "les performances économiques et financières", "une utilité", "une organisation", "une organisation", "une organisation", "les performances éco", "une organisation", "les performances économiques", "les performances"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 27, "end": 29}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_029", "text": "Donc heu... donc en fait, de... de ne pas accaparer du capital sans être... sans se rendre conscient que en fait, cette accaparement de capital peut avoir des im... des implications heu... sociales, sociétales ou environnementales. Donc heu... les performances, elles doivent être aussi limitées, financières, économiques j'entends.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 48, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 2.0, "performance_indicators": 1, "legitimacy_indicators": 2}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["donc en fait", "des im", "des implications heu", "les performances", "les performances", "les performances", "donc en fait", "en fait", ", sociétal", "les performances", "donc en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 28, "end": 30}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_030", "text": "Et puis des performances, bah pour le coup, pour faire vraiment le focus entre le... les performances sociales et sociétales, eh bah c'est pour son environnement, pour l'environnement et pour ses membres, donc ça revient un peu en termes de cohérence avec ce que je disais avant sur la légitimité. Heu... c'est que son... son... elle doit performer socialement et... et heu... sociétalement, heu... en interne, donc c'est-à-dire avec les membres qui la composent, et aussi en externe, heu... ça.... ça refait partie de la légitimité, c'est-à-dire en externe vis-à-vis de son environnement propre et de l'environnement global.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 98, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 2.0, "performance_indicators": 1, "legitimacy_indicators": 2}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["la légitimité", "ses membres", "la légitimité", "la légitimité", "ses membres", "de son environnement", "son environnement", "la légitimité", "la légitimité", "les performances", "puis des performances", "bah pour le coup", "le focus", "les performances sociales et sociétales", "eh bah c'est pour son environnement", ", pour l'environnement", "pour ses membres", "un peu en termes", "sur la légitimité", "la légitimité", ", c'est-à-dire en externe", "les performances", "les performances", "son environnement", "c'est que", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "les performances", "son environnement"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 29, "end": 32}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_031", "text": "Donc c'est là où on arrive sur une... une notion de sociétal et d'environnemental. Voilà, donc les performances, juste pour heu... conclure que les performances, elles... les critères de performance, elles doivent être sociales et sociétales avant tout, mais sans oublier les performances économiques et financières qui doivent pas être en déséquilibre avec justement l'utilité sociale.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 2.0, "performance_indicators": 1, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["les critères", "les performances économiques et financières", "utilité sociale", "les performances", "une... une notion", "les performances", "juste pour heu", "les performances", "les critères", "les performances économiques et financières", "justement l'utilité sociale", "... une notion", "les critères", "les cri", "les critères", "Une notion", "Les critères", "les critères", "les performances éco", "les performances économiques", "les performances", "les critères", "Les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 30, "end": 32}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_032", "text": "Performance en innovation, voilà, « il faut oser pour créer », je reprends cette devise que j'aime beaucoup, constamment. Et pour qu'une entreprise soit aussi... performance en prospective, c'est un jeu que j'aime beaucoup dans mon entreprise, et mon entreprise, en disant c'est quoi, c'est un jeu qu'on pratique chaque année, qui permet de corriger des choses aussi.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 58, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "cette devise", "qu'une entreprise", "mon entreprise", "chaque ann<PERSON>", "des choses", "des choses", "mon entreprise", "une entreprise", "une entreprise", "une entreprise", "mon entreprise", "une entreprise", "des choses", "Une entreprise", "une entreprise", "des choses", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "des choses", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 31, "end": 33}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_033", "text": "Il est important de dire : et dans dix ans ? Mon métier va changer, la société va changer, qu'est-ce que mon entreprise devient ?", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la société", "la société", "mon entreprise", "<PERSON><PERSON>", "la société", "mon entreprise", "la société", "mon entreprise", "la société", "la société"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 32, "end": 34}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_034", "text": "Cette performance en innovation, heu... c'est... elle est technique, commerciale, sociétale, peu importe, elle est très diverse, mais innover, ça veut dire changer. C'est aussi la qualité du pilotage financier, qui fait les cri... un critère de performance pour moi très important, mais ce pilotage financier, pour moi, il peut aujourd'hui passer par quelque chose de et ça, c'est de l'innovation.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 61, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un critère", "quel<PERSON> chose", "pour moi", "<PERSON>tte performance", "pilotage financier", "les cri", "un critère", "ce pilotage financier", "quel<PERSON> chose", "et ça", ", c'est de l'innovation", "pilotage financier", ", c'est de l'innovation", "un critère", "et ça", "quel<PERSON> chose", ", sociétal", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "un critère", "quel<PERSON> chose", "la qualité", "mais ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 33, "end": 36}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_035", "text": "Et j'aimerais que un jour, pouvoir développer le thème, d'ailleurs, parce que pour moi, c'est complètement faisable heu... de... d'avoir un pilotage financier qui passe par les euros et par ce qui est . Et j'ai des exemples très concrets d'entreprises qui... qui sont là-dedans, malheureusement peu nombreuses, mais j'ai des exemples concrets. Heu... si on... on a une qualité de pilotage financier, c'est que... si elle est bonne, cette qualité, cette....", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 72, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le thème", "pour moi", "pilotage financier", "le thème", "un pilotage financier", "les euros", "des exemples très concrets", "des exemples concrets", "une qualité", "pilotage financier", "c'est que", "un pilotage financier"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 34, "end": 38}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_036", "text": "ce critère de performance, on va arriver à attirer des financeurs, donc que ce soit des financeurs en capital, en emprunts ou en aides, ou toute autre, en fait, capacité de financement . La performance extra-financière ? C'est pas... pour moi, c'est pas la performance extra-financière. Pour moi, c'est la capacité à piloter financièrement en ayant conscience que cette notion de financier doit aujourd'hui être entendue en euros comme classiquement, et aussi en , parce que y'a possibilité d'avoir des... un pilotage financier et des... une capacité à attirer du financement en . Et là, j'ai... j'avoue que le mot n'est pas fait pour, mais moi, j'aimerais bien qu'on puisse dire que le financement, c'est de l'euro et aussi du et qu'inventer le financement en , c'est de l'innovation, mais c'est très économique, ça marche.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 135, "thematic_indicators": {"performance_density": 1.4814814814814814, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.7407407407407407}, "noun_phrases": ["la performance", "pour moi", "La performance", "La capa", "la performance", "pilotage financier", ", c'est de l'innovation", "un pilotage financier", "pilotage financier", "ce critère", "des financeurs", "en aides", "La performance extra-financière", "cette notion", "un pilotage financier", "une capacité", "le mot", "le financement", ", c'est de l'innovation", ", ça marche", "la capacité", "en fait", "le mot", "ce critère", ", j'", "la capacité", "la performance", ", en fait", "le mot", ", en fait", "La performance", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 35, "end": 40}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_037", "text": "Et puis y'a... donc le critère de performance, pour moi, important, c'est la qualité du dirigeant ou du management, que ce soit des valeurs entrepreneuriales et humaines, surtout, qui pour moi vont ensemble nécessairement, parce qu'une entreprise, c'est pas une machine, c'est pas une mécanique, hein. Y'a des gens dedans et sans les gens, elle est rien, heu... les gens qui la dirigent, les gens qui la font.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 68, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "une machine", "une entreprise", "les gens", "une entreprise", "une entreprise", "Une entreprise", "des valeurs", "pour moi", "Une entreprise", "Les gens", "une entreprise", "qu'une entreprise", "des valeurs", "le critère", "du management", "une entreprise", "des gens dedans", "sans les gens", ", heu... les gens", "les gens", "les gens", "une entreprise", "des gens", "une entreprise", "les gens", "les gens", "des gens", "une entreprise", "les gens", "les gens", "des gens", "les gens", "les gens", "les gens", "Une entreprise", "les gens", "les gens", "une entreprise", "des gens", "des gens", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "la qualité", "une entreprise", "les gens"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 36, "end": 38}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_038", "text": "Et il me semble que cette notion quand même de respect, de droiture heu... donc est très objective, et... et une... enfin pour moi, une entreprise performante aujourd'hui et demain, ça doit pas être une entreprise voyou. Je rejoins un petit peu quand même la notion de sur-profit et d'enrichissement à tout crin, qui heu... bah je le vois aussi beaucoup, heu... a des limites, peut avoir des limites, par exemple. Moi, j'ai... j'ai juste... je pense qu'on a encore un peu de temps. J'ai juste une... un éclaircissement à avoir sur le... l'aspect . Est-ce que c'est quelque chose qui est du coup non financier ou qui reste financier ?", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 111, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.9009009009009008}, "noun_phrases": ["une entreprise", "une entreprise", "quel<PERSON> chose", "une entreprise", "une entreprise", "Une entreprise", "la notion", "pour moi", "Une entreprise", "une entreprise", ", une entreprise", "quel<PERSON> chose", "c'est que", "cette notion", "une entreprise", "enfin pour moi", "une entreprise performante aujourd'hui", "quand même la <PERSON>", "tout crin", "des limites", "des limites", "une... un éclaircissement", "le... l'aspect", "une entreprise", "une entreprise", "la notion", ", j'", "la notion", "quel<PERSON> chose", "une entreprise", "quel<PERSON> chose", "la notion", "quel<PERSON> chose", "la notion", "Une entreprise", "quel<PERSON> chose", "une entreprise", "Une entreprise", "quel<PERSON> chose", "une entreprise", "une entreprise", "une entreprise", "... l'aspect", "une entreprise", "une entreprise", "du coup", "du coup", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 37, "end": 43}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_039", "text": "Ou est-ce que c'est une monnaie qui pourrait être heu... donc qui... qui n'est pas l'euro, mais qui pourrait être une monnaie heu... comme une monnaie locale complémentaire ? Ah non, ça, j'exclus complètement de remplacer l'euro par une monnaie même locale. Les monnaies locales sont des échecs patents, parce qu'ils ne poussent pas à modifier quoi que ce soit dans ce que nous sommes et dans comment nous fonctionnons. C'est une monnaie de toute façon. Je pense plutôt à des pratiques économiques qui sont heu... des pratiques de mutualisation, comme je l'ai dit un peu avant, et aussi des pratiques économiques.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 102, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["une monnaie locale", "une monnaie même locale", "Les monnaies locales", "quoi que ce soit", "toute façon", "des pratiques économiques", "des pratiques", ", j'", "des é", "de mutualisation"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 38, "end": 43}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_040", "text": "Alors j'ai l'exemple d'une entreprise, par exemple, heu... qui est sociétale, parce qu'elle va répliquer le modèle ailleurs, qui a créé un écosystème de production, fabrication au sein de l'Auvergne, autour... genre 50 kilomètres autour de Clermont-Ferrand, dans l'agricole notamment, ses matières premières sont agricoles et qui heu... donc s'appuie sur des agriculteurs en leur mettant à disposition un outil de production. C'est la fin de ce premier temps...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 69, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une entreprise", "un outil", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "le modèle", "un écosystème", "50 kilomètres", ", dans l'agricole", "ses matières premières", "des agriculteurs", "un outil", "ce premier temps", "ce premier temps", "matières premières", "matières premières", "une entreprise", "matières premières", "une entreprise", "de ce", "matières premières", "la fin", "matières premières", "matières premières", "matières premières", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "la fin"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 39, "end": 41}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_041", "text": "<PERSON><PERSON>, l<PERSON>, on était heu... sur une question qui était très ancrée en 2023, qu'est-ce qu'une bonne boîte en 2023, qu'est-ce qu'une boîte légitime en 2023, qu'est-ce qu'une boîte performante en 2023 ? Voilà, qu'est-ce qu'une boîte légitime et performante en 2023 ? <PERSON><PERSON>, maintenant, on va se projeter en 2050. On va se projeter en 2050.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 58, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une bonne boîte", "une bonne boîte", "une boîte", "une boîte", "une bonne boîte", "une boîte", "une question", "une boîte", "une boîte", "une boîte", "une boîte légitime", "une boîte", "une boîte"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 40, "end": 44}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_042", "text": "On vous a récapitulé ce que Alexis vous avait présenté tout à l'heure, les différentes conséquences d'un scénario +2 degrés en 2050, donc on a un petit récapitulatif ici, pour que vous puissiez heu... vous en rappeler et puis l'exploiter en fait dans votre réflexion. Et en fait, on va poser exactement la même question, mais là, ça va être un travail directement collectif, qu'est-ce qu'une boîte légitime en 2050 dans ces conditions-là ? Et quels seront les critères de performance d'une boîte en 2050 avec ces conditions-là ? Ça va ? Oui ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 94, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["votre réflexion", "une boîte", "une boîte", "les critères", "les critères", "les critères", "les cri", "les différentes conséquences", "un scénario", "+2 degrés", "votre réflexion", "la même question", "ces conditions", "les critères", "une boîte", "ces conditions", "en fait", "une boîte", "une boîte", "une boîte", "une boîte légitime", "Les critères", "ces conditions", "les critères", "les critères", "une boîte", "Les critères", "les critères", "une boîte", "les critères", "en fait", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 41, "end": 46}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_043", "text": "Donc on va partir aussi... alors là, vous aurez un temps plus long, heu... une demi-heure, d'accord, pour aborder ces sujets, donc vous pouvez bah approfondir, vous pouvez raturer, vous pouvez revenir sur ce que vous avez dit, vous pouvez itérer. Toutes les dix minutes à peu près, je vous dirai où est-ce qu'on en est, puisque si on a une demi-heure, on peut dire trois fois dix minutes, comme ça vous voyez où on est un peu dans le temps.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 81, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le temps", "comme ça", "un temps plus long", "heu... une demi-heure", "ces sujets", "Toutes les dix minutes", "une demi-heure", "trois fois", "une demi-heure", "comme ça", "Le temps"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 42, "end": 44}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_044", "text": "Pour l'instant, c'est que légitimité pendant 30 minutes. Pendant une demi-heure, juste légitimité, c'est ça ? <PERSON><PERSON>, que légitimité, pardon. Que la légitimité. Que légitimité pendant... d'accord, 30 minutes.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la légitimité", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "c'est que", "une demi-heure", "une demi-heure", "juste légitimité", "que légitimité", "Que légitimité", "la légitimité", "la légitimité", "C'est ça", "C'est ça", "C'est ça", "la légitimité", "la légitimité", "C'est ça"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 43, "end": 48}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_045", "text": "Donc heu... bah la légitimité, la légitimité... est-ce que par exemple, une légitimité, ce serait heu... de...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la légitimité", "la légitimité", "la légitimité", "une légitimité", "la légitimité", "la légitimité", "la légitimité", "une légitimité", "la légitimité", "la légitimité", "la légitimité", "ce serait", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 44, "end": 47}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_046", "text": "si je prends la responsabilité, est-ce qu'une entreprise légitime en 2050 pourrait être une entreprise qui très volontairement a limité l'utilisation de matières premières heu... de matières premières et limité volontairement par exemple heu... la nature des matières premières qu'elle doit absolument utiliser, ainsi que d'où elles viennent, pour éviter des transports, ou pas ? Alors je... je vais être beaucoup plus large dès le départ.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "la nature", "Une entreprise", "une entreprise", "qu'une entreprise", "une entreprise", "la nature", "une entreprise", "la responsabilité", "qu'une entreprise légitime", "matières premières", "matières premières et limité", "... la nature", "matières premières", "le d<PERSON><PERSON><PERSON>", "la nature", "des matières", "une entreprise", "des transports", "Une entreprise légitime", "une entreprise légitime", "matières premières", "<PERSON> matière<PERSON>", "une entreprise", "une entreprise légitime", "exemple heu", "des matières premières", "matières premières", "des matières premières", "matières premières", "matières premières", "matières premières", "Une entreprise", "une entreprise", "la nature", "Une entreprise", "exemple heu", "la nature", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 45, "end": 47}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_047", "text": "On est bien d'accord qu'on est toujours dans une société type capitalistique, comme on vit aujourd'hui, parce qu'en 2050, on peut tout imaginer, donc on reste quand même dans les mêmes contraintes économiques ? Bah plus ou moins, <PERSON><PERSON><PERSON>.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une société type capitalistique", "quand même dans les mêmes contraintes économiques", "<PERSON>h plus ou moins, <PERSON><PERSON><PERSON>", "une société", ", <PERSON><PERSON><PERSON>"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 46, "end": 48}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_048", "text": "Bah c'était un peu ce que disait <PERSON>, c'est-à-dire que en fait, c'est business as usual, donc en fait, on reste dans une société capitalistique. Oui, c'est ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["donc en fait", "donc en fait", "une société capitalistique", "<PERSON><PERSON>, c'est ça", "en fait", "une société capitalistique", "C'est ça", "C'est ça", "une société", "C'est ça", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça", "donc en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 47, "end": 49}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_049", "text": "Heu ... oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 3, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 48, "end": 51}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_050", "text": "<PERSON><PERSON>, je me pose aussi une question sur heu... là, en fait, on est droppés en 2050 alors qu'on est en 2023, donc heu... c'est un peu de temps après et en fait, avec tout le discours qu'on a eu auparavant, c'est-à-dire heu... les... les risques d'augmentation de la température globale,", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 51, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["croissance_decroissance", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["une question", "en fait", "tout le discours", "les... les risques", "la température globale", "la température globale", ", en fait", ", en fait", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 49, "end": 51}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_051", "text": "des... des changements climatiques, etc., donc ça, on... on vient de l'entendre et donc du coup, on... tac, on passe de 2023 à 2050 et... et quelle doit être la légitimité en 2050 ? C'est... en fait, on va parler d'un point de vue d'une personne en 2023 qui s'imagine être en 2050, avec tout ce que ça a pu heu... amener, c'est-à-dire probablement une augmentation de la température globale, etc., des tensions, voilà. Mais en fait, heu... ce... là où j'ai un peu du mal à... à rentrer dans la prospective du truc, c'est que en fait, ça fait quand même heu", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 103, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.970873786407767, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": ["croissance_decroissance", "local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["la légitimité", "tout ce", "tout ce", "la légitimité", "la légitimité", "tout ce", "la légitimité", "la légitimité", "des changements", "la légitimité", "c'est que", "en fait", "la température globale", "... des changements climatiques", "un point", "une personne", "tout ce", ", c'est-à-dire probablement une augmentation", "la température globale", "des tensions", "la prospective", "la légitimité", "la légitimité", "du mal", "une personne", "donc du coup", "tout ce", "Et donc du coup", "tout ce", "la légitimité", "la légitimité", "des tensions", "du coup", "Et donc du coup", "du coup", "tout ce", "tout ce", "donc du coup", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 50, "end": 53}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_052", "text": "... bah on l'a vu depuis grosso modo l'après-guerre, que en fait, heu... ça commence à partir un peu en vrille, voire même encore plus depuis les années 1980 et que finalement, la légitimité des organisations aujourd'hui, elle a... elle a pas pris en compte tout ce qui s'était passé heu... les 30, 40, 50 dernières années. Ah absolument pas. Elle a... elle a rien pris en compte. Et du coup, ma question est de savoir : est-ce qu'en 2050 on prendra plus en compte ce qui s'est passé les 30 dernières années ?", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 94, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la légitimité", "tout ce", "tout ce", "la légitimité", "la légitimité", "tout ce", "la légitimité", "la légitimité", "la légitimité", "en fait", "tout ce", "grosso modo", "partir un peu en vrille", "voire même encore plus depuis les années", "la légitimité", ", 50 dernières années", "Et du coup", "ma question", "les 30 dernières années", "la légitimité", "les années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "tout ce", "les 30 dernières années", "les 30 dernières années", "les années", "les années", "les années", "tout ce", "les années", "les 30 dernières années", "la légitimité", "la légitimité", "du coup", "du coup", "tout ce", "tout ce", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 51, "end": 55}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_053", "text": "Donc heu... enfin voilà, c'est une autre question qu'on... que je... que je pose, mais en fait, c'est... pour répondre à la légitimité en 2050, on doit bien répondre en tant que personne située en 2023, en prospective en 2050.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la légitimité", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "en fait", "la légitimité", "la légitimité", "tant que personne", "la légitimité", "la légitimité", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 52, "end": 54}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_054", "text": "Donc il faut aussi qu'on se mette en... en tête d'abord qu'on reste dans une économie capitalistique, comme tu le disais, mais que heu... à côté de ça, ça se trouve, on a pris conscience ou pas de... d'un changement climatique fort les... les 30 dernières années. Bah il peut y avoir aussi la... Suivant qu'on est au... au nord, au sud du monde, à l'est ou à l'ouest.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 69, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["une économie", "les 30 dernières années", "une économie capitalistique", "fort les... les 30 dernières années", "à l'ouest", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "une économie", "une économie capitalistique", "une économie capitalistique"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 53, "end": 55}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_055", "text": "sur-consomment des matières premières rares ou heu", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 7, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["matières premières", "matières premières", "des matières", "matières premières", "<PERSON> matière<PERSON>", "des matières premières", "matières premières", "des matières premières", "matières premières", "matières premières", "matières premières"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 54, "end": 57}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_056", "text": "... n'ont pas commencé quoi que ce soit en matière de réduction de leurs transports et de leur logistique, ou peut-être plus tard s'ajouteront des critères comme des entreprises qui par exemple refusent d'embaucher des gens venus de pays où ils peuvent plus vivre et qui ont été obligés à l'exil. Eh bah je pense que là, la réglementation a un fort rôle à jouer dans le fait que, que tu le veuilles ou non, pour être légitime en 2050, a minima, il faudra que tu respectes les règles, les lois et bah ça, on te demandera pas de choisir.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 100, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["quoi que ce soit", "la réglementation", "des entreprises", "leurs transports", "de leur logistique", "des critères", "des entreprises", "des gens", "la réglementation", "un fort rôle", "le fait", "le veuilles", "les règles", "les lois", "des gens", "des gens", "la réglementation", "des entreprises", "des gens", "des gens", "des critères", "Des critères", "Des entreprises", "des critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 55, "end": 57}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_057", "text": "Mais la question, elle est vraiment de savoir en quoi ça... en quoi une boîte en 2050 va être légitime ? Mais de manière faisable, pas forcément idéale, on est d'accord, hein", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["une boîte", "une boîte", "une boîte", "une boîte", "<PERSON><PERSON> la <PERSON>", "quoi une boîte", "Mais de manière faisable, pas forcément idéale", "une boîte", "la question", "une boîte", "la question", "une boîte", "une boîte"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 56, "end": 58}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_058", "text": ", parce que... <PERSON><PERSON>, on est dans de la prospective, donc je pense qu'on peut dire ce qu'on veut. Il faudrait que ce soit faisable quand même.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la prospective"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 57, "end": 59}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_059", "text": "Donc si on revient à un principe de base de ce qu'on disait, on a fait une projection avec un cadre au départ, il faut toujours... si le modèle... si le modèle politique ne change pas, il faudra toujours qu'une entreprise soit rentable. Et derrière le mot « rentable », je dis « au-dessus de zéro ».", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "qu'une entreprise", "le mot", "une entreprise", "une entreprise", "le modèle", "un principe", "une projection", "un cadre", "le modèle politique", "une entreprise", "le mot", "une entreprise", "de ce", "le mot", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 58, "end": 61}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_060", "text": "D'accord, donc je vais le marquer. Je vais juste aller aux toilettes. Mais on changera après. Oui.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 59, "end": 63}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_061", "text": "Donc rentable. Donc rentable, tu dis, <PERSON><PERSON><PERSON>, heu", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 8, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", <PERSON><PERSON><PERSON>"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 60, "end": 62}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_062", "text": "C'est deux choses différentes, encore. Alors rentable économiquement. Est -ce que je fon... je dis « afin de pouvoir continuer ses activités » ? C'est ça ? Oui, oui, tout à fait. «", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["ses activités", "C'est deux choses différentes", "ses activités", "C'est ça", "ses activités", "ses activités", "ses activités", "ses activités", "C'est ça", "ses activités", "C'est ça", "ses activités", "C'est ça", "deux choses"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 61, "end": 67}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_063", "text": "Suffisamment pour pouvoir continuer ses activités », est-ce que c'est une heu... « Rentabilité économique pour être durable ».", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 19, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["une heu", "ses activités", "ses activités", "ses activités", "Rentabilité économique", "Rentabilité économique", "ses activités", "ses activités", "ses activités", "rentabilité économique", "ses activités", "ses activités", "une heu"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 62, "end": 64}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_064", "text": "Ou avoir un EBE... … la clé sous la porte, d'accord, continuer ses activités sans être en danger. Ça va, ça ?", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ses activités", "ses activités", "ses activités", "ses activités", "la clé", "la porte", "la clé", "la porte", "la clé", "la porte", "un EBE", "la clé", "la porte", "ses activités", "être en danger", "être en danger", "la clé", "la porte", "ses activités", "être en danger", "ses activités", "ses activités"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 63, "end": 66}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_065", "text": "<PERSON><PERSON>, sans être en danger et puis une entreprise qui a des finances, elle peut investir, elle peut innover, elle peut distribuer. Bon après , on est sur les mêmes principes qu'en 2023, hein.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "être en danger", "des finances", "les mêmes principes", "être en danger", "être en danger", "une entreprise", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 64, "end": 67}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_066", "text": "Sans être en danger, je vais... je vais rester sur la notion de... <PERSON><PERSON>, j'aime bien parler avec des mots positifs. Jamais de mots noirs, que des mots... Rentable économiquement pour être durable,", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 33, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["la notion", "être en danger", "être en danger", "la notion", "... <PERSON><PERSON>", "des mots positifs", "Jamais de mots noirs", "être en danger", ", j'", "la notion", "la notion", "la notion"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 65, "end": 68}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_067", "text": "donc ne pas mettre la clé sous la porte, continuer ses activités sans être en danger. Après, bon, toutes les implications.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["ses activités", "ses activités", "ses activités", "ses activités", "la clé", "la porte", "la clé", "la porte", "la clé", "la porte", "la clé", "la porte", "ses activités", "être en danger", "être en danger", "la clé", "la porte", "ses activités", "être en danger", ", toutes les implications", "ses activités", "ses activités"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 66, "end": 68}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_068", "text": "Est -ce que demain, les nouvelles attentes des consommateurs, c'est de dire « est-ce que vous pouvez pas répondre à nos demandes à nous ?", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les nouvelles attentes", "nos demandes", "nouvelles attentes"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 67, "end": 69}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_069", "text": "<PERSON><PERSON>, je vais m'inscrire en faux, <PERSON><PERSON><PERSON> « rouge carré 1 », je vais m'inscrire en faux, parce que quand tu as dit ça, bah tu as levé ton smartphone, là je le dis pour l'enregistrement, et en fait, moi qui connais l'Histoire, heu... on a demandé aux gens heu... dans le passé, y'a longtemps, quand on leur posait la question de savoir si ils voulaient téléphoner de manière autonome en ayant un téléphone sur eux qui fonctionnait n'importe comment, ils ont tous répondu non. Ils ont répondu non, parce que « bah non, j'en ai pas besoin, parce que j'ai le téléphone chez moi, y'a des cabines téléphoniques dans la rue, y'a des téléphones dans les cafés, j'ai le téléphone à mon entreprise », donc personne n'en voyait l'utilité.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 131, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.3053435114503817}, "noun_phrases": ["<PERSON><PERSON>", "mon entreprise", "mon entreprise", "en fait", "<PERSON><PERSON>", "ton smartphone", "gens heu", "le passé", "la question", "manière autonome", "un téléphone", ", j'", "le téléphone", "des cabines téléphoniques", "la rue", "des téléphones", "les cafés", "le téléphone", "mon entreprise", "donc personne", "des téléphones", "la question", ", <PERSON><PERSON><PERSON>", "en fait", "chez moi"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 68, "end": 70}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_070", "text": "Donc en fait, ce qui aujourd'hui nous est indispensable à tous, par exemple, bah personne ne l'a... ne le voulait, ne l'exprimait comme un besoin, donc heu... tu vois ? J'entends et je comprends.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 34, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["donc en fait", "donc en fait", "en fait", "un besoin", "donc en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 69, "end": 71}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_071", "text": "-ce que c'est pas non plus... tu vois les scoubidous, ce que je veux dire, ce qu'on faisait dans les cours d'école. Alors est-ce qu'on peut résumer en disant...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 29, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les cours", "les scoubidous", "les cours"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 70, "end": 72}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_072", "text": "J'essaie de trouver un produit, tu vois ? Une entreprise légitime ne crée pas des demandes de superflu... Mais répond aux demandes des consommateurs, aux attentes des consommateurs. <PERSON><PERSON> ça, c'est autre chose. Ça, c'est autre chose.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "autre chose", "Une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "un produit", "Une entreprise légitime", "des demandes", "des demandes", "une entreprise légitime", "une entreprise", "une entreprise légitime", "autre chose", "autre chose", "autre chose", "un produit", "autre chose", "Une entreprise", "autre chose", "une entreprise", "autre chose", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 71, "end": 75}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_073", "text": "<PERSON><PERSON>, elle crée pas des demandes auprès des consommateurs. Superflues, énergivores.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des demandes", "des demandes"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 72, "end": 74}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_074", "text": "Heu... une entreprise légitime en 2050, donc ne crée pas... Une demande, on va la mettre entre guillemets.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise légitime", "une entreprise légitime", "Une demande", "une entreprise", "une entreprise légitime", "Heu... une entreprise", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 73, "end": 75}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_075", "text": "heu... Si on développait cette idée-là, je suis sûr qu'on trouverait plein de biens et services qui ont été développés au profit de certaines entreprises et qui sont énergivores, consommatrices pour rien.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["cette idée", "certaines entreprises", "de ce", "certaines entreprises"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 74, "end": 76}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_076", "text": "- sourcées . Et également heu... moi je dirais destructrices de l'humain, parce que....", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["... <PERSON><PERSON>"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 75, "end": 79}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_077", "text": "Social. Social, oui. Hu<PERSON>ine et sociale, parce que si par exemple ces scoubidous heu...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 76, "end": 79}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_078", "text": "ils sont fabriqués par des enfants au Bangladesh, voilà, donc on peut prendre... Ou... ou des personnes en France sous-payées ou heu... enfin... On peut mettre l'exemple du scoubidou entre parenthèses qui peut parler. De l'humain et du social.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des enfants", "Ou... ou des personnes", "du social", "des enfants"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 77, "end": 80}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_079", "text": "Alors on peut prendre un exemple, donc heu... exemple, scoubidou heu... en plastique, pour que les enfants... <PERSON><PERSON>, mais sans... sans aller trop dans le détail.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un exemple", ", scoubidou", "que les enfants", "le d<PERSON>", "un exemple"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 78, "end": 80}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_080", "text": "<PERSON><PERSON> vraiment, l'idée, c'est de dire : on va produire ce dont on a besoin. Et effectivement, dans les effets sur les organisations... On arrête de produire l'inutile.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 28, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["les effets", "les organisations", "les organisations"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 79, "end": 81}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_081", "text": "Parce que les gens, ce qu'ils veulent, c'est avoir. Et plus ils ont, plus ils sont.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les gens", "Les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 80, "end": 82}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_082", "text": "Donc heu... et ça, ça va pas changer beaucoup. C'est pour ça que mon idée de départ, c'est de dire : on... on part de créer le besoin, on crée le besoin auprès des consommateurs...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["et ça", "et ça", "le besoin", "le besoin", "Mon idée"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 81, "end": 83}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_083", "text": "<PERSON><PERSON> u<PERSON> , tu vois, c'est... tu vois, <PERSON><PERSON><PERSON>, la notion d'utilité...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la notion", "la notion", "la notion", "la notion", ", <PERSON><PERSON><PERSON>", "la notion"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 82, "end": 84}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_084", "text": "<PERSON><PERSON>, <PERSON>, tu nous fais ça ? <PERSON><PERSON>.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 8, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", <PERSON>"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 83, "end": 85}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_085", "text": "Donc heu... eh bah eux, je pense que y'a plein de gens qui estiment avoir besoin de 48 vêtements neufs par an. <PERSON><PERSON>, hein", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 24, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["48 vêtements", "48 vêtements neufs"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 84, "end": 86}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_086", "text": "Je reste toujours sur mon truc en disant : nous, on va donner un avis en 2023 d'une entreprise en 2050 et finalement, heu... on n'a pas su en 2050 regarder tout ce qui s'est passé les 30 dernières années. On continue à fabriquer la même chose, voire pires, des choses pires que ce qui existait y'a 30 ans.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 59, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["tout ce", "tout ce", "une entreprise", "tout ce", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "des choses", "des choses", "une entreprise", "une entreprise", "tout ce", "les 30 dernières années", "une entreprise", "mon truc", "un avis", "une entreprise", "les 30 dernières années", "la même chose", "des choses pires", "30 ans", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "des choses", "tout ce", "les 30 dernières années", "les 30 dernières années", "30 ans", "30 ans", "30 ans", "tout ce", "les 30 dernières années", "Une entreprise", "mon truc", "une entreprise", "des choses", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "des choses", "une entreprise", "tout ce", "tout ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 85, "end": 87}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_087", "text": "Donc en fait, on n'est pas du tout dans cet... dans cette orientation-là de se dire « il faut faire un petit gaffe à ce qu'on fait et ce qu'on... et comment on le fait et à qui on le vend et comment on le vend ». Donc une entreprise légitime en 2050, elle aurait appris des 30 dernières années et mis en œuvre des changements à la lueur des 30 dernières... des 30 dernières années. C'est ça. Mais c'est très utopique, hein, vu que... vu qu'on n'arrive pas à le faire aujourd'hui, heu... peut-être qu'on arrivera à le faire dans 30 ans. Et heu... moi, j'ai un autre truc heu ... Attends, je vais le marquer.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 117, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.34188034188034194}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Donc une entreprise", "des changements", "donc en fait", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "donc en fait", "en fait", "le fait", "une entreprise", "C'est ça", "... <PERSON><PERSON>", ", j'", "Une entreprise légitime", "une entreprise légitime", "des 30 dernières années", "une entreprise", "un petit gaffe", "une entreprise légitime", "des 30 dernières années", "la lueur", "des 30 dernières années", "C'est ça", "30 ans", "Et heu... moi", "un autre truc heu", "la lueur", "30 ans", "30 ans", "30 ans", "C'est ça", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "C'est ça", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "donc en fait", "une entreprise", "en fait", "donc en fait", "Et ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 86, "end": 92}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_088", "text": "<PERSON><PERSON>, oui, vas-y, vas-y. Elle aurait donc... comment tu le dirais ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON>ui, oui, vas", ", vas"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 87, "end": 89}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_089", "text": "Et donc du coup, elle est légitime parce que justement, elle prend en compte ce qui a évolué ou ce qui n'a pas évolué et elle le prend en compte pour pouvoir heu... orienter son... son organisation. De ce qui a donc évolué ou non dans les 30 ans antérieurs.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["son organisation", "30 ans", "de ce", "donc du coup", "son... son organisation", "les 30 ans antérieurs", "les 30 ans antérieurs", "dans les 30 ans", "les 30 ans antérieurs", "son organisation", "30 ans", "30 ans", "Et donc du coup", "30 ans", "du coup", "Et donc du coup", "du coup", "donc du coup"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 88, "end": 90}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_090", "text": "En fait, elle... elle en est consciente sur ce qu'elle va créer les 30 prochaines années en fonction de ce qu'elle a vu les 30 dernières années. En fait, c'est heu...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 31, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["en fait", "les 30 dernières années", "les 30 dernières années", "de ce", "les 30 dernières années", "les 30 prochaines années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 89, "end": 91}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_091", "text": "Et soutenable ? Don<PERSON> mais ça, il faudra défen... déterminer ce qu'est un futur désirable, parce que tu vois, en fonction de ton futur désirable et du mien, c'est pas le même.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 0.6}, "noun_phrases": ["du mien", "<PERSON><PERSON>a"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 90, "end": 92}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_092", "text": "Elle est consciente de cela, premièrement. Deuxièmement, elle se projette dans les 30 ans futurs pour structurer son organisation et ses activités. J'ai bon, là ? Oui. Pour... À la lueur de ses leçons ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["son organisation", "ses activités", "ses activités", "ses activités", "ses activités", "ses activités", "ses activités", "la lueur", "30 ans", "de ce", "dans les 30 ans", "les 30 ans futurs", "son organisation", "ses activités", "... À la lueur", "ses leçons", "la lueur", "30 ans", "30 ans", "30 ans", "ses activités"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 91, "end": 96}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_093", "text": "À la lueur de ses constatations. Pour fabriquer, dans un but de fabriquer quelque chose de soutenable et durable. D'accord.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose", "la lueur", "quel<PERSON> chose", "la lueur", "ses constatations", "un but", "quelque chose de soutenable et durable", "quel<PERSON> chose", "quel<PERSON> chose", "quel<PERSON> chose"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 92, "end": 95}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_094", "text": "Alors donc pour, trois... Je partage complètement. Et redis ce que tu viens de dire ? Pour fabriquer un futur désirable et... et heu... et soutenable. Est", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["Et redis ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 93, "end": 98}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_095", "text": "-ce qu'on peut juste garder « soutenable » ? Parce que désirable, tu vois, c'est subjectif.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 94, "end": 96}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_096", "text": "Et soutenable, c'est la finalité du truc. Voilà.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 8, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Et soutenable, c'est la finalité", "la fin", "la fin"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 95, "end": 97}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_097", "text": "Enfin travailler dans une entreprise heu... qui fait heu... qui peut faire beaucoup beaucoup d'argent et qui exploite des données heu... des heu... des matières premières heu... limitées, elle fait beaucoup beaucoup d'argent, c'est un futur désirable pour les gens qui sont à l'intérieur de l'organisation, mais qui n'est pas soutenable. En fonction de ce qui s'est passé les 30 dernières années, on peut ensemble déterminer un futur qui est désirable, ensemble. Je reviens sur la coopération et sur l'inclusion des parties prenantes, etc.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 84, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["individuel_collectif", "court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "une entreprise", "les gens", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "Les gens", "une entreprise", "une entreprise", "les gens", "les gens", "une entreprise", "matières premières", "matières premières", "les 30 dernières années", "des matières", "le fait", "une entreprise", "matières premières", "<PERSON> matière<PERSON>", "les gens", "les gens", "une entreprise", "les 30 dernières années", "de ce", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "les 30 dernières années", "une entreprise heu", "des données heu", "... des heu", "des matières premières", "les gens", "les 30 dernières années", ", ensemble", "la coopération", "sur l'inclusion", "parties prenantes", "les gens", "matières premières", "les gens", "les gens", "les 30 dernières années", "des matières premières", "parties prenantes", "les gens", "matières premières", "matières premières", "matières premières", "Une entreprise", "les gens", "les gens", "des parties prenantes", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "les gens"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 96, "end": 99}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_098", "text": "Et... et je veux dire... enfin je voulais dire ça et je voulais dire aussi autre chose, c'est que en fait, heu... on parle d'innovation, on peut... on dit heu... on parle d'adaptation, on parle de changement, on parle de plein de choses. Le jour où il n'y aura plus de... de matières premières qui sont nécessaires à... au confort que l'on a aujourd'hui, pour fabriquer des téléphones, des ordinateurs, des enregistreurs, heu... des bâtiments béton", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 76, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["autre chose", "c'est que", "matières premières", "matières premières", "en fait", "des téléphones", "matières premières", "autre chose", "Le jour", "des téléphones", "des ordinateurs", ", des enregistreurs", "des bâtiments b<PERSON>ton", "matières premières", "autre chose", "autre chose", "matières premières", "matières premières", "matières premières", "autre chose", "autre chose", "autre chose", "des bâtiments", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 97, "end": 99}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_099", "text": "Donc en fait, y'a... ça, c'est des désirs, heu... c'est des choses qui n'existaient pas à un moment, qui ont été... c'est des besoins qui sont devenus aujourd'hui complètement indispensables.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un moment", "donc en fait", "des choses", "des choses", "donc en fait", "en fait", "des choses", "heu... c'est des choses", "un moment", "des besoins", "des besoins", "un moment", "un moment", "C'est des choses", "des choses", "un moment", "un moment", "un moment", "des choses", "un moment", "donc en fait", "un moment", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 98, "end": 100}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_100", "text": "Mais je veux dire, tous les outils qui vont autour, heu... enfin y'a ça, la voiture, la voiture électrique, la voiture hybride, heu... les transports en commun, l'avion, enfin j'en sais rien, y'a plein plein de choses. On a besoin de la voiture, mais est-ce qu'on a besoin d'une voiture qui roule à 300 kilomètres-heure ? Et qui pollue ?", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 60, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la voiture électrique", "la voiture hybride", "les transports", ", l'avion", "la voiture", "une voiture", "300 kilomètres", "une voiture", "une voiture", "la voiture", "les outils", "les outils", "la voiture", "une voiture", "une voiture"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 99, "end": 102}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_101", "text": "Et donc ce que je veux dire, juste pour aller plus loin, c'est que en fait, on va... ce confort-là, que l'on... qui sont... qui sont issus d'un modèle heu... économique heu... capitalistique, arrivent à... nous donnent accès à un certain confort et ce confort, heu... il est fort à parier que on n'arrive pas à descendre de ce confort-là. Et que en fait, ce confort ne va même faire que augmenter.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 72, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["c'est que", "en fait", "de ce", "ce confort", "donc ce", "ce confort", "un modèle heu", "un certain confort", "ce confort", "ce confort", "ce confort", "ce confort", "ce confort", "un certain", "en fait", "Et ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 100, "end": 102}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_102", "text": "Et donc du coup, toutes les questions de matières premières, de choses comme ça, va toujours se poser et on trouvera des alternatives, parce qu'en fait, on n'arrive pas à baisser le confort. Donc qu'est-ce que une organisation légitime en 2050 ?", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["comme ça", "une organisation", "matières premières", "matières premières", "en fait", "matières premières", "donc du coup", "une organisation", "une organisation légitime", "le confort", "le confort", "Et donc du coup", "toutes les questions", "matières premières", "des alternatives", "le confort", "le confort", "comme ça", "une organisation", "matières premières", "matières premières", "matières premières", "une organisation", "une organisation", "du coup", "Et donc du coup", "du coup", "donc du coup", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 101, "end": 103}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_103", "text": "Alors puisqu'on arrive à la fin, je vous dis comment je l'ai noté et on peut peut-être mettre la... l'exemple de <PERSON><PERSON>, <PERSON><PERSON><PERSON> « rouge carré 1 », et moi <PERSON> « rouge carré 2 », donc j'ai noté : saura... alors une boîte légitime en 2050 saura produire pour servir le confort des gens avec raison et avec des alternatives vertueuses pour le sociétal et environnemental. J'ai marqué : le juste confort.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 73, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["Et moi", "Et moi", "une boîte", "une boîte", "Et moi", "une boîte", "des gens", "une boîte", "une boîte", "des gens", "une boîte", "le confort", "le confort", "des alternatives", "le confort", "des gens", "la fin", "la... l'exemple", "alors une boîte légitime", "le confort", "avec des alternatives", "le sociétal", "le juste confort", ", <PERSON><PERSON><PERSON>", "une boîte légitime", "des gens", "des gens", "une boîte", "une boîte", "la fin"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 102, "end": 104}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_104", "text": "<PERSON><PERSON>, aujourd'hui, on est dans un confort qui est créé. <PERSON><PERSON>, tout à fait.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un confort"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 103, "end": 105}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_105", "text": "Je veux dire, si heu... si y'a 30 ans on n'avait pas inventé le téléphone portable, heu... on... on vivrait encore, enfin je veux dire, heu... on n'aurait pas de problème. On serait pas morts.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le téléphone", "le téléphone", "30 ans", "30 ans", "téléphone portable", "30 ans", "30 ans", "le téléphone portable", "de problème"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 104, "end": 106}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_106", "text": "Donc en fait, c'est du confort créé. C'est la notion de décroissance privative, alors, que il faudrait introduire ? Non, non, non. Ou est-ce qu'on l'introduit ou pas dans ce... ? Une notion... une notion heu... de... avant de parler de décroissance, y'a quand même une croissance, en fait.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 1, "tension_strength": 0, "total_indicators": 2}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance"], "conceptual_complexity": 0.6}, "noun_phrases": ["la notion", "donc en fait", "... une notion", "donc en fait", "en fait", "la notion", "la notion", "la notion", "Donc en fait, c'est du confort", "décroissance privative", "Une notion", "une croissance", "la notion", ", en fait", ", en fait", "donc en fait", ", en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 105, "end": 111}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_107", "text": "Donc en fait, cette croissance a été générée par un... un certain nombre d'objets, d'outils, de... de bâtiments, enfin de plein de choses heu... qui ont été heu... créés sur une... une volonté justement d'augmenter un confort. Après, on est... Bah peut-être que... pour moi, si tu veux, y'a eu... pour créer du confort superflu. Par exemple, quand on a utilisé le béton massivement heu... dans les années 1950, 1960 et 1970, c'était parce que y'avait bien nécessité que les gens soient logés, quoi. Et quand on a fait des barres en béton, c'était parce que ça permettait de faire des logements en très grand nombre à... très rapidement, avec un coût tout à fait acceptable. Parce que sinon, on pouvait juste pas loger les gens, quoi. C'est-à-dire que dans les années 1950 et début des années 1960, autour de Paris, je rappelle qu'y'avait toujours des bidonvilles, hein, avec des gens qui vivaient sur la terre battue gorgée d'eau, avec vaguement une tole au-dessus de la tête.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 167, "thematic_indicators": {"performance_density": 0.5988023952095809, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {"croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "court_terme_long_terme": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["croissance_decroissance", "court_terme_long_terme"], "conceptual_complexity": 0.718562874251497}, "noun_phrases": ["les gens", "pour moi", "donc en fait", "Les gens", "les gens", "les gens", "donc en fait", "en fait", "des gens", "les années", "les gens", "les gens", "des gens", "les gens", "les gens", "des gens", "cette croissance", "un certain nombre", ", d'outils", ", de... de bâtiments", "choses heu", "une... une volonté", "un confort", "confort superflu", "le b<PERSON><PERSON>", "massivement heu", "les années", "les gens", "des barres", "des logements", "très grand nombre", "un coût tout à fait acceptable", "les gens", "les années", "des bidonvilles", "hein, avec des gens", "la terre", "vaguement une tole", "la tête", "les années", "les années", "un certain nombre", "les gens", "la tête", "parce que ça", "les gens", "les gens", "des gens", "des gens", "donc en fait", "un certain", "en fait", "donc en fait", "les gens"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 106, "end": 112}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_108", "text": "<PERSON><PERSON> quand on a logé ces gens-là... En 2023, ça existe encore à Lyon. Et ça... peut-être, mais c'est tout à fait marginal, quand m<PERSON><PERSON>, <PERSON>, par rapport à ce que c'était. Ça existe autour de Lyon, oui, c'est vrai. J'ai vu. <PERSON><PERSON>, non, mais c'est pas... c'est pas si marginal que ça.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["et ça", ", <PERSON>", "et ça", "ces gens", "quand m<PERSON><PERSON>, <PERSON>"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 107, "end": 112}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_109", "text": "Aujourd'hui, ce que je veux dire, c'est que... Donc en fait, on a pu aussi utiliser des... des moyens pas forcément prospectivement bons pour la nature, parce que y'a rien de plus chimique que le béton, pour des raisons aussi sociétales. Mai<PERSON> ç<PERSON>, c'est... c'est un très très bon exemple. C'est un très très bon exemple. Dans les années 1970, y'avait heu... moins de gens à loger qu'aujourd'hui.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 68, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la nature", "donc en fait", "c'est que", "la nature", "donc en fait", "en fait", "la nature", "... <PERSON><PERSON>", "les années", "le b<PERSON><PERSON>", "les années", "les années", "des... des moyens pas forcément prospectivement bons pour la nature", "rien de plus chimique", "des raisons aussi sociétales", "<PERSON><PERSON>a", "les années", "les années", "<PERSON>", "... des moyens", "des moyens", "la nature", "la nature", "donc en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 108, "end": 112}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_110", "text": "Et aujourd'hui, on... on s'attelle méthodiquement à détruire tout ce qu'on a construit en béton dans les années 1970, que ce soit autour de Lyon, enfin moi j'ai plein plein d'exemples à Vénissieux, à Vaulx-en-Velin ou... où y'a pas... y'a pas trois mois sans qu'on fasse tomber une tour, parce qu'en fait, elle est une passoire énergétique, etc. C'est ça. Quand je parle de désir heu... soutenable et désirable, c'est de fabriquer des... peut-être des bâtiments de manière différente. Aujourd'hui, on est dans une région où y'a énormément d'eau et de... et de bois. Pourquoi on... on n'utilise pas cette matière-là, en fait ? C'est des matières qui sont locales et renouvelables.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 112, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.5357142857142857}, "noun_phrases": ["tout ce", "tout ce", "tout ce", "en fait", "tout ce", "des matières", "C'est ça", "les années", "<PERSON> matière<PERSON>", "C'est ça", "tout ce", "les années", "les années", "les années", "tout ce", "les années", "enfin moi", "trois mois", "une tour", "C'est ça", "d<PERSON><PERSON> heu", "des... peut-être des bâtiments", "manière différente", "une région", "de bois", "cette matière", "C'est des matières", ", en fait", ", en fait", "des bâtiments", "C'est ça", ", en fait", "tout ce", "tout ce", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 109, "end": 115}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_111", "text": "Alors y'a... bah justement, si tu veux... bah justement, moi qui travaille avec ces entreprises-là, ce que je peux dire, c'est qu'aujourd'hui, les normes de construction obligent à l'utilisation d'un minimum de... de matériaux bois, et ça génère de l'innovation, d'ailleurs. Premièrement. Ça, c'est réglementaire. La RT 2015, 2020 et tout, elle oblige aussi ça. Ce que tu viens de dire, c'est servi par la réglementation.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["et ça", "la réglementation", "la réglementation", "et ça", "ces entreprises", "les normes", "un minimum", "... de mat<PERSON><PERSON><PERSON> bois", "La RT", "la réglementation"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 110, "end": 115}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_112", "text": "Donc en fait, c'est... en fait, c'est un peu ce que je disais, c'est : une... une entreprise légitime en 2050, c'est une entreprise qui a su vendre en fait toutes les erreurs, toutes les évolutions et toutes les erreurs que l'organ... que la société a pu faire, le dévoiement qu'il a pu y avoir les 30 dernières années. Je... je vais plus loin que ça.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 66, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "la société", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "la société", "donc en fait", "Une entreprise", "une entreprise", "la société", "une entreprise", "la société", "une entreprise", "donc en fait", "en fait", "les 30 dernières années", "une entreprise", "Une entreprise légitime", "une entreprise légitime", "une entreprise", "les 30 dernières années", "une entreprise légitime", "les évolutions", "les 30 dernières années", "les évolutions", "les 30 dernières années", "les 30 dernières années", "la société", "les 30 dernières années", "les 30 dernières années", "toutes les erreurs", "toutes les évolutions", "toutes les erreurs", "la société", "le dévoiement", "les 30 dernières années", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "donc en fait", "une entreprise", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 111, "end": 113}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_113", "text": "-ville entiers où on détruit quand même des bâtiments qui seraient totalement rénovables, ce n'est qu'un choix politique, économique pour quelques-uns. <PERSON><PERSON>, mais qui va payer la rénovation, <PERSON><PERSON><PERSON> ?", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["-ville entiers", "la rénovation", ", <PERSON><PERSON><PERSON>", "des bâtiments"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 112, "end": 114}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_114", "text": "Non, là, je suis sur une projection 2050. On l'imagine en se disant : plutôt que de sans arrêt détruire pour refaire quelque chose d'autre avec encore des matières premières, pourquoi on réutilise pas des matières premières pour construire différemment ? D'accord, de sorte à heu... oui, je note l'exemple. À partir de maintenant, on va vous accorder une pause de cinq minutes. Voilà. On a des flux migratoires, c'est peut-être la chance d'avoir une opportunité d'avoir des nouvelles compétences sur nos territoires, aussi.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 84, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["cinq minutes", "cinq minutes", "cinq minutes", "quel<PERSON> chose", "cinq minutes", "quel<PERSON> chose", "matières premières", "matières premières", "des matières", "une projection", "matières premières", "<PERSON> matière<PERSON>", "quel<PERSON> chose", "quel<PERSON> chose", "des matières premières", "matières premières", "quel<PERSON> chose", "encore des matières premières", "des matières premières", ", de sorte", "une pause", "cinq minutes", "des flux migratoires", "une opportunité", "des nouvelles compétences", "nos territoires", "matières premières", "matières premières", "matières premières", "quel<PERSON> chose", "quel<PERSON> chose"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 113, "end": 119}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_115", "text": "Donc voitures ? Oui, voitures, bâtiments, y'a plein d'exemples.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 9, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Donc voitures", "O<PERSON>, voitures", ", bâtiments"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 114, "end": 116}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_116", "text": "Ah donc on arrive à ce que disait Samuel. Je suis toujours dans la prospective. Mais oui, pareil. Je suis pas aujourd'hui, je suis dans la prospective.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la prospective"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 115, "end": 119}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_117", "text": "Donc en fait, on dit ce que <PERSON> di<PERSON>, c'est-à-dire que ce qu'on vient de dire là et ce que je viens de noter, c'est en gros une entreprise légitime en 2050, elle est quasiment heu... elle est quasiment 90 à 100 % économie circulaire dans ses activités.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 48, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "donc en fait", "Une entreprise", "une entreprise", "une entreprise", "ses activités", "une entreprise", "donc en fait", "en fait", "une entreprise", "ses activités", "ses activités", "ses activités", "ses activités", "ses activités", "Une entreprise légitime", "une entreprise légitime", "une entreprise", "une entreprise légitime", "ses activités", "que <PERSON>", "ses activités", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "donc en fait", "une entreprise", "en fait", "donc en fait", "Et ce"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 116, "end": 118}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_118", "text": "Heu... une entreprise qui pratique au maximum l'économie circulaire, ça vous va, ça ? Oui. D'accord. Parce que l'exemple du bâtiment, c'est quand même Al<PERSON> je dirais : l'économie circulaire, la revalorisation, le réemploi, tous ces genres de choses, quoi. Oui. D'accord.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "l'économie circulaire", "l'économie circulaire", "Heu... une entreprise", "l'économie circulaire", "l'économie circulaire", "la revalorisation", "le réemploi", "tous ces genres", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 117, "end": 123}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_119", "text": "Enfin et puis en effet, enfin l'exemple heu... de détruire pour heu... détruire un bâtiment pour reconstruire un autre, heu... énergétiquement plus viable, ou qui aille plus dans le sens de... d'utiliser le moins d'énergies, ça coûte de l'argent. Mais ça coûte beaucoup moins d'argent que de heu... refabriquer un bâtiment de... depuis le début et... en... en termes de ressources économiques, de ressources heu... minérales, de ressources naturelles.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 69, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["le sens", "exemple heu", "<PERSON><PERSON>a", "un bâtiment", "le sens", "que de heu", "un bâtiment", "... depuis le début", "ressources économiques", "exemple heu", "de ressources naturelles"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 118, "end": 120}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_120", "text": "C'est pas mal, ça, finalement. Je me permets de... une capture.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["pas mal"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 119, "end": 121}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_121", "text": "Il faut pas que tu photographies ça, <PERSON><PERSON>, parce qu'en fait, on a réutilisé comme cartes, donc en fait, c'est un mauvais exemple. Ah oui, pardon.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["donc en fait", "donc en fait", "en fait", "fait, c'est un mauvais exemple", "donc en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 120, "end": 122}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_122", "text": "Je vous prie de m'en excuser. <PERSON><PERSON> il restera Jean<PERSON>, « un rouge carré » et il restera Samuel, « trois rouge carré ». Alors nous allons repartir sur la suite de l'atelier. Là, c'est bon, j'ai lancé l'enregistrement.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", j'", "un rouge carré", "la suite", "la suite", "la suite"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 121, "end": 125}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_123", "text": "Donc sur la performance. Donc on va... on va relancer le... on va faire la suite de l'atelier.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la performance", "La performance", "la performance", "la suite", "<PERSON><PERSON> sur la performance", "la suite", "la performance", "La performance", "la suite"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 122, "end": 124}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_124", "text": "Donc l'atelier va concerner la deuxième partie du qu'on vous a distribué. <PERSON><PERSON>, maintenant, on va se concentrer, on a parlé de la légitimité, on va se concentrer sur la performance.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 32, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la légitimité", "la performance", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "la légitimité", "La performance", "la performance", "la légitimité", "la légitimité", "la deuxième partie", "la légitimité", "la performance", "La performance", "la légitimité"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 123, "end": 125}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_125", "text": "Don<PERSON> même principe, 2050 avec les conditions de 2050, performance en 2050 et, de la même manière que tout à l'heure, on vous a prévu des cartes à ne consulter heu... bah je vous dirai à la moitié du temps que vous pouvez commencer à les consulter, en fait.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 49, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["en fait", "<PERSON><PERSON> même principe", "les conditions", ", de la même manière", "des cartes", "la moitié", ", en fait", "la moitié", ", en fait", "la moitié", "la même manière", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 124, "end": 126}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_126", "text": "Donc si jamais... si jamais, au bout d'un quart d'heure, vous avez panne d'idée, vous pouvez utiliser les cartes, je vous le dirai à ce moment-là « on est à la moitié du temps », on... voilà. Ça va pour tout le monde ? Eh bien c'est parti, on va se donner heu... une demi-heure aussi.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un quart", "au bout", "heu... une demi-heure", "une demi-heure", "tout le monde", "une demi-heure", "tout le monde", "les cartes", "la moitié", "un quart", "les cartes", "ce moment", "la moitié", "tout le monde", "la moitié", "les cartes", "tout le monde", "tout le monde"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 125, "end": 128}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_127", "text": "Et je vais lancer, donc là, heu... « matin, atelier trois, performance collective ». Alors, performance collective, hein, on est d'accord ?", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 22, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Al<PERSON>, performance collective"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 126, "end": 128}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_128", "text": "On est vraiment sur la performance de l'organisation. L'organisation, comment elle doit être pour pouvoir s'adapter à tous ces besoins qui sont ici. Effectivement, c'est le... quand je vois que... moins de matières premières, le coût des matières premières, les transports difficiles, une main d'œuvre qui arrive, c'est sa capacité... alors je l'avais en tête, le mot, en plus, ça doit être l'âge, j'ai pas la tête. C'est sa... c'est sa performance, effectivement, à se dire... à optimiser les ressources. La ressource, elle peut être humaine, elle peut être de matière première, elle peut être d'énergie, c'est sa performance à optimiser les ressources. Peut-être qu'au départ, c'est utiliser heu... choisir ses ressources et à les optimiser. Choisir les ressources et optimiser les ressources, parce qu'il faut faire des choix avant d'optimiser.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 131, "thematic_indicators": {"performance_density": 0.7633587786259541, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.3053435114503817}, "noun_phrases": ["la performance", "La performance", "la performance", "le mot", "matières premières", "matières premières", "des matières", "le mot", "... <PERSON><PERSON>", ", j'", "matières premières", "<PERSON> matière<PERSON>", "des matières premières", "les transports", "matières premières", "la tête", "des matières premières", "la performance", "tous ces besoins", "matières premières", "le coût", "matières premières", ", les transports difficiles, une main", "le mot", "la tête", "les ressources", "La ressource", "matière première", "les ressources", "ses ressources", "les ressources", "les ressources", "des choix", "matières premières", "les ressources", "les ressources", "les ressources", "La performance", "sa capacité"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 127, "end": 134}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_129", "text": "Enfin il faut savoir quelles ressources... Alors <PERSON>a, c'est la stratégie.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 11, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les ressources", "les ressources", "les ressources", "les ressources", "quelles ressources", "Alors ça, c'est la stratégie", "les ressources", "les ressources", "les ressources"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 128, "end": 130}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_130", "text": "Temps. Temps.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 2, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 129, "end": 131}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_131", "text": "Heu... finances. <PERSON><PERSON> on met la finance qui existe, oui. <PERSON><PERSON> moi, j'ai envie de rajouter... Mais c'est une ressource. <PERSON><PERSON>, j'ai envie de rajouter heu ... finances non euros", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["alors moi", ", j'", "la fin", "la finance", "<PERSON><PERSON> moi", "... finances non euros", "une ressource", "la finance", "la fin"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 130, "end": 136}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_132", "text": ", c'est-à-dire heu... donc par exemple, pour moi, dedans, on met la mutualisation. Alors oui, alors les ressources, alors à la limite, si je rajoute pas mon truc à moi...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 30, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["pour moi", "la limite", "mon truc", "les ressources", "les ressources", "les ressources", "les ressources", "les ressources", "la mutualisation", ", alors à la limite", "mon truc", "la mutualisation", "La mutualisation", "les ressources", "la mutualisation", "la mutualisation", "les ressources"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 131, "end": 133}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_133", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, c'est par exemple signer des accords, des machins, des trucs. J'allais pas si loin. Et après, apr<PERSON>, une fois que t'as une fédération, après tu... tu cherches... Non, non, mais j'étais pas dans... alors c'est la capacité que les gens soient capables de se mettre autour d'une table, sur quelque chose de commun.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["quel<PERSON> chose", "les gens", "Les gens", "La capa", "quel<PERSON> chose", "les gens", "la capacité", "les gens", "les gens", "les gens", "quel<PERSON> chose", "quel<PERSON> chose", "les gens", "les gens", "les gens", "les gens", "la capacité", "quel<PERSON> chose", "les gens", "les gens", "une table", "des accords", "des trucs", "une fois", "une fédération", "les gens", "une table", "quel<PERSON> chose", "quel<PERSON> chose", "les gens"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 132, "end": 136}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_134", "text": "C'est se fédérer autour d'un projet. Et après, de se dire « on va mutualiser ». Non, pas du tout. Pas du tout.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un projet", "Non, pas du tout"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 133, "end": 137}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_135", "text": "<PERSON>ur moi, on peut mutualiser n'importe quoi à n'importe quel niveau, sans forcément avoir heu... ouvert des négociations. <PERSON><PERSON>, je suis très très très pratico-pratique dans la mutualisation. Si par exemple heu... Non, mais c'est... c'est une... une manière...", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["pour moi", "exemple heu", "la mutualisation", "n'importe quel niveau", "la mutualisation", "c'est... c'est une... une manière", "La mutualisation", "Une manière", "la mutualisation", "la mutualisation", "exemple heu"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 134, "end": 138}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_136", "text": "La mutualisation des transports, imagine. Une manière d'y arriver, en fait, c'est ça.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["en fait", "C'est ça", "des transports", "C'est ça", "C'est ça", ", en fait", "la mutualisation", "la mutualisation", "La mutualisation", "Une manière", "la mutualisation", ", en fait", "la mutualisation", "C'est ça", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 135, "end": 137}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_137", "text": "<PERSON><PERSON>, c'est une manière d'y arriver. C'est un moyen. <PERSON><PERSON>, c'est un moyen.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Une manière"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 136, "end": 139}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_138", "text": "Donc en fait, bah du coup, on peut... c'est optimiser les ressources avec heu... donc tout ce qu'on a mis et heu... et les moyens pour heu... optimiser ces ressources, eh bah elles sont diverses. Y'a la mutualisation, alors à travers une fédération ou pas de fédération, c'est pas la question, en fait, finalement.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 0.6}, "noun_phrases": ["tout ce", "tout ce", "tout ce", "donc en fait", "donc en fait", "en fait", "tout ce", "la question", "tout ce", "la question", "tout ce", ", en fait", "les ressources", "les ressources", "les ressources", "les ressources", "les ressources", "la mutualisation", "une fédération", "la mutualisation", "La mutualisation", "bah du coup", "les ressources", "donc tout ce", "ces ressources", "la mutualisation", "alors à travers une fédération", "pas de fédération", ", en fait", "la mutualisation", "les ressources", "du coup", "du coup", "donc en fait", ", en fait", "tout ce", "tout ce", "en fait", "donc en fait", "les moyens"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 137, "end": 139}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_139", "text": ", une partie du temps chez l'autre. <PERSON><PERSON>, bah ça, c'est de la mutualisation de moyens aussi, enfin si heu... mettre en commun des véhicules, mettre en commun heu... des... des locaux, j'en sais rien.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 0, "side_b": 2, "tension_strength": 2, "total_indicators": 2}, "individuel_collectif": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage", "individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["une partie", ", j'", "la mutualisation", "la mutualisation", "La mutualisation", "la mutualisation", "la mutualisation", "une partie", ", une partie", "des... des locaux"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 138, "end": 140}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_140", "text": "OK, donc optimiser. Utiliser des moyens adaptés, par exemple, mutualiser les outils de production, locaux, c'est une autre manière d'économie heu... une économie mutualisée et circulaire.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une économie", "<PERSON>", "des moyens", "les outils", "économie heu", "... une économie", "une économie mutualisée", "une économie", "des moyens adaptés", "les outils"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 139, "end": 141}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_141", "text": "On peut les recommercialiser, etc., c'est... ça s'oppose vraiment à une économie linéaire. Et tout ça crée de la richesse.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["une économie", "tout ça", "une économie", "une économie linéaire", "Une économie linéaire", "les recommercialiser", "une économie linéaire", "la richesse"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 140, "end": 142}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_142", "text": "Donc j'ai marqué, donc heu... alors, « capacité à optimiser les ressources, main d'œuvre, énergies, matières, temps, rejets, déchets, finances, etc. », j'ai marqué « optimiser égale utiliser des moyens adaptés, exemple : mutualiser les outils de production, locaux, personnel, main d'œuvre, transports, donc inscription dans une économie circulaire que l'on organise comme circulaire et mutualisée, à l'inverse de linéaire, fabriquer-utiliser-jeter et en silos, chaque boîte dans son coin ». Ça va, ça ? Oui. Ça reflète ce qu'on vient de dire ? Oui, oui. Et après, d'autres critères de performance, c'est aussi peut-être des performances heu... qui sont pas forcément que financières, qui sont un bénéfice sociétal ou un bénéfice environnemental. La performance, elle sera environnementale, en fait.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 119, "thematic_indicators": {"performance_density": 0.8403361344537815, "legitimacy_density": 0.8403361344537815, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {"individuel_collectif": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": ["individuel_collectif"], "conceptual_complexity": 1.0}, "noun_phrases": ["la performance", "une économie", "son coin", "La performance", "la performance", "en fait", ", j'", "la performance", ", en fait", "les ressources", "les ressources", "les ressources", "les ressources", ", déchets", ", énergies", ", temps", ", rejets", ", déchets", "les ressources", "les ressources", ", en fait", "<PERSON>", "des moyens", "les outils", "une économie", "Une économie circulaire", "les ressources", ", énergies", ", matières", ", temps", ", rejets", ", déchets", "des moyens adaptés", "les outils", ", personnel", ", transports", "donc inscription", "une économie circulaire", "chaque bo<PERSON>te", "son coin", "La performance", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 141, "end": 148}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_143", "text": "Donc capacité à créer des bénéfices... À créer autre chose que de la performance économique et financière.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 0.0, "performance_indicators": 2, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la performance", "des bénéfices", "des bénéfices", "autre chose", "La performance", "la performance", "autre chose", "autre chose", "autre chose", "la performance", "autre chose", "autre chose", "La performance", "Donc capacité", "des bénéfices", "autre chose", "que de la performance économique et financière"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 142, "end": 144}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_144", "text": "Alors j'ai marqué « bénéfices », j'ai entendu « bénéfices », donc je remarque « performance ». J'en sais rien, replanter des arbres, c'est un bénéfice environnemental, par exemple. « Non financière ». Pas que financière. C'est pas de « non financière ».", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 1.0, "performance_indicators": 2, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": [", j'", "Pas que financière", "des arbres", "des arbres", "des arbres"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 143, "end": 149}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_145", "text": "Planter des arbres, il faut des gens qui puissent heu... enfin c'est... il faut réussir à trouver des gens heu... à payer... Restaurer la nature, par exemple ? À payer des gens pour... pour planter des arbres.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["la nature", "la nature", "la nature", "des gens", "gens heu", "des gens", "des gens", "des arbres", "des gens", "des gens heu", "la nature", "des gens", "des arbres", "des arbres", "la nature"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 144, "end": 147}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_146", "text": "Donc ça coûte, mais en fait, ça coûte au niveau... au niveau financier, donc c'est... c'est heu... et économique. Donc il faut pas l'enlever, c'est nécessaire.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["en fait", "au niveau", "au niveau", "niveau financier", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 145, "end": 148}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_147", "text": "Mais en fait, y'a... la performance Donc performance non financière, économique, ce serait par exemple restaurer la planète ? Restaurer la planète à l'échelle qu'on peut ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 27, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la performance", "La performance", "la performance", "en fait", "la planète", "la performance", "La performance", "Mais en fait, y'a... la performance Donc performance non financière, économique", "ce serait", "la planète", "la planète", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 146, "end": 148}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_148", "text": "-ce que donc là, on est en train de parler de juste limiter la casse ou est-ce que tu... ce que tu nous dis, c'est : et en plus, on va réparer ? C'est pas un but, enfin c'est les critères de performance, c'est sur quoi on... on regarde une...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "les critères", "les cri", "les critères", "un but", "Les critères", "la casse", "la casse", "les critères", "la casse", "les critères", "Les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 147, "end": 149}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_149", "text": "<PERSON><PERSON>, de performance. <PERSON> vois, par exemple, aujourd'hui, en 2023, on produit des factures équivalent carbone. On nous demande de produire des factures équivalent carbone.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des factures", "des factures équivalent carbone"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 148, "end": 151}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_150", "text": "L'idée, on est en 2023, OK, je... je dis n'importe quoi, pour 1000 euros, je produis 200 grammes de carbone, c'est vraiment du grand n'importe quoi, hein, on est en 2023, j'espère bien qu'en 2028, je serai plus qu'à 150 et ainsi de suite. Donc", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 45, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", OK", ", OK", ", j'", ", OK", "1000 euros", "200 grammes", "qu'à 150"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 149, "end": 151}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_151", "text": "Je le vois plutôt... enfin c'est comme ça que je le ressens. Alors, critères de performance, critères de performance dans ces conditions-là, qu'est-ce que... comment tu le dirais ? Elle est... ce critère de performance par rapport à ce que tu viens de dire, comment... ? Elle a... elle a un critère de performance environnemental positif. Positif, oui. Aujourd'hui, il est juste... il est juste pour essayer de... de rattraper...", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 70, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un critère", "comme ça", "un critère", "ce critère", "ces conditions", "ces conditions", "un critère", "ce critère", "comme ça", "Alors, critères", ", critères", "ces conditions", "un critère", "performance environnemental", "il est juste"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 150, "end": 156}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_152", "text": "Donc ton bilan environnemental, il est positif ? <PERSON><PERSON>, comme un peu des... enfin il existe des... des bâtiments à énergie positive, c'est-à-dire que ils dépensent... ils fabriquent plus d'énergies qu'ils en consomment. En fait, on peut... dans les critères de performance des entreprises, ce serait heu... de... d'avoir des performances environnementales plus... plus élevées que celles qu'elles utilisent.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 59, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["les critères", "les critères", "les critères", "les cri", "les critères", "en fait", "des entreprises", "des entreprises", "Les critères", "des entreprises", "ce serait", "ton bilan environnemental", "des bâtiments", "énergie positive", "les critères", "des performances environnementales", "les critères", "Les critères", "les critères", "Des entreprises", "les critères", "en fait", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 151, "end": 154}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_153", "text": "Donc elle aurait davantage heu... Qu'on soit plus en compensation carbone par des flux financiers, parce que c'est ce qui arrive aujourd'hui, en fait, hein.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["en fait", ", en fait", ", en fait", "Qu'on soit plus en compensation carbone par des flux financiers, parce que c'est ce qui arrive aujourd'hui, en fait, hein.", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 152, "end": 154}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_154", "text": "Bénéfices climat positifs donc heu... exemple heu... c'est pas juste qu'elle consomme moins. Ah non, ça c'est ce qu'on fait aujourd'hui.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 21, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["exemple heu", "Bénéfices climat positifs", "exemple heu"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 153, "end": 155}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_155", "text": "Il faut que la balance, elle change de camp, quoi, elle change de sens. C'est-à-dire qu'on... on vend plus une entreprise... c'est des critères économiques qu'il faut sûrement heu... changer, des critères heu... des critères de performance qu'il faut changer. C'est-à-dire que heu... quand tout... tout est basé sur la... la marchandisation et sur la... le capital, que ça vienne pas forcément d'une vision marxiste sur le travail, mais que ça vienne aussi sur l'environnemental, quoi.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 76, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "critères heu", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "des critères", "une entreprise", "une entreprise", "Une entreprise", "une vision", "une vision", "une entreprise", "Une entreprise", "la balance", "une entreprise", "des critères", "la... la marchandisation", "le capital", "une vision marxiste", "le travail", "une entreprise", "une entreprise", "Des critères", "une entreprise", "une entreprise", "des critères", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 154, "end": 157}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_156", "text": "<PERSON><PERSON>, j'ai marqué : donc critères de performance... Qu'on prenne en compte. Que ça devienne un élément extra-financier pondérant. O<PERSON>, oui, ça, c'est une possibilité, oui. <PERSON><PERSON>, j'essaie de le mettre avec notre image d'aujourd'hui, hein. <PERSON><PERSON>, c'est ça. <PERSON><PERSON>, oui.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 41, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est ça", "C'est ça", ", j'", "C'est ça", "C'est ça", "donc crit<PERSON>", "un élément extra-financier", "notre image", "<PERSON><PERSON>, c'est ça", "C'est ça", "<PERSON><PERSON>, c'est ça"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 155, "end": 162}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_157", "text": "Alors j'ai marqué donc... donc quels sont les critères de performance d'une boîte en 2050, qui a un bilan environnemental positif, elle a davantage heu... que limiter la casse climat, voire elle a produit du bénéfice climat positif, exemple : pas juste moins consommer de ressources naturelles humaines, pas juste équilibrer l'économique et l'environnemental, elle entre dans la restauration, la régénération environnementale. C'est ça ? Oui, oui. Sur heu... en fait, le... que le bilan heu... le bilan social et environnemental soit prépondérant, que son bilan économique et financier. Prépondérant, ça veut dire ? Plus élevé.", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 96, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 2.0, "performance_indicators": 1, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.4, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["une boîte", "une boîte", "le bilan social", "les critères", "les critères", "les critères", "les cri", "les critères", "une boîte", "en fait", "une boîte", "C'est ça", "une boîte", "une boîte", "C'est ça", "C'est ça", "Les critères", "la casse", "la casse", "les critères", "la casse", "les critères", "une boîte", "un bilan environnemental", "la casse climat", "bénéfice climat positif", "de ressources naturelles", "dans la restauration", "C'est ça", "le bilan heu", "le bilan social et environnemental", "son bilan", "Les critères", "les critères", "une boîte", "les critères", "en fait", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 156, "end": 162}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_158", "text": "Donc que son bilan social et environnemental soit plus élevé que son bilan économique et financier. En gros, que le critère heu... le critère de performance aujourd'hui qui n'est que économique et financier, heu... que demain, il... on puisse juger une entreprise à sa heu... à son... à son bilan sociétal, social et environnemental.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 54, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 2.0, "performance_indicators": 1, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.4, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 1.0}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "le critère", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "son bilan social et environnemental", "le critère heu", "une entreprise", "sa heu", "... à son... à son bilan", "son bilan", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 157, "end": 159}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_159", "text": "OK, qu'on puisse la juger sur son bilan sociétal et environnemental d'abord. D'accord.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["son bilan"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 158, "end": 160}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_160", "text": "Et si on parle d'humain, parce que c'est une performance, hein, der<PERSON><PERSON> les entreprises, y'a des hommes, ne jamais oublier, si on se projette en 2050 avec effectivement un marché de l'emploi qui va complètement... des nouveaux arrivants en France, donc on parle de migrants, des salariés qui ont peut-être d'autres attentes, donc voilà, c'est... comment on se projette pour l'entreprise en termes de RH, c'est une notion importante dans l'entreprise dont on n'a pas beaucoup parlé, là. Est", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 79, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["Une notion", "hein, der<PERSON><PERSON> les entreprises", "des hommes", "un marché", "des nouveaux arrivants", "des salariés", "d'autres attentes", "d'autres attentes", "des salariés", "les entreprises", "une performance"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 159, "end": 161}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_161", "text": "-ce que... en quoi c'est un critère de... enfin que... sous l'angle du critère de performance, comment tu le dirais ? C'est un critère de performance...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un critère", "un critère", "un critère", "un critère"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 160, "end": 162}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_162", "text": "<PERSON><PERSON>. <PERSON> id<PERSON>, c'est de dire : l'entreprise, elle se doit à un moment donné de s'adapter, de mettre en place des politiques RH qui fait que... <PERSON><PERSON>, mais c'est quoi, le critère de performance ? Eh bah c'est son adaptabilité à manager ses... ses nouveaux arrivants.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 47, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["un moment", "le critère", "un moment", "un moment", "un moment", "un moment", "Mon idée", "un moment", "Eh bah c'est son adaptabilité", "ses... ses nouveaux arrivants", "un moment", "un moment", "un moment"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 161, "end": 165}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_163", "text": "Tu parlais... tu parlais d'aujourd'hui, qu'est-ce qui est compliqué à gérer aujourd'hui dans les entreprises ? Tu en parlais tout à l'heure, c'est gérer les X, Y qui arrivent ou les... ou les Z, d'ailleurs, on peut parler de Z, maintenant, qui commencent à arriver sur le marché. Il faudrait pas que je sois trop vieillissant.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["les entreprises", "les X", "le marché"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 162, "end": 165}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_164", "text": "Donc c'est un critère... c'est un critère de performance, de dire : j'ai une nouvelle... j'ai une nouvelle donne au niveau des salariés. Si on peut... si tu veux, est-ce qu'on peut le résumer ainsi, heu... être heu... savoir produire de l'employabilité, quelle que soit la diversité des personnes ? On peut le mettre comme ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 56, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un critère", "comme ça", "un critère", "un critère", "au niveau", "au niveau", "comme ça", "un critère", "des salariés", "des salariés", "quelle que soit la diversité", "la diversité"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 163, "end": 166}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_165", "text": "Et là, on part vraiment du principe que l'État n'int... intervient peu, parce que finalement, ils interviennent peu, faut pas rêver. C'est toujours « démerdez-vous ». Quand on a... qu'on soit passés de ressources humaines à richesses humaines, si je vais plus loin. Y'a déjà plein d'entreprises qui utilisent ça.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 50, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["accumulation_partage"], "conceptual_complexity": 1.0}, "noun_phrases": ["ressources humaines", "vraiment du principe", "ressources humaines", "richesses humaines"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 164, "end": 168}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_166", "text": "<PERSON><PERSON>, je sais, mais je... il est galvaudé, mais là, vraiment, on est en projection, donc là je veux bien l'utiliser. Alors j'ai marqué savoir produire de l'employabilité quelle que soit la diversité des personnes qui ont besoin d'emploi.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 39, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["quelle que soit la diversité", "la diversité"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 165, "end": 167}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_167", "text": "<PERSON><PERSON>, je dois vous laisser, donc heu... je laisse heu... je laisse la suite de mon rôle de scribe. Merci beaucoup à tous les deux.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les deux", "la suite", "la suite", "la suite", "mon rôle", "tous les deux"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 166, "end": 168}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_168", "text": "<PERSON><PERSON>, qu'est-ce qu'on a d'autre en cartes, là ? Eh bah... pardon, je vais les mettre dans le bon sens.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 20, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le bon sens"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 167, "end": 169}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_169", "text": "<PERSON><PERSON>, je pense que dans un contexte en 20... je m'en éloigne un peu, parce que finalement, c'est ce qu'on retrouve aujourd'hui dans nos boîtes, hein, mais... Les inn... de quoi ? Ça, ces items-là. Ah oui, oui", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 38, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", c'est ce", ", c'est ce", "un contexte", "nos bo<PERSON>tes", ", ces items"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 168, "end": 171}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_170", "text": ", bien sûr, bah oui, oui, de toute façon, c'est... oui, c'est les critères de performance, donc heu... <PERSON><PERSON>, oui, actuels. Je reviens là-dessus, parce que ce qui est intéressant, c'est de se projeter avec les éléments qu'on nous donne. On parle de renforcement de sécurité niveau", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 47, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "les critères", "les cri", "toute façon", "les critères", "Les critères", "les critères", "les critères", "les éléments", "Les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 169, "end": 173}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_171", "text": "conflit aussi, donc heu... on s'adapte. Y'a peut-être une chose dont on commence à parler aujourd'hui, mais dont il faudrait forcément parler demain, c'est l'éthique, les critères d'éthique, qui sont un... aussi des piliers de la RSE, hein. <PERSON><PERSON>, oui, bien sûr.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 42, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "les critères", "les cri", "les critères", "Les critères", "les critères", "les critères", "une chose", "un... aussi des piliers", "la RSE", "Les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 170, "end": 173}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_172", "text": "Qui est celui qu'on galvaude, d'ailleurs. <PERSON><PERSON>, ce qui est heu... quasiment par essence dans les... les organisations de l'économie sociale et solidaire, en fait.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 25, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["l'économie sociale et solidaire", "l'économie sociale et solidaire", "de l'économie", "l'économie sociale et solidaire", "en fait", "les organisations", ", en fait", ", en fait", "les organisations", "Oui, ce", "quasiment par essence", "l'économie sociale et solidaire", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 171, "end": 173}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_173", "text": "Tu comprends mon... mon schéma ? <PERSON><PERSON>, bien sûr. J'essaie de voir comment on se projette en 2050. C'est quoi, l'éthique en 2050 ? Est -ce qu'elle est la même ou elle peut évoluer ? Eh bah du coup, est-ce que ça... Est-ce qu'elle est mesurable ? Et est-ce que c'est un critère de performance ? Aussi.", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 1.0, "performance_indicators": 1, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un critère", "un critère", "un critère", "bah du coup", "un critère", "mon... mon schéma", "C'est quoi, l'éthique", "du coup", "du coup"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 172, "end": 182}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_174", "text": "Ou est-ce que c'est heu... est-ce que c'est pas plutôt de la légitimité, en fait ? Enfin... on est un peu entre les deux, en fait. Je suis un peu entre les deux. Est -ce que... est-ce que une heu... une entreprise va pouvoir vendre son savoir-faire, heu... son employabilité, son... ce qu'on veut, par l'éthique ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 2.0, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la légitimité", "une entreprise", "la légitimité", "la légitimité", "une heu", "la légitimité", "une entreprise", "une entreprise", "une entreprise", "les deux", "Une entreprise", "la légitimité", "la légitimité", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "en fait", "la légitimité", "la légitimité", "une entreprise", "une entreprise", "Heu... une entreprise", "la légitimité", ", en fait", "Une entreprise", ", en fait", "une entreprise", "Une entreprise", "la légitimité", "une entreprise", "une entreprise", "Enfin... on est un peu entre les deux", "une heu", "une entreprise", "son savoir", "une entreprise", "une entreprise", "une entreprise", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 173, "end": 179}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_175", "text": "Non, par contre, si elle n'a aucune éthique, elle pourra pas vendre. Donc en fait, c'est un minimum...", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 18, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["donc en fait", "donc en fait", "en fait", "un minimum", "aucune éthique", "donc en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 174, "end": 176}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_176", "text": "enfin c'est un... un... C'est un minimum requis, c'est comme le financier, si on n'a pas l'argent, on n'existe pas. C'est un minimum requis, en fait.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 26, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["en fait", "un minimum", ", en fait", ", en fait", "le financier", ", en fait", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 175, "end": 177}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_177", "text": "Donc en fait, c'est... c'est heu... Mais tu vois la différence ?", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["donc en fait", "donc en fait", "en fait", "la différence", "donc en fait", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 176, "end": 178}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_178", "text": "Heu... donc ça va être : intégrer, intégrer heu... l'aspect... Les critères d'éthique.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "les critères", "les cri", "les critères", "Les critères", "les critères", "les critères", "... l'aspect", "Les critères", "les critères", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 177, "end": 179}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_179", "text": "Puisqu'à un moment donné, quand on crée des tensions sur les marchés, y'a certaines entreprises qui le font pour pouvoir revendre derrière. En fait, là, on... C'est le cas du pétrole, hein. Oui, oui, par exemple, oui.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 37, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["un moment", "en fait", "des tensions", "certaines entreprises", "un moment", "un moment", "un moment", "un moment", "un moment", "un moment", "Puisqu'à un moment", "des tensions", "les marchés", "certaines entreprises", "le cas", "un moment", "un moment", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 178, "end": 181}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_180", "text": "C'est l'exemple même de l'économie du pétrole, hein. C'est tout sauf éthique.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 12, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["de l'économie", "même de l'économie"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 179, "end": 181}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_181", "text": "Ce qu'on montre quand même ici, c'est que heu... bah en fait, là où aujourd'hui heu... tout est quand même pas mal basé... les critères de performance sont énormément basés sur le financier, heu... l'idée, c'est de sortir de ça et de... d'intégrer des nouveaux heu... critères de performance. Je suis d'accord.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 52, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["les critères", "les critères", "les critères", "les cri", "c'est que", "les critères", "en fait", "Les critères", "les critères", "les critères", "Les critères", "c'est que heu", "les critères", "le financier", "des nouveaux heu", "... crit<PERSON>", "les critères", "pas mal", "en fait", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 180, "end": 182}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_182", "text": "Qui ne soient pas que... que financiers et qui ne soient pas non plus les critères qui sont... bah là, sur les cartes, là, c'est assez clair, enfin y'a plus de... de cartes d'un aspect financier, plus que d'un aspect... enfin non économique, quoi, en tout cas, qui sont « risques et conformité, efficacité opérationnelle, innovation, satisfaction client », qui vont quand même dans l'orientation de heu... d'une performance de l'organisation, pas une performance de l'environnement. Je suis d'accord .", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 80, "thematic_indicators": {"performance_density": 3.0, "legitimacy_density": 0.0, "performance_indicators": 3, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.6, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 1.0}, "noun_phrases": ["tout cas", "les critères", "les critères", "les critères", "les cri", "les critères", "les cartes", "les cartes", "Les critères", "les critères", "les critères", "les cartes", ", innovation", "efficacité opérationnelle", "Les critères", "les critères", "... de cartes", "un aspect financier", "plus que d'un aspect... enfin non économique, quoi, en tout cas", "efficacité opérationnelle", ", innovation", "une performance", "pas une performance", "les critères", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 181, "end": 184}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_183", "text": "Donc est-ce que ça... est-ce que justement ça serait pas d'intégrer heu... des nouveaux critères de performance, mais qui soient heu... peut-être heu... à visée extérieure de l'organisation, plus que juste en interne, de montrer en quoi heu... l'organisation, elle a une... un impact positif sur son environnement ? Je suis convaincu.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 52, "thematic_indicators": {"performance_density": 1.0, "legitimacy_density": 0.0, "performance_indicators": 1, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.2, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["son environnement", "son environnement", "son environnement"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 182, "end": 184}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_184", "text": "Donc c'est... c'est ça, c'est ça qu'il faudrait heu... <PERSON><PERSON>, ces critères-là, c'est ce que je... j'appelle aujourd'hui l'extra-financier, qui existe au sens comptable du terme, la Banque de France le ramène, l'Europe aussi le ramène au titre des grands groupes, hein, voilà.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["C'est ça", "... <PERSON><PERSON>", "Moi, ce", "C'est ça", ", c'est ce", ", c'est ce", "C'est ça", "C'est ça", "ces critères", "Donc c'est... c'est ça", "ces critères", "sens comptable", ", la Banque", "grands groupes", "ces critères", "ces critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 183, "end": 185}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_185", "text": "Est -ce qu'on rogne sur l'aspect financier ou est-ce qu'on rogne sur l'aspect heu", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["l'aspect financier", "l'aspect heu"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 184, "end": 186}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_186", "text": "J'accepte de payer 105 parce que j'achète aussi la qualité. Donc l'entreprise qui veut le vendre...", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 16, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["la qualité", "Donc l'entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 185, "end": 187}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_187", "text": "OK. OK.", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 2, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": []}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 186, "end": 188}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_188", "text": "Et donc du coup, ce que tu dis... Ça , c'est la... c'est la réalité d'aujourd'hui. Ça commence à être la réalité d'aujourd'hui.", "features": {"temporal_context": "2023", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 23, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["donc du coup", "Et donc du coup", "du coup", "Et donc du coup", "être la réalité", "du coup", "donc du coup"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 187, "end": 191}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_189", "text": "<PERSON><PERSON>, et donc là, c'est... tu dis, c'est entre 5 et 10 %, oui, ou 5 %. 5, 5. 5 %, et du coup, la volonté, ce serait de... de monter ces critères-là. D'être à 30. À 30, oui, OK.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 40, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": [", OK", ", la volonté", ", OK", "Et du coup", ", OK", "ce serait", "ces critères", "ces critères", "5 %", "5 %", "5 %", "5 %", "du coup", "5 %", "5 %", "du coup", ", la volonté", "ces critères", "ces critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 188, "end": 193}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_190", "text": "Parce qu'à un moment donné, l'entreprise, c'est une mécanique, faut quand même, bah finalement, qu'elle soit solvable, qu'elle soit durable. Mais on arrête de tout mettre sur la finance. <PERSON><PERSON>, je parle avec passion d'économie réelle, hein, mais la finance, je suis moins doué.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 44, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 1.0}, "noun_phrases": ["un moment", "un moment", "la fin", "un moment", "un moment", "la finance", "un moment", "un moment", "un moment", "un moment", "la finance", "économie réelle", "un moment", "la fin"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 189, "end": 192}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_191", "text": "Pas bien doué, d'aille<PERSON>. <PERSON><PERSON>, dans les critères économiques, la valorisation des entreprises.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 13, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.8}, "noun_phrases": ["les critères", "les critères", "les critères", "les cri", "les critères", "des entreprises", "des entreprises", "Les critères", "des entreprises", "les critères", "les critères", "Les critères", "les critères", "les critères économiques", "Des entreprises", "les critères", "la valorisation", "les critères"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 190, "end": 192}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_192", "text": "Oui. Et heu... avant, j'avais aussi marqué heu... « intégrer des critères d'éthique mesurables et affichables ».", "features": {"temporal_context": "unknown", "discourse_markers": [], "sentence_count": 1, "word_count": 17, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 1.0, "performance_indicators": 0, "legitimacy_indicators": 1}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.2, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["des critères", ", j'", "des critères", "Des critères", "des critères", "éthique mesurables et affichables"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 191, "end": 193}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_193", "text": "<PERSON><PERSON>, parce que les cartes qu'on a là, en fait... <PERSON>h malgré tout, heu... enfin je veux dire, on l'a dit au début, on reste dans une économie capitalistique, donc en fait, heu... y'aura quand même ces critères-là de heu... parts de marché, rentabilité, croissance des revenus, trésorerie, coûts opérationnels, tout ça. Mais on est d'accord. Mais là, je suis dans la performance financière qui doit être là, voilà. C'est ce que j'appelle la mécanique des entreprises. Oui, oui.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 79, "thematic_indicators": {"performance_density": 3.0, "legitimacy_density": 0.0, "performance_indicators": 3, "legitimacy_indicators": 0}, "tension_patterns": {"accumulation_partage": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}, "croissance_decroissance": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.6, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": ["accumulation_partage", "croissance_decroissance"], "conceptual_complexity": 1.0}, "noun_phrases": ["la performance", "une économie", "donc en fait", "la performance financière", "La performance", "la performance", "donc en fait", "en fait", "une économie capitalistique", "des entreprises", "des entreprises", "les cartes", "la performance", ", en fait", "les cartes", "tout ça", "des entreprises", ", en fait", "une économie", "La performance", "les cartes", "coûts opérationnels", ", croissance", "ces critères", "ces critères", "ces critères", "Des entreprises", "une économie capitalistique", "donc en fait", "ces critères", "... parts", "coûts opérationnels", ", tout ça", "la mécanique", ", en fait", "une économie capitalistique", "en fait", "donc en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 192, "end": 197}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_194", "text": "<PERSON><PERSON> dem<PERSON>, on peut... on peut pas juger seulement une entreprise que là-dessus. Donc", "features": {"temporal_context": "2050", "discourse_markers": [], "sentence_count": 1, "word_count": 14, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ORGANISATIONNELS"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "Une entreprise", "une entreprise", "Une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise", "une entreprise"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 193, "end": 195}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_195", "text": "<PERSON><PERSON>, c'est pour ça qu'il fallait un postulat de dépar<PERSON>, parce que c'est... c'est compliqué. Quand... quand on voit les... les dérives que certains pays peuvent heu... peuvent avoir, on peut... on peut se poser quand même légitimement 2-3 petites questions, quand même.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 43, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["<PERSON><PERSON>, c'est pour ça", "un postulat", "les dérives", "certains pays", "quand même légitimement 2-3 petites questions"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 194, "end": 196}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_196", "text": "Ce qui peut écoeurer aussi l'entrepreneur qui voyage un petit peu, y'a tout ce qu'on fait en Europe, tout ce qu'on fait en France, même d'ailleurs, parce que si je vais qu'en Italie, y'a des fois où on se pose des questions aussi, sur heu... O<PERSON>, sur l'aspect social et heu... Et environnemental.", "features": {"temporal_context": "unknown", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 53, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 2.0, "performance_indicators": 0, "legitimacy_indicators": 2}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.4, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["tout ce", "tout ce", "tout ce", "tout ce", "tout ce", "tout ce", "des fois", "tout ce", "tout ce", "des fois", "des questions"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 195, "end": 197}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_197", "text": "Donc à un moment donné, on se dit, en France, on nous contraint, c'est super de contraindre un petit peu, parce que c'est obligé, mais on fait rien aussi pour le reste du monde. Donc est-ce qu'on jette pas une goutte d'eau dans l'océan ? Et il faut commencer par le faire, aussi, ça commence par là. Oui, oui. <PERSON> vois, là, on en parle pas, ici, en projection 2050, mais qu'ont fait les autres pays ? C'est... c'est ce que disent un certain...", "features": {"temporal_context": "2050", "discourse_markers": ["context"], "sentence_count": 1, "word_count": 84, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["un moment", "un moment", "un moment", "un moment", "un moment", "un moment", "un moment", "un moment", "un moment", "le reste", "une goutte", "les autres pays", "un certain"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 196, "end": 202}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_198", "text": "enfin certains économistes qui disent : en fait, les rejets néfastes pour l'environnement, aujourd'hui, heu... la France, c'est 8 %, je crois, de... la France, l'Europe, c'est 8 % de tous les rejets mondiaux, donc du coup, en fait, c'est pas nous qui devons faire l'effort, c'est les autres. Oui, mais il faut continuer à les faire.", "features": {"temporal_context": "2023", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 57, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": ["MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": [], "conceptual_complexity": 0.4}, "noun_phrases": ["en fait", "donc du coup", ", en fait", ", en fait", "du coup", "du coup", ", en fait", "enfin certains économistes", "les rejets néfastes", "... la France", ", c'est 8 %", "... la France", "tous les rejets mondiaux", "donc du coup", "la France", "en fait"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 197, "end": 199}, "ml_readiness_score": 0.7, "target_format_compatibility": true}}, {"id": "Table_D_seg_199", "text": "Bah le fait encore pas mal, enfin je veux dire, y'a pas mal de choses qui sont produites en Asie ou heu... ou en Moyen-Orient, heu... Ou pour le... la maîtrise des routes maritimes, ce que fait la Chine, aller nous... nous créer des îles pour se donner des... enfin c'est des trucs aberrants, quoi. Non, mais la fabrication...", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 59, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["le fait", "des trucs", "pas mal", "en Moyen-Orient", "la maîtrise", "routes maritimes", "la Chine", "des îles", "la fabrication", "la fabrication", "la fabrication", "la fabrication"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 198, "end": 200}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_200", "text": "la fabrication des véhicules électriques, par exemple, heu... c'est pas du tout... enfin la... le... la Chine se fait taper sur les doigts en disant : mais en fait, vous polluez comme personne sur la fabrication des véhicules. En fait, c'est des véhicules qui vont pas forcément être en Chine, qui vont être en Europe, en fait. Oui, je suis d'accord. Parce que nous, on pollue moins, mais en fait, c'est parce qu'on fou... on fabrique pas dans... sur le territoire. Oui.", "features": {"temporal_context": "unknown", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 82, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2035.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["en fait", ", en fait", ", en fait", "le territoire", "le territoire", ", en fait", "la Chine", "la fabrication", "la fabrication", "véhicules électriques", "les doigts", "en fait", "la fabrication", "fait, c'est des véhicules", "le territoire", "la fabrication"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 199, "end": 204}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_201", "text": "Donc en fait, c'est facile de dire : on va utiliser des produits propres, écologiques, machin, on n'en fabrique pas chez nous, donc en fait, c'est... enfin tu vois, dans les critères de performance, on peut être aussi sur heu... sur une relocalisation de l'économie, finalement. Et ce qui amènera bah plein de choses niveau social, mais... mais ce qui permettra aussi de mesurer ce... enfin sur quels heu... quels leviers on peut activer pour justement être socialement et environnementalement responsable, quoi.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 82, "thematic_indicators": {"performance_density": 2.0, "legitimacy_density": 2.0, "performance_indicators": 2, "legitimacy_indicators": 2}, "tension_patterns": {"local_global": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": ["MODELES_SOCIO_ECONOMIQUES", "MODELES_ENVIRONNEMENTAUX"], "ml_features": {"performance_score": 0.4, "legitimacy_score": 0.4, "temporal_period": 2050.0, "tension_indicators": ["local_global"], "conceptual_complexity": 1.0}, "noun_phrases": ["de l'économie", "les critères", "donc en fait", "les critères", "les critères", "les cri", "les critères", "donc en fait", "en fait", "Les critères", "les critères", "les critères", "Les critères", "les critères", "les critères", "donc en fait", "en fait", "des produits propres, écologiques, machin", "donc en fait", "les critères", "une relocalisation", "Et ce", "bah plein de choses niveau social", "mais ce", "quels heu", "quels leviers"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 200, "end": 202}, "ml_readiness_score": 1.0, "target_format_compatibility": true}}, {"id": "Table_D_seg_202", "text": "On n'est pas adaptés, en face. C'est certain. Et on va créer des déséquilibres, hein, y'a plus que les gens qui... il faudra avoir de l'argent, pour avoir une voiture. Ah mais c'est certain. Et très rapidement, quand je dis « demain », c'est vraiment demain. Ah mais c'est déjà aujourd'hui. C'est déjà aujourd'hui, oui.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 55, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"court_terme_long_terme": {"side_a": 1, "side_b": 0, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["court_terme_long_terme"], "conceptual_complexity": 0.6}, "noun_phrases": ["les gens", "Les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "les gens", "une voiture", "les gens", "les gens", "une voiture", "une voiture", "les gens", "les gens", "les gens", "des déséquilibres", "les gens", "une voiture", "une voiture"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 201, "end": 209}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}, {"id": "Table_D_seg_203", "text": "Enfin à <PERSON>, heu... nous, on a une vieille voiture et moi, je me dis, mais si je la... d'abord, est-ce que j'ai les moyens de... d'en changer ? Pour une voiture qui est... qui serait une voiture électrique, dans lequel je puisse rentrer dans Lyon ? Parce qu'en fait, sinon, je peux plus... à partir du 1er janvier de cette année, ça va laisser l'année prochaine où je vais plus pouvoir rentrer dans Lyon. Je me dis, me dus, il va falloir que j'achète une voiture électrique, j'ai pas de garage, j'habite à un endroit où les... les places de parking sont en tension en bas de chez moi. En fait, c'est... comment... comment je branche mon véhicule ?", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 120, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["Et moi", "Et moi", "Et moi", "en fait", "l'année prochaine", ", j'", "de ce", "une voiture", "une voiture", "une voiture", "en fait", "une voiture", "une vieille voiture", "les moyens", "une voiture", "cette année", "l'année prochaine", "une voiture électrique", "de garage", "un endroit", "les places", "chez moi", "mon véhicule"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 202, "end": 207}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_204", "text": "Enfin du coup, je vais acheter un véhicule que je vais pas pouvoir utiliser, qui va me coûter beaucoup plus cher que... et avec un bilan qui est... qui est très très discutable sur la pollution qu'il engendre. Ah mais ça... Je veux dire, sur la fabrication, sur heu... On va prendre en photo l'affiche et les cartes en même temps, comme ça on sait qu'elles sont associées. Tu veux que j'arrête ça ? Oui, merci. <PERSON><PERSON>, on peut l'arrêter.", "features": {"temporal_context": "2050", "discourse_markers": ["priority"], "sentence_count": 1, "word_count": 80, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2050.0, "tension_indicators": [], "conceptual_complexity": 0.0}, "noun_phrases": ["comme ça", "comme ça", "<PERSON><PERSON>a", "les cartes", "les cartes", "les cartes", "du coup", "du coup", "la fabrication", "la fabrication", "la fabrication", "un véhicule", "avec un bilan", "la pollution", "la fabrication", "même temps", "<PERSON><PERSON>, merci"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 203, "end": 208}, "ml_readiness_score": 0.5, "target_format_compatibility": true}}, {"id": "Table_D_seg_205", "text": "Date : 29/12/2023 Nom du fichier : « D1 » Commanditaire : <PERSON> : 59 minutes (D1)+ 34 minutes (D2) Remarques particulières : en italique les modératrices du groupe global . Un time-code.", "features": {"temporal_context": "2023", "discourse_markers": [], "sentence_count": 1, "word_count": 35, "thematic_indicators": {"performance_density": 0.0, "legitimacy_density": 0.0, "performance_indicators": 0, "legitimacy_indicators": 0}, "tension_patterns": {"local_global": {"side_a": 0, "side_b": 1, "tension_strength": 1, "total_indicators": 1}}, "conceptual_markers": [], "ml_features": {"performance_score": 0.0, "legitimacy_score": 0.0, "temporal_period": 2023.0, "tension_indicators": ["local_global"], "conceptual_complexity": 0.6}, "noun_phrases": ["« D1", "<PERSON>", "59 minutes", "D1)+ 34 minutes", "les modératrices", "groupe global", "Un time-"]}, "metadata": {"source": "data_renamed\\Table_D.docx", "segment_lines": 1, "position": {"start": 204, "end": 209}, "ml_readiness_score": 0.8, "target_format_compatibility": true}}]}