Temporal Context Distinction in French Transcripts: Analysis and Methodologies for Present (2023) vs. Future (2050) Classification1. IntroductionPurpose: This report provides a comprehensive analysis and practical methodologies for distinguishing between present (circa 2023) and future (circa 2050) temporal contexts within French language transcripts. The focus is on identifying relevant linguistic indicators, evaluating the capabilities and limitations of Natural Language Processing (NLP) tools like spaCy, and detailing rule-based techniques suitable for implementation by NLP practitioners.Context: Accurately determining the temporal frame of reference within a text is fundamental for numerous NLP applications, including information extraction, automated timeline generation, question answering systems, and text summarization.1 Understanding whether a statement refers to the present moment or a future point in time is crucial for correct interpretation and subsequent processing. This task presents specific challenges when dealing with designated timeframes like the current year (2023) versus a specific future year (2050), particularly in languages like French where tense usage can be complex and context-dependent.Approach: The analysis begins by examining the primary French verb tenses employed to express present and future time, highlighting nuances in their usage. Subsequently, the report evaluates the capabilities of the spaCy NLP library, specifically its part-of-speech (POS) tagging and morphological analysis features, for identifying these tenses, including documented limitations. It then explores other linguistic cues, such as temporal adverbs and common patterns, that signal temporal context. Strategies for disambiguating ambiguous cases, notably the use of the present tense for future events, are proposed. The report discusses the expected accuracy and inherent challenges associated with rule-based temporal distinction systems. Finally, it provides detailed technical descriptions of specific techniques for temporal classification, adhering to a structured format for clarity and implementation readiness.Target Audience & Scope: This document is intended for technical personnel, such as NLP Engineers, Data Scientists, or Researchers, tasked with implementing temporal context classification systems. The scope is confined to the French language, focusing on the binary distinction between present (approximated as 2023) and future (specifically targeting 2050) contexts within transcribed text, primarily through rule-based NLP approaches augmented by spaCy's functionalities.2. Temporal Indicators in French Verb TensesOverview: French grammar offers several indicative verb tenses to convey temporal information. While some tenses are strongly associated with either present or future time, their usage is often nuanced, influenced by context, formality, and speaker intent. Understanding these tenses and their typical functions is the first step toward building a temporal classification system.2.1. Présent de l'Indicatif (Present Tense)The Présent de l'indicatif (Present Indicative) is arguably the most versatile and frequently used tense in French.Core Usage: Its primary functions align closely with the English simple present and present continuous:
Current Actions/States: Describing actions happening at the moment of speaking or states of being that are currently true.4 Examples include: "Le téléphone sonne." (The phone rings/is ringing) 4 or "Il est en retard." (He is late).6
Habitual Actions: Expressing actions that occur regularly or habitually.4 Time expressions like "tous les jours" (every day) or days of the week with a definite article (le vendredi - on Fridays) often signal this usage.4 Example: "Tous les jours, je me lève à 6 heures." (Every day, I get up at 6).4
General Truths/Timeless Facts: Stating facts considered universally or generally true.4 Examples: "Les lions mangent de la viande." (Lions eat meat) 4 or "L'eau est essentielle à la vie." (Water is essential to life).6
Future Reference Usage: Crucially, the Présent is also very commonly used to refer to future events, making it a significant source of temporal ambiguity.4 This usage is particularly prevalent in spoken French.10 Key future contexts include:
Near Future: Referring to actions planned or expected to happen soon. In informal speech, it is often used interchangeably with the Futur Proche (aller + infinitive).8 Examples: "J'arrive tout de suite!" (I'm coming right away! / I'll be right there!) 9, "On part en vacances demain matin." (We are leaving on vacation tomorrow morning).7
Scheduled Events: Describing future events that are fixed or part of a schedule, such as arrivals, departures, or appointments.9 Example: "Son train arrive à dix heures demain matin." (His train arrives at ten tomorrow morning).9
Conditional Clauses (Si Clauses): Used in the condition part ('if' clause) of hypothetical sentences referring to likely future situations, where the result clause is often in the Futur Simple or imperative.6 Example: "Si tu veux, tu peux dîner avec moi." (If you want, you can have dinner with me).6 Note the difference from English, where the future tense might appear after conjunctions like 'as soon as'; French often requires the future tense in such subordinate clauses if the main clause is future.6
The inherent ambiguity of the Présent tense, coupled with its frequent deployment for future reference, especially in conversational contexts 10, presents a primary challenge. A system relying solely on dedicated future tenses like Futur Simple or Futur Proche would inevitably fail to capture a significant portion of future-oriented statements, leading to poor recall. Consequently, developing robust methods to disambiguate the Présent tense based on context is not merely an enhancement but a critical requirement for accurate temporal classification in French.2.2. Futur Simple (Simple Future)The Futur Simple corresponds most closely to the English "will + verb" construction and is used to situate actions or states firmly in the future.Formation: It is formed by adding specific endings (-ai, -as, -a, -ons, -ez, -ont) directly to the infinitive form of -er and -ir verbs.8 For regular -re verbs, the final 'e' of the infinitive is dropped before adding the endings.14 A key characteristic is the pronounced 'r' sound preceding the ending in all forms.8 While the endings are regular, numerous common verbs have irregular stems that must be learned (e.g., être -> ser-, avoir -> aur-, aller -> ir-, faire -> fer-, pouvoir -> pourr-, venir -> viendr-).15Core Usage: The Futur Simple is primarily used for:
Future Plans, Intentions, Predictions: Expressing actions planned for the future, stating intentions, or making predictions about future events.14 It often implies a greater distance in time or less certainty compared to the Futur Proche.8 Examples: "Demain, je visiterai le musée." (Tomorrow, I will visit the museum) 14, "Il pleuvra demain." (It will rain tomorrow) 21, "Les prix augmenteront l'année prochaine." (Prices will increase next year).21
Formal Contexts: It is the preferred future tense in formal writing and speech.8
Conditional Sentences (Si Clauses): Used in the result clause when the condition (introduced by si) is expressed in the Présent tense, indicating a likely future outcome.14 Example: "Si nous ratons le bus, nous marcherons." (If we miss the bus, we will walk).14
Temporal Subordinate Clauses (Formal): In formal French, conjunctions like quand (when), lorsque (when), dès que (as soon as), aussitôt que (as soon as) require the Futur Simple in the subordinate clause if the main clause refers to the future.13 Example: "Nous sortirons quand la pluie s'arrêtera." (We'll go out when the rain stops).15 This contrasts with English usage, which often employs the present tense in such clauses.13
2.3. Futur Proche (Near Future)The Futur Proche is a compound tense construction used extensively, particularly in spoken French, to refer to the near future.Formation: It is formed using the present tense conjugation of the auxiliary verb aller (to go) followed by the main verb in its infinitive form.8 Example: "Je vais manger." (I am going to eat).20 In negative sentences, the negation particles (ne...pas) surround the conjugated form of aller.8 Object pronouns are placed between aller and the infinitive.8Core Usage: The Futur Proche typically expresses:
Immediate or Near Future Actions: Events expected to happen very soon.8 Example: "L'avion va atterrir dans 2 minutes." (The plane is going to land in 2 minutes).27
High Certainty/Inevitability: It often conveys a stronger sense of certainty or inevitability about the future event from the speaker's perspective compared to the Futur Simple.8 For instance, "Je vais avoir un enfant" implies a high degree of certainty, such as confirmed pregnancy or adoption.23
Informal/Spoken French: It is highly prevalent in everyday conversation and informal contexts.8
Given its dominance in spoken French 10 and its straightforward structure (aller [Présent] + Infinitive), identifying this pattern is a high-yield strategy for detecting future context in transcripts of conversations or interviews. While both Futur Proche and Futur Simple denote future time, the choice between them can subtly signal the speaker's perceived immediacy or certainty regarding the event 13, a nuance that might be relevant for more sophisticated temporal analysis beyond simple classification.2.4. Conditionnel Présent (Present Conditional)While primarily a mood expressing hypothesis or politeness, the Conditionnel Présent can also relate to future time, albeit with less certainty than the dedicated future tenses.Formation: It is formed using the same stem as the Futur Simple (often the infinitive, with the same irregularities) but adding the endings of the Imparfait (Imperfect tense): -ais, -ais, -ait, -ions, -iez, -aient.17Core Usage: Its main functions include expressing politeness (Je voudrais un café - I would like a coffee), hypothetical situations (Si j'avais de l'argent, j'achèterais une voiture - If I had money, I would buy a car), giving advice (Tu devrais étudier - You should study), and stating wishes (J'aimerais voyager - I would like to travel).17Relevance to Future: Its connection to future time stems from its use to express:
Future Possibility/Eventuality: Actions that could potentially happen in the future, often contingent on certain conditions or representing a degree of uncertainty.31 Example: "Cet après-midi, je pourrais faire du shopping." (This afternoon, I might/could go shopping) 31 or "Demain, il pourrait pleuvoir." (Tomorrow, it could/might rain).31
Future-in-the-Past: Describing an event that was future from a past perspective.30 Example: "Il a dit qu'il viendrait." (He said that he would come). While not strictly future relative to the present moment of analysis, recognizing this structure is important for understanding temporal relations within a narrative.
Compared to the Futur Simple and Futur Proche, the Conditionnel Présent serves as a weaker, more nuanced indicator of future context. Its primary role is modal, expressing potentiality rather than definite futurity. However, its presence, especially when combined with explicit future temporal markers like "demain" 31, can contribute evidence towards a future classification, albeit one marked by uncertainty or conditionality.3. Leveraging spaCy for Tense IdentificationOverview: spaCy is a widely used Python library for advanced NLP tasks, offering functionalities like tokenization, part-of-speech (POS) tagging, and morphological analysis, which are potentially useful for identifying verb tenses.333.1. POS Tagging (token.pos_, token.tag_)spaCy assigns both coarse-grained Universal POS (UPOS) tags (e.g., VERB, NOUN, ADJ via token.pos_) and fine-grained, language-specific tags (via token.tag_).34 Identifying tokens tagged as VERB or AUX (auxiliary verb) is the necessary first step before analyzing tense and mood. While generally reliable for broad categories, errors can occur; for instance, imperative verb forms in French have been reported to be occasionally misclassified as nouns (NOUN) by spaCy models.373.2. Morphological Analysis (token.morph)The token.morph attribute provides a more detailed breakdown of a token's grammatical properties, represented as a set of feature-value pairs, typically following the Universal Dependencies (UD) standard.36 Key features relevant for verb tense analysis include 35:
Tense: Indicates the tense (e.g., Pres for Present, Past for Past, Fut for Future).
Mood: Specifies the grammatical mood (e.g., Ind for Indicative, Cnd for Conditional, Imp for Imperative, Sub for Subjunctive).
VerbForm: Denotes the form of the verb (e.g., Fin for Finite, Inf for Infinitive, Part for Participle).
Person: Indicates the grammatical person (e.g., 1, 2, 3).
Number: Specifies grammatical number (e.g., Sing for Singular, Plur for Plural).
In an ideal scenario, these morphological features would allow for precise distinction between the tenses relevant to the present vs. future classification:
Présent de l'indicatif: Mood=Ind, Tense=Pres
Futur Simple: Mood=Ind, Tense=Fut
Futur Proche: Identified by the pattern aller (Mood=Ind, Tense=Pres) + Verb (VerbForm=Inf)
Conditionnel Présent: Mood=Cnd, Tense=Pres (Note: UD typically classifies Conditional mood as having Present tense).
3.3. Limitations and Inaccuracies in French ModelsDespite high reported overall accuracy for morphological analysis in spaCy's French models (e.g., fr_core_news_lg MORPH_ACC 0.95 35), specific and systematic errors have been documented, particularly concerning the future and conditional forms crucial for this task.37User reports and testing 37 reveal significant misclassifications for unambiguous verb forms when using standard fr_core_news models:
Imperative: The imperative form "Remplacez" (Replace) was tagged as POS=NOUN instead of VERB.
Futur Simple: The future form "remplacerez" (you will replace) was tagged with Mood=Imp (Imperative) and Tense=Pres (Present), instead of the correct Mood=Ind, Tense=Fut.
Conditionnel Présent: The conditional form "remplaceriez" (you would replace) was tagged with Mood=Ind (Indicative) and Tense=Fut (Future), instead of the correct Mood=Cnd, Tense=Pres.
Quantitative tests on a set of common French verbs indicated alarmingly low recognition rates for these specific forms: only 37% of future tense forms and a mere 7% of conditional mood forms were correctly identified by their morphology tags, with imperatives also performing poorly (13% correct).37 These inaccuracies are likely due to the relative infrequency of these specific irrealis forms (future, conditional, imperative) in the news-based corpora typically used to train the fr_core_news models.37The discrepancy between the high overall morphological accuracy score 35 and the poor performance on these specific, crucial verb forms demonstrates that the headline accuracy figure can be misleading for tasks requiring fine-grained tense/mood distinctions. Relying solely on token.morph attributes like Tense=Fut or Mood=Cnd generated by standard spaCy French models is therefore highly unreliable for distinguishing future and conditional contexts.The following table summarizes the expected Universal Dependencies morphological features for key French verb forms relevant to temporal context, contrasted with the reported tagging behavior observed in standard spaCy French models (based primarily on findings in 37).Table 1: spaCy French Morphology - Expected vs. Reported Behavior for Temporal Context
Verb Form ExampleTense/MoodExpected UD Morph Features (Mood, Tense, VerbForm)Reported fr_core_news Morph Features (Example: remplacer)Reliability AssessmentparlePrésent IndicatifMood=Ind, Tense=Pres, VerbForm=FinMood=Ind, Tense=Pres, VerbForm=Fin (Generally Correct)HighparleraFutur SimpleMood=Ind, Tense=Fut, VerbForm=FinMood=Imp, Tense=Pres (remplacerez) 37Lowvais parlerFutur Prochealler: Mood=Ind, Tense=Pres; parler: VerbForm=InfPattern Recognition (Not direct morph tag on single verb)N/A (Pattern Match)parleraitConditionnel PrésentMood=Cnd, Tense=Pres, VerbForm=FinMood=Ind, Tense=Fut (remplaceriez) 37LowRemplacez!Impératif PrésentMood=Imp, Tense=Pres, VerbForm=FinPOS=NOUN (Remplacez) 37Very Low (POS Error)
(Note: Reliability assessment is based on reported issues 37 specifically concerning future/conditional/imperative forms. Basic present indicative forms are generally tagged reliably).This unreliability necessitates a shift in strategy. While spaCy remains invaluable for foundational processing steps like tokenization, lemmatization, and basic POS tagging, its morphological analysis output for future and conditional tenses in French cannot be trusted implicitly. A robust system must incorporate supplementary methods, primarily rule-based approaches grounded in linguistic knowledge of verb conjugations, auxiliary verb patterns (like aller + infinitive for Futur Proche), and the analysis of contextual cues such as temporal adverbs, to either augment or, where necessary, override spaCy's potentially flawed morphological tags.4. Temporal Adverbs and Linguistic PatternsBeyond verb tense, other linguistic elements provide crucial clues for anchoring text segments in time. Temporal adverbs, adverbial phrases, and specific grammatical patterns can explicitly signal present or future contexts.4.1. Adverbs/Adverbial Phrases Indicating Present ContextThese markers situate an action or state firmly in the current timeframe:
maintenant (now) 42
actuellement (currently, nowadays) 43
aujourd'hui (today) 42
en ce moment (at the moment) 47
présentement (presently)
à présent (at present)
Example: "Je fête mes 10 ans aujourd'hui." (I'm celebrating my 10th birthday today).434.2. Adverbs/Adverbial Phrases Indicating Future ContextA wide range of adverbs and phrases point towards future events. These can vary in specificity:
Definite Future:

demain (tomorrow) 9
après-demain (the day after tomorrow)
l'année prochaine (next year) 10
le mois prochain (next month)
[jour de la semaine] prochain (next [day of week], e.g., lundi prochain)
ce soir (tonight, this evening) 9
cet après-midi (this afternoon) 18
dans [timeframe] (in [timeframe], e.g., dans deux jours, dans une heure) 9
d'ici [timeframe] (by [timeframe])


Indefinite Future:

bientôt (soon) 9
plus tard (later) 9
à l'avenir (in the future)
prochainement (soon, shortly)
ultérieurement (subsequently, later)
un jour (one day, someday)
ensuite (next, then) 42
après (after) 47
désormais / dorénavant (from now on) 44
tout à l'heure (in a little while) 44


Examples: "Je ferai du sport demain." (I will play sports tomorrow).46 "Elle arrive bientôt." (She'll be there soon / She is arriving soon).45The following table provides a structured overview of common temporal indicators.Table 2: French Temporal Adverbs and Phrases for Present/Future Context
Adverb/PhraseTemporal ContextSpecificity (Future)Example UsageSource Snippet(s)maintenantPresentN/AJe travaille maintenant. (I am working now.)42actuellementPresentN/AActuellement, il est en réunion. (Currently...)43aujourd'huiPresentN/AIl pleut aujourd'hui. (It's raining today.)42en ce momentPresentN/AQue fais-tu en ce moment? (What are you doing..?)47demainFutureDefiniteNous partirons demain. (We will leave tomorrow.)42bientôtFutureIndefiniteLe film commence bientôt. (The film starts soon.)42plus tardFutureIndefiniteJe t'appelle plus tard. (I'll call you later.)9l'année prochaineFutureDefiniteIl voyagera l'année prochaine. (He will travel...)15dans [temps]FutureDefiniteElle arrive dans 10 minutes. (She arrives in...)9d'ici [temps]FutureDefiniteFinis le rapport d'ici vendredi. (Finish...by...)ce soirFutureDefiniteJe sors ce soir. (I'm going out tonight.)15ensuite / puisFuture (Sequence)IndefiniteD'abord je lis, ensuite je dors. (First I read...)42aprèsFuture (Sequence)IndefiniteOn mange après le film. (We eat after the film.)47à l'avenirFutureIndefiniteÀ l'avenir, sois prudent. (In the future, be...)prochainementFutureIndefiniteLe magasin ouvrira prochainement. (The shop will...)
4.3. Other Linguistic PatternsBeyond single adverbs or simple phrases, certain grammatical structures frequently signal future orientation:
Temporal Conjunctions: As mentioned (Section 2.2), conjunctions like quand, lorsque, dès que, aussitôt que often introduce clauses in the Futur Simple in formal contexts when the main clause is also future.13 Detecting this pattern (Conj + Subj + Verb) can be a strong future indicator in relevant text types.
Prepositional Phrases: Phrases indicating a starting point or duration extending into the future, such as à partir de [future time] (starting from...), jusqu'à [future time] (until...), signal future relevance.
Verbs of Planning/Intention: While the verbs themselves might be conjugated in the present tense, verbs expressing planning, intention, forecasting, or scheduling inherently point towards future actions or states. Examples include prévoir (to plan/forecast), compter (to intend), envisager (to consider/envisage), planifier (to plan), promettre (to promise). Their presence increases the likelihood that associated actions or events described (often via infinitives or subordinate clauses) are future-oriented. Example: "Nous prévoyons de lancer le produit en 2050." (We plan to launch the product in 2050).
A significant challenge with using temporal adverbs and phrases is scope ambiguity. An adverb like "demain" might modify only the immediately following clause, or it could set the temporal context for an entire paragraph. Simple keyword spotting might incorrectly associate an adverb with a verb outside its actual scope.3 Rule-based systems often use heuristics (e.g., assuming sentence-level scope) or rely on more complex syntactic parsing (e.g., dependency relations) to link adverbs to the verbs they modify, though this adds complexity and potential for parsing errors.505. Disambiguating Present Tense for Future ReferenceThe Challenge: As established, the Présent de l'indicatif is frequently used in French to denote future events, particularly near or scheduled ones.4 Distinguishing this future usage from its standard present-time usage is crucial for accurate temporal classification.5.1. Key Disambiguating FactorsSeveral contextual factors help resolve the ambiguity of the Présent tense:
Co-occurring Future Temporal Markers: This is the strongest indicator. The presence of explicit future adverbs or phrases (demain, ce soir, bientôt, lundi prochain, dans une heure, l'année prochaine, etc.) within the same sentence or immediate context strongly suggests a future interpretation of a present tense verb.7 Example: "On part en vacances demain matin." - the presence of "demain matin" forces a future reading of "part".7
Context of Scheduled Events: When the context involves schedules, timetables, or fixed arrangements (appointments, travel itineraries, opening/closing times), the Présent is often used for future events.9 Example: "Son train arrive à dix heures demain matin." - the verb "arrive" (Présent) refers to a future scheduled event signaled by "demain matin".9
Verb Semantics: Certain types of verbs are more prone to being used in the present tense for future actions. These often include:

Verbs of motion: aller (to go), venir (to come), partir (to leave), arriver (to arrive), rentrer (to return).9
Verbs indicating initiation or completion: commencer (to start), finir (to finish).
Verbs related to communication/arrangement: téléphoner (to call), rappeler (to call back).9


Absence of Present Markers: While a weaker signal, the lack of explicit present-time markers (maintenant, actuellement, etc.) in a sentence containing a present tense verb might slightly increase the probability of a future reading, but only if other supporting future cues (like verb type or context) are present. It is not sufficient on its own.
The effectiveness of disambiguation hinges on the interplay between these factors. A present tense verb combined with a strong future adverb is a clear future case. A present tense motion verb alone might be ambiguous, but if paired even with a weak future adverb (plus tard), the future interpretation becomes much more likely. Therefore, rules must be designed to weigh and combine these different pieces of evidence.5.2. Rule-Based HeuristicsBased on the disambiguating factors, the following heuristics can form the basis of a rule-based system:

Rule 1 (Strong Future Adverb Dominance):

Condition: A verb is identified as Présent de l'indicatif (either via reliable morphology tag or pattern matching) AND a strong, definite future temporal marker (e.g., demain, l'année prochaine, dans X temps, ce soir, explicit future date/year like 2050) is detected within the same sentence or clause scope.
Action: Classify the context as FUTURE.
Rationale: Explicit future temporal markers generally override the default present meaning of the tense.7



Rule 2 (Weak Future Adverb + Supporting Verb Type):

Condition: A verb is identified as Présent de l'indicatif AND a weaker or indefinite future temporal marker (e.g., bientôt, plus tard, ensuite) is detected within scope AND the verb's lemma belongs to a predefined list of verbs commonly used in this construction (e.g., motion verbs, scheduling verbs, communication verbs).
Action: Classify the context as FUTURE.
Rationale: The combination of a future-leaning adverb (even if weak) and a verb type known to participate in this construction provides cumulative evidence for a future reading.



Rule 3 (Scheduled Event Context):

Condition: A verb is identified as Présent de l'indicatif AND the verb's lemma belongs to a predefined list of verbs associated with scheduling or fixed events (e.g., arriver, partir, ouvrir, fermer, commencer) AND the surrounding context (e.g., presence of times, locations like gare (station), mentions of tickets, appointments) suggests a scheduled event OR a future temporal marker is present.
Action: Classify the context as FUTURE.
Rationale: The Présent is conventionally used for future scheduled events.9



Rule 4 (Default Present):

Condition: A verb is identified as Présent de l'indicatif AND none of the conditions for Rules 1, 2, or 3 are met (i.e., no significant future markers or supporting context detected).
Action: Classify the context as PRESENT.
Rationale: In the absence of contrary evidence, the default interpretation of the Présent tense is present time.


It is worth noting that the Présent-for-future construction often implies a degree of immediacy or nearness.9 The examples commonly cited involve adverbs like demain, ce soir, tout de suite.7 While the target future context here is distant (2050), the disambiguation mechanisms often rely on detecting cues typically associated with nearer futures. This suggests that rules based on common future adverbs and scheduling contexts will likely capture the majority of Présent-for-future cases effectively. Using the Présent tense to refer to distant, non-scheduled future events without an explicit marker like "en 2050" is less common than using the Futur Simple. Therefore, the primary challenge addressed by these rules is identifying the near/scheduled future uses of the Présent. Distant future references are more likely to use dedicated future tenses or explicit date markers.5.3. Handling AmbiguityDespite these rules, some instances will remain ambiguous. Strategies include:
Confidence Scoring: Assigning a score based on the strength and number of cues supporting a future reading versus a present reading.
Flagging: Marking segments with conflicting cues or insufficient evidence as 'AMBIGUOUS' for potential manual review or alternative processing.
Discourse Analysis: For more complex systems, analyzing the temporal context established in preceding sentences might help resolve ambiguity in subsequent ones lacking explicit markers.
6. Rule-Based Temporal Context Distinction: Accuracy and ChallengesRule-based systems have historically performed well in structured information extraction tasks, including temporal expression processing.6.1. Expected AccuracyEvaluating the expected accuracy for classifying broad temporal context (Present vs. Future) requires looking at related, though not identical, tasks like temporal expression (TIMEX) recognition and normalization.
Performance in Related Tasks: Rule-based systems like HeidelTime have achieved high performance in shared tasks like TempEval.51 For TIMEX extraction on the French TimeBank corpus (newswire), HeidelTime reported an F1-score of 0.85 for strict span matching and 0.91 for relaxed matching.53 For normalization (assigning the correct standardized value, e.g., "2023-10-27" for "today"), the value F1 was 0.74.53 In some challenges, rule-based systems even outperformed early machine learning approaches.52 More recent neural models applied to temporal relation extraction in French clinical text have also shown strong results (F1=0.86).55
Extrapolation to Context Classification: While these results are encouraging, classifying the overall temporal context of a sentence or segment is distinct from extracting and normalizing specific time expressions. The task involves integrating signals from tense, adverbs, and potentially other contextual cues. Given the inherent ambiguities, particularly the Présent-for-future usage, achieving the very high F1 scores seen in TIMEX span detection might be challenging. A well-developed rule-based system for Present vs. Future classification might realistically target F1 scores in the 0.75 to 0.85 range. Precision could be high if rules are carefully crafted and specific, but recall might be limited by the sheer variety of linguistic expression and the difficulty of capturing all relevant patterns, especially in less formal text.
The design of rule-based systems involves an inherent trade-off between precision and recall. Highly specific rules targeting unambiguous patterns (e.g., Futur Simple tense, explicit future dates) will likely achieve high precision but may miss many instances expressed differently (low recall).51 Conversely, more general rules designed to capture a wider range of expressions (e.g., allowing Présent tense with weaker future cues) might increase recall but risk misclassifying present contexts as future (lower precision).3 System development requires careful tuning and evaluation to find an acceptable balance based on the application's specific needs.6.2. Potential Challenges and AmbiguitiesImplementing a rule-based system for temporal context distinction faces several challenges:
Tense Ambiguity: The primary challenge is the Présent-for-future ambiguity discussed extensively. Additionally, phonetic similarity between Futur Simple and Conditionnel Présent endings (especially for "je" forms, e.g., parlerai vs. parlerais) can pose problems in transcribed spoken language if pronunciation is not perfectly clear.8
Scope of Temporal Markers: Accurately determining which verb or clause a temporal adverb modifies is non-trivial.3 A marker might appear in a sentence but apply to a different event than the main verb being analyzed (Insight 6). This often requires syntactic parsing or sophisticated heuristics.
Implicit Context: Temporal context is often established early in a text or conversation and not explicitly repeated in every sentence. Sentence-level analysis may fail if the relevant temporal anchor lies outside the current sentence.
Ungrammatical/Informal Text: Transcripts of spoken language frequently contain grammatical errors, fragments, hesitations, and informal structures that can break rules designed for standard, well-formed text.55
Domain Specificity: Rules and lexicons (e.g., lists of temporal adverbs or relevant verbs) developed for one domain (like newswire, the basis for French TimeBank 53) may not transfer perfectly to others (e.g., technical documentation, medical notes, casual conversation).51 Terminology and common phrasing can differ significantly.
Normalization Complexity: While the primary goal here is classification, accurately interpreting relative temporal expressions ("next week," "three months ago") requires normalization against a reference time (often the Document Creation Time - DCT).51 This adds another layer of complexity if precise temporal values are needed beyond the Present/Future classification.
General NLP Ambiguity: The system is susceptible to broader NLP challenges like lexical ambiguity (words having multiple meanings or POS tags, e.g., French page can be masculine or feminine with different meanings 49) and syntactic ambiguity (sentences allowing multiple valid parse structures 49), which can interfere with rule matching.
Finally, the reported performance of any temporal analysis system is highly dependent on the evaluation corpus used.53 Standard corpora like French TimeBank 53 were annotated according to specific guidelines (ISO-TimeML, TIMEX3) 51 and may not perfectly reflect the specific requirements (Present-2023 vs. Future-2050 classification) or the characteristics (e.g., genre, formality) of the user's target transcripts. Therefore, creating or adapting an evaluation corpus specifically annotated for the desired temporal distinction and representative of the input data is crucial for obtaining a meaningful assessment of the system's real-world performance.7. Detailed Techniques for Temporal Context DistinctionThis section outlines specific techniques that can be combined to build a system for distinguishing present (2023) and future (2050) contexts in French transcripts. Each technique is described following a consistent format: Goal, Procedure, Input, Output, Resources, Example, Limitations, and Improvements. A combined approach leveraging multiple techniques is recommended for optimal performance.7.1. Technique 1: Rule-Based Verb Tense/Form Analysis
Goal: To identify potential present or future temporal context based solely on explicit verb tense morphology and the common Futur Proche construction, without relying on potentially inaccurate spaCy morphology tags for future/conditional.
Procedure:

Preprocess the input text using spaCy to obtain tokens, POS tags, and lemmas.
Iterate through tokens identified as verbs (POS == 'VERB' or 'AUX').
For each verb token, apply pattern matching rules based on its surface form and lemma:

Futur Simple Detection: Check if the verb ending matches known Futur Simple patterns (e.g., -erai, -eras, -era, -erons, -erez, -eront for regular -er verbs; -rai, -ras, -ra, etc., for -re verbs and common irregular stems like ser-, aur-, ir-, fer-). Requires a list of endings and irregular stems.14
Futur Proche Detection: Check if the token is tagged as an infinitive (POS == 'VERB', TAG indicates infinitive, or check token.morph['VerbForm'] == 'Inf' if reliable) AND the preceding token is a present tense form of the verb aller (e.g., token.lemma_ == 'aller' and previous token's text is in ['vais', 'vas', 'va', 'allons', 'allez', 'vont']).20
Conditionnel Présent Detection: Check if the verb ending matches known Conditionnel Présent patterns (e.g., -erais, -erait, -erions, -eriez, -eraient applied to the future stem).17
Présent Tense Detection: If none of the above future/conditional patterns match, check if the ending corresponds to known Présent de l'indicatif conjugations for the verb's group/lemma.


Assign a preliminary temporal classification based on the matched pattern: FUTURE_SIMPLE, FUTURE_PROCHE, CONDITIONAL, PRESENT, or UNKNOWN.


Input: A French text segment, preprocessed with spaCy (tokenization, POS tagging, lemmatization).
Output: A list associating verb tokens with their preliminary rule-based temporal classification (PRESENT, FUTURE_SIMPLE, FUTURE_PROCHE, CONDITIONAL, UNKNOWN).
Resources:

spaCy (for preprocessing).
Comprehensive lists of French verb endings for Présent, Futur Simple, Conditionnel Présent.
List of irregular verb stems for Futur Simple/Conditionnel.
List of present tense forms of aller.


Example:

Input: "Demain, je visiterai le musée et ensuite nous allons manger."
Output: visiterai: FUTURE_SIMPLE, manger (preceded by allons): FUTURE_PROCHE.
Input: "Je suis ici."
Output: suis: PRESENT.
Input: "Il pourrait venir."
Output: pourrait: CONDITIONAL.


Limitations: Does not resolve the Présent-for-future ambiguity. Performance depends heavily on the completeness and accuracy of conjugation rules and irregular stem lists. Can be confused by homographic forms if not carefully implemented (though less likely with full endings). Does not incorporate contextual information like adverbs.
Improvements: Integrate results with adverb analysis (Technique 3) and disambiguation logic (Technique 4). Use finite-state transducers or more sophisticated morphological analyzers if available and reliable for French.
7.2. Technique 2: spaCy POS/Morphology Analysis (Augmented)
Goal: To utilize spaCy's built-in POS tagging and morphological analysis as features or weak indicators, while explicitly accounting for its documented limitations regarding French future and conditional forms.
Procedure:

Process the input text using a suitable spaCy French pipeline (e.g., fr_core_news_lg or fr_dep_news_trf).
Iterate through tokens tagged as VERB or AUX.
Extract morphological features using token.morph. Access specific features like token.morph.get('Tense'), token.morph.get('Mood').
Apply cautious interpretation based on known reliability (referencing Table 1):

If Tense=Pres and Mood=Ind are present, consider this reliable evidence for a potential PRESENT context.
If Tense=Fut or Mood=Cnd are present, treat these tags as unreliable based on documented issues.37 Do not directly classify as FUTURE or CONDITIONAL based solely on these tags.


Instead of direct classification, use the presence/absence or specific values of Tense and Mood features (reliable or not) as input signals for a more comprehensive rule-based system (Technique 4) or potentially a simple machine learning classifier.


Input: Raw French text segment.
Output: Verb tokens annotated with morphological features extracted from spaCy, potentially with a preliminary PRESENT tag if reliable present indicative features are found. The primary output is the set of features themselves for use in subsequent steps.
Resources:

spaCy French model (e.g., fr_core_news_lg).
Knowledge base of spaCy French model reliability for specific morphological tags (e.g., Table 1).
Understanding of Universal Dependencies morphological feature definitions.38


Example:

Input: "Elle chante." -> chante: token.morph likely contains Tense=Pres, Mood=Ind. -> Reliable -> Potential PRESENT.
Input: "Vous remplacerez." -> remplacerez: token.morph reportedly contains Tense=Pres, Mood=Imp.37 -> Unreliable -> Extract features {'Tense': 'Pres', 'Mood': 'Imp'} but do not classify based on them alone.
Input: "Vous remplaceriez." -> remplaceriez: token.morph reportedly contains Tense=Fut, Mood=Ind.37 -> Unreliable -> Extract features {'Tense': 'Fut', 'Mood': 'Ind'} but do not classify based on them alone.


Limitations: Directly inherits the inaccuracies of the spaCy French morphologizer for future and conditional tenses.37 Cannot function as a standalone classifier due to these reliability issues.
Improvements: Use the extracted features within a larger rule set (Technique 4) that incorporates other evidence (verb endings, adverbs). Experiment with different spaCy models (transformer-based models might offer improvements) or alternative French NLP pipelines if available.37 Fine-tuning a spaCy model on a corpus annotated correctly for these tenses could potentially improve morphology tagging accuracy, but requires significant effort.
7.3. Technique 3: Temporal Adverb/Pattern Matching
Goal: To identify explicit temporal indicators (adverbs, phrases, specific patterns) within the text that signal either present or future context.
Procedure:

Compile comprehensive lexicons (lists) of temporal indicators for PRESENT context (e.g., maintenant, aujourd'hui) and FUTURE context (e.g., demain, bientôt, l'année prochaine, dans X temps). Refer to Table 2. Categorize future indicators by strength (strong/weak) and specificity (definite/indefinite).
Preprocess the text using spaCy (tokenization, lemmatization). Dependency parsing can optionally be performed for scope resolution.
Utilize spaCy's Matcher or PhraseMatcher components to efficiently find occurrences of the lexicon entries in the processed text. Matching on lowercase forms (LOWER) or lemmas (LEMMA) provides robustness against capitalization and inflection.
Define Matcher patterns for more complex temporal expressions involving numbers or specific structures (e.g., `` for "dans jours").
Optionally, identify other future-indicating patterns like specific conjunctions (quand, lorsque) or verbs of planning (prévoir, compter).
Determine the scope of the detected marker. Simplest approach: assume sentence scope. More advanced: use dependency parse information (if available) to link the marker to the specific verb it modifies.


Input: Preprocessed French text segment (tokenized, lemmatized, optionally dependency-parsed).
Output: A list of detected temporal markers found in the text, annotated with their classification (e.g., PRESENT, FUTURE_STRONG_DEF, FUTURE_WEAK_INDEF) and their determined scope (e.g., sentence index, associated verb token index).
Resources:

Curated lexicons of French temporal indicators (based on Table 2 and potentially expanded).
spaCy library, including Matcher and PhraseMatcher.
Optional: spaCy dependency parser.


Example:

Input: "Je pars demain matin."
Output: Marker: demain matin, Class: FUTURE_STRONG_DEF, Scope: Sentence X / Verb pars.
Input: "Il arrivera plus tard."
Output: Marker: plus tard, Class: FUTURE_WEAK_INDEF, Scope: Sentence Y / Verb arrivera.
Input: "Actuellement, nous travaillons sur le projet 2050."
Output: Marker: Actuellement, Class: PRESENT, Scope: Sentence Z / Verb travaillons.


Limitations: Effectiveness depends heavily on the completeness and accuracy of the lexicons. Scope ambiguity remains a challenge, especially for simple implementations (Insight 6). Negation around temporal expressions (e.g., "pas demain") needs specific handling to avoid misclassification. Does not provide a full temporal context classification on its own.
Improvements: Employ dependency parsing for more reliable scope resolution. Continuously refine and expand lexicons, potentially tailoring them to the specific domain of the transcripts. Implement logic to handle negation affecting temporal markers. Use fuzzy matching or embedding similarity for detecting variations not explicitly in the lexicon.
7.4. Technique 4: Combined Rule-Based Disambiguation for Overall Context
Goal: To synthesize evidence from verb tense/form analysis (Technique 1 and/or features from Technique 2) and temporal marker detection (Technique 3) to make a final classification of the overall temporal context (PRESENT vs. FUTURE) for a given text segment (e.g., a sentence), paying special attention to resolving the Présent-for-future ambiguity.
Procedure:

Apply Techniques 1 (rule-based tense ID), 2 (spaCy morph features), and 3 (temporal markers) to the input text segment.
For each sentence (or other defined segment):

Initialize context classification (e.g., to UNKNOWN).
Check for Explicit Future Tenses: If Technique 1 reliably identifies a main verb as FUTURE_SIMPLE or FUTURE_PROCHE, classify the sentence as FUTURE.
Check for Strong Future Markers: If Technique 3 detects a FUTURE_STRONG_DEF marker within the sentence scope, classify as FUTURE. This overrides a PRESENT tense finding from Technique 1/2 (Rule 1 from Section 5.2).
Disambiguate Présent Tense: If the main verb is classified as PRESENT by Technique 1 (or reliably by Technique 2):

Check if any future marker (strong or weak) from Technique 3 is present. If yes, classify as FUTURE (Rules 1 & 2 from Section 5.2).
If no future marker, check if the verb type (motion, scheduling) combined with contextual cues suggests a scheduled event (Rule 3 from Section 5.2). If yes, classify as FUTURE.
If none of the above apply, maintain the PRESENT classification (Rule 4 from Section 5.2).


Handle Conditionnel: If Technique 1 identifies CONDITIONAL, check for future markers from Technique 3. If a future marker is present, classify as FUTURE (acknowledging it's potential/hypothetical future). Otherwise, classify as PRESENT or OTHER (as it likely expresses hypothesis/politeness, not the target future context).
Apply Default/Fallback: If after all checks the classification remains UNKNOWN (e.g., due to ambiguous verb forms and no clear markers), default to PRESENT or classify as AMBIGUOUS.


Implement a weighting or priority system. For example, explicit future tenses and strong future markers carry more weight than weak future markers or verb type heuristics.


Input: The annotated outputs from Techniques 1, 2, and 3 applied to a text segment. The defined rule hierarchy and potentially feature weights.
Output: A final temporal context classification (PRESENT, FUTURE, or AMBIGUOUS) for each processed sentence or segment.
Resources: Outputs from Techniques 1, 2, 3. A clearly defined set of prioritized rules or a simple scoring mechanism.
Example:

Sentence: "Je pars demain." -> T1: pars=PRESENT; T3: demain=FUTURE_STRONG_DEF. -> Rule (Strong Marker): Output = FUTURE.
Sentence: "Elle chante maintenant." -> T1: chante=PRESENT; T3: maintenant=PRESENT. -> Rule (Default Present): Output = PRESENT.
Sentence: "Il arrive." -> T1: arrive=PRESENT (motion verb); T3: No markers. -> Rule (Default Present): Output = PRESENT.
Sentence: "Son train arrive bientôt." -> T1: arrive=PRESENT (scheduling verb); T3: bientôt=FUTURE_WEAK_INDEF. -> Rule (Weak Marker + Verb Type): Output = FUTURE.


Limitations: The complexity of the rule set can become difficult to manage and debug. Interactions between multiple cues, negation, and complex sentence structures can lead to errors. Still vulnerable to unresolved scope issues and reliance on context outside the analyzed segment. Accuracy is highly dependent on the quality and comprehensiveness of the rules and underlying lexicons.
Improvements: Develop a more sophisticated scoring system where different cues contribute weighted evidence towards PRESENT or FUTURE classifications. Explore simple supervised machine learning models (e.g., Logistic Regression, SVM, Naive Bayes) using features derived from Techniques 1, 2, and 3 as input. Incorporate discourse-level analysis to track temporal context across sentences. Perform thorough error analysis on a development set to iteratively refine rules.
8. Conclusion and RecommendationsSummary of Findings: This report has detailed the linguistic mechanisms used in French to express present and future time, focusing on the distinction relevant for classifying context around 2023 versus 2050. Key findings include:
Multiple verb tenses (Présent, Futur Simple, Futur Proche, Conditionnel Présent) are involved, each with specific nuances.
The Présent de l'indicatif poses a significant challenge due to its frequent use for referring to future events, especially near or scheduled ones, necessitating robust disambiguation strategies.4
Standard spaCy French models, while useful for basic processing, exhibit documented inaccuracies in morphological tagging for Futur Simple and Conditionnel Présent forms, rendering them unreliable for direct classification of these tenses.37
Temporal adverbs and specific linguistic patterns provide crucial contextual clues for determining temporal reference.9
Rule-based systems, leveraging linguistic knowledge and contextual analysis, offer a viable approach, although they face challenges related to ambiguity, scope, text formality, and domain specificity.3 Expected accuracy depends heavily on rule quality and evaluation data.
Recommended Approach: Based on the analysis, a purely spaCy-morphology-driven approach is insufficient for reliably distinguishing present and future contexts in French due to specific model limitations. A hybrid approach is recommended, combining spaCy's strengths in preprocessing with custom, linguistically informed rule-based components:
Utilize spaCy for Foundations: Employ spaCy for robust tokenization, sentence segmentation, part-of-speech tagging (identifying verbs), and lemmatization.
Implement Rule-Based Tense Identification: Develop rules based on verb endings and auxiliary patterns to reliably identify Futur Simple and Futur Proche constructions (Technique 1), bypassing reliance on potentially faulty spaCy morphology tags for these tenses.
Leverage Temporal Markers: Create comprehensive lexicons of present and future temporal adverbs/phrases and use spaCy's Matcher for efficient detection (Technique 3).
Focus on Présent Disambiguation: Implement explicit rules prioritizing the co-occurrence of Présent tense verbs with future temporal markers or specific verb/context types to resolve ambiguity (Technique 4, incorporating rules from Section 5.2).
Use spaCy Morphology Cautiously: Treat spaCy's morphological features for Tense=Fut and Mood=Cnd primarily as supplementary signals or features within the combined rule system, rather than definitive classifiers (Technique 2).
Future Directions:
Task-Specific Evaluation Corpus: The development or adaptation of a French corpus specifically annotated for the Present (2023) vs. Future (2050) context distinction, representative of the target transcript data, is highly recommended for meaningful system evaluation and refinement.53
Machine Learning Integration: Explore using the outputs of the rule-based components (detected tenses, markers, ambiguity flags) as features for simple supervised machine learning classifiers (e.g., Logistic Regression, SVM) to potentially improve generalization and handle complex cue interactions.
Advanced Models: Investigate the potential of fine-tuning large language models (LLMs) or transformer-based architectures specifically for this temporal classification task in French, which might capture nuances missed by purely rule-based systems. Recent work explores LLMs for related tasks like temporal normalization.56
Scope Resolution: Further research into more advanced techniques for resolving the scope of temporal modifiers within sentences could significantly improve accuracy, potentially using dependency parsing or relation extraction methods.
