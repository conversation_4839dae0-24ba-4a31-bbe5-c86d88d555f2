# ML Pipeline Requirements
# Additional dependencies for the enhanced ML capabilities

# Topic Modeling
bertopic>=0.15.0
sentence-transformers>=2.2.0
umap-learn>=0.5.3
hdbscan>=0.8.29

# Semantic Search
faiss-cpu>=1.7.4
# For GPU support, use: faiss-gpu>=1.7.4

# French Language Processing
spacy>=3.6.0
# Download French model with: python -m spacy download fr_core_news_lg

# Machine Learning & Data Science
scikit-learn>=1.3.0
numpy>=1.24.0
pandas>=2.0.0

# Visualization (for topic modeling)
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Utilities
tqdm>=4.65.0
joblib>=1.3.0

# Excel Export
openpyxl>=3.1.0

# Optional: For advanced optimization
optuna>=3.2.0

# Optional: For model deployment
fastapi>=0.100.0
uvicorn>=0.22.0

# Development & Testing
pytest>=7.4.0
pytest-cov>=4.1.0
