"""
Threshold Tuning for Imbalanced Classification
=============================================

This module implements threshold tuning to optimize classification performance
for imbalanced datasets, specifically targeting the severe class imbalance
in thematic classification (89.4% Performance vs 10.6% Légitimité).

Key Features:
- Optimal threshold search using validation data
- Multiple optimization metrics (F1, Precision-Recall balance)
- Integration with existing model pipeline
- Minimal computational overhead

Author: ML Engineering Team
Date: 2025-06-14
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from sklearn.metrics import (
    precision_recall_curve, f1_score, precision_score, 
    recall_score, accuracy_score, classification_report
)
import logging

logger = logging.getLogger(__name__)


class ThresholdTuner:
    """
    Threshold tuning for binary classification with class imbalance.
    
    Optimizes decision threshold to improve minority class detection
    while maintaining overall performance.
    """
    
    def __init__(self, optimization_metric: str = 'f1_macro'):
        """
        Initialize threshold tuner.
        
        Args:
            optimization_metric: Metric to optimize ('f1_macro', 'f1_minority', 'balanced_accuracy')
        """
        self.optimization_metric = optimization_metric
        self.best_threshold = 0.5
        self.threshold_results = {}
        self.is_tuned = False
        
        logger.info(f"Initialized ThresholdTuner with metric: {optimization_metric}")
    
    def find_optimal_threshold(self, y_true: np.ndarray, y_proba: np.ndarray, 
                              class_names: List[str] = None) -> Dict[str, Any]:
        """
        Find optimal threshold using validation data.
        
        Args:
            y_true: True labels
            y_proba: Predicted probabilities (for positive class)
            class_names: Names of classes for reporting
            
        Returns:
            Dictionary with tuning results
        """
        logger.info("Starting threshold tuning optimization...")
        
        if class_names is None:
            class_names = ['Class_0', 'Class_1']
        
        # Get probabilities for positive class (minority class)
        if y_proba.ndim > 1:
            y_proba_pos = y_proba[:, 1]  # Probability of positive class
        else:
            y_proba_pos = y_proba
        
        # Generate threshold candidates
        thresholds = np.linspace(0.1, 0.9, 81)  # 0.1 to 0.9 in steps of 0.01
        
        results = []
        best_score = -1
        best_threshold = 0.5
        
        for threshold in thresholds:
            # Make predictions with current threshold
            y_pred = (y_proba_pos >= threshold).astype(int)
            
            # Calculate metrics
            accuracy = accuracy_score(y_true, y_pred)
            precision = precision_score(y_true, y_pred, average='macro', zero_division=0)
            recall = recall_score(y_true, y_pred, average='macro', zero_division=0)
            f1_macro = f1_score(y_true, y_pred, average='macro', zero_division=0)
            f1_minority = f1_score(y_true, y_pred, pos_label=1, zero_division=0)
            
            # Calculate class-specific metrics
            try:
                class_report = classification_report(y_true, y_pred, target_names=class_names, 
                                                   output_dict=True, zero_division=0)
                minority_precision = class_report[class_names[1]]['precision']
                minority_recall = class_report[class_names[1]]['recall']
                majority_f1 = class_report[class_names[0]]['f1-score']
                minority_f1 = class_report[class_names[1]]['f1-score']
            except:
                minority_precision = minority_recall = majority_f1 = minority_f1 = 0.0
            
            # Calculate optimization score
            if self.optimization_metric == 'f1_macro':
                score = f1_macro
            elif self.optimization_metric == 'f1_minority':
                score = f1_minority
            elif self.optimization_metric == 'balanced_accuracy':
                score = (recall + precision) / 2
            else:
                score = f1_macro
            
            # Store results
            result = {
                'threshold': threshold,
                'accuracy': accuracy,
                'precision_macro': precision,
                'recall_macro': recall,
                'f1_macro': f1_macro,
                'f1_minority': f1_minority,
                'minority_precision': minority_precision,
                'minority_recall': minority_recall,
                'majority_f1': majority_f1,
                'optimization_score': score
            }
            results.append(result)
            
            # Update best threshold
            if score > best_score:
                best_score = score
                best_threshold = threshold
        
        # Store results
        self.threshold_results = pd.DataFrame(results)
        self.best_threshold = best_threshold
        self.is_tuned = True
        
        # Log results
        best_result = self.threshold_results.loc[self.threshold_results['optimization_score'].idxmax()]
        logger.info(f"Optimal threshold found: {best_threshold:.3f}")
        logger.info(f"Best {self.optimization_metric}: {best_score:.4f}")
        logger.info(f"Minority class F1 improvement: {best_result['f1_minority']:.4f}")
        logger.info(f"Accuracy at optimal threshold: {best_result['accuracy']:.4f}")
        
        return {
            'best_threshold': best_threshold,
            'best_score': best_score,
            'results_df': self.threshold_results,
            'best_result': best_result.to_dict()
        }
    
    def predict_with_threshold(self, y_proba: np.ndarray, threshold: float = None) -> np.ndarray:
        """
        Make predictions using specified or optimal threshold.
        
        Args:
            y_proba: Predicted probabilities
            threshold: Custom threshold (uses optimal if None)
            
        Returns:
            Binary predictions
        """
        if threshold is None:
            if not self.is_tuned:
                logger.warning("Threshold not tuned yet, using default 0.5")
                threshold = 0.5
            else:
                threshold = self.best_threshold
        
        # Get probabilities for positive class
        if y_proba.ndim > 1:
            y_proba_pos = y_proba[:, 1]
        else:
            y_proba_pos = y_proba
        
        return (y_proba_pos >= threshold).astype(int)
    
    def evaluate_with_tuning(self, y_true: np.ndarray, y_proba: np.ndarray, 
                           class_names: List[str] = None) -> Dict[str, Any]:
        """
        Complete evaluation with threshold tuning.
        
        Args:
            y_true: True labels
            y_proba: Predicted probabilities
            class_names: Names of classes for reporting
            
        Returns:
            Evaluation results with both default and tuned thresholds
        """
        if class_names is None:
            class_names = ['Class_0', 'Class_1']
        
        # Default threshold (0.5) results
        y_pred_default = self.predict_with_threshold(y_proba, threshold=0.5)
        default_results = {
            'accuracy': accuracy_score(y_true, y_pred_default),
            'f1_macro': f1_score(y_true, y_pred_default, average='macro', zero_division=0),
            'f1_minority': f1_score(y_true, y_pred_default, pos_label=1, zero_division=0),
            'classification_report': classification_report(y_true, y_pred_default, 
                                                         target_names=class_names, 
                                                         output_dict=True, zero_division=0)
        }
        
        # Tune threshold
        tuning_results = self.find_optimal_threshold(y_true, y_proba, class_names)
        
        # Optimal threshold results
        y_pred_tuned = self.predict_with_threshold(y_proba, threshold=self.best_threshold)
        tuned_results = {
            'accuracy': accuracy_score(y_true, y_pred_tuned),
            'f1_macro': f1_score(y_true, y_pred_tuned, average='macro', zero_division=0),
            'f1_minority': f1_score(y_true, y_pred_tuned, pos_label=1, zero_division=0),
            'classification_report': classification_report(y_true, y_pred_tuned, 
                                                         target_names=class_names, 
                                                         output_dict=True, zero_division=0)
        }
        
        # Calculate improvements
        improvements = {
            'accuracy_improvement': tuned_results['accuracy'] - default_results['accuracy'],
            'f1_macro_improvement': tuned_results['f1_macro'] - default_results['f1_macro'],
            'f1_minority_improvement': tuned_results['f1_minority'] - default_results['f1_minority']
        }
        
        return {
            'default_threshold_results': default_results,
            'tuned_threshold_results': tuned_results,
            'threshold_tuning': tuning_results,
            'improvements': improvements,
            'best_threshold': self.best_threshold
        }
    
    def get_threshold_analysis(self) -> pd.DataFrame:
        """
        Get detailed threshold analysis results.
        
        Returns:
            DataFrame with threshold analysis
        """
        if not self.is_tuned:
            logger.warning("Threshold tuning not performed yet")
            return pd.DataFrame()
        
        return self.threshold_results.copy()


def apply_threshold_tuning_to_model(model, X_val: List[str], y_val: List[int], 
                                   X_test: List[str], y_test: List[int],
                                   class_names: List[str] = None,
                                   optimization_metric: str = 'f1_macro') -> Dict[str, Any]:
    """
    Apply threshold tuning to an existing trained model.
    
    Args:
        model: Trained model with predict_proba method
        X_val: Validation texts for threshold tuning
        y_val: Validation labels for threshold tuning
        X_test: Test texts for final evaluation
        y_test: Test labels for final evaluation
        class_names: Names of classes
        optimization_metric: Metric to optimize
        
    Returns:
        Complete evaluation results with threshold tuning
    """
    logger.info("Applying threshold tuning to trained model...")
    
    # Get validation probabilities for tuning
    val_probabilities = model.predict_proba(X_val)
    
    # Initialize tuner and find optimal threshold
    tuner = ThresholdTuner(optimization_metric=optimization_metric)
    tuning_results = tuner.find_optimal_threshold(np.array(y_val), val_probabilities, class_names)
    
    # Evaluate on test set with both thresholds
    test_probabilities = model.predict_proba(X_test)
    evaluation_results = tuner.evaluate_with_tuning(np.array(y_test), test_probabilities, class_names)
    
    # Add model-specific information
    evaluation_results['model_type'] = type(model).__name__
    evaluation_results['optimization_metric'] = optimization_metric
    
    return evaluation_results
