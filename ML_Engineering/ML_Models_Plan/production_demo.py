"""
Production ML System Demo
========================

This script demonstrates the complete production-ready ML system
for tension detection and thematic classification.

Run this script to validate that your production system is working correctly.

Author: ML Engineering Team
Date: 2025-06-14
"""

import sys
import os
from pathlib import Path
import json
import time
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from production_ml_api import ProductionMLAPI
from validate_production_models import ProductionModelValidator


def print_header(title: str):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)


def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n🔹 {title}")
    print("-" * 40)


def demo_production_system():
    """
    Complete demonstration of the production ML system.
    """
    print_header("PRODUCTION ML SYSTEM DEMONSTRATION")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Status: Production Ready ✅")
    
    # Step 1: Validate Models
    print_section("Step 1: Model Validation")
    validator = ProductionModelValidator()
    validation_results = validator.validate_all_models()
    
    if validation_results['production_ready']:
        print("✅ All models validated successfully!")
        print(f"   - Models validated: {validation_results['models_validated']}")
        print(f"   - Models passed: {validation_results['models_passed']}")
    else:
        print("❌ Model validation failed!")
        return False
    
    # Step 2: Initialize Production API
    print_section("Step 2: Initialize Production API")
    try:
        api = ProductionMLAPI()
        load_results = api.load_models()
        
        if all(load_results.values()):
            print("✅ Production API initialized successfully!")
            for model, status in load_results.items():
                print(f"   - {model}: {'✅ Loaded' if status else '❌ Failed'}")
        else:
            print("❌ Failed to load some models!")
            return False
            
    except Exception as e:
        print(f"❌ API initialization failed: {str(e)}")
        return False
    
    # Step 3: Demo Predictions
    print_section("Step 3: Demonstration Predictions")
    
    # Sample texts for demonstration
    demo_texts = [
        "This document discusses performance metrics and efficiency improvements in our quarterly review.",
        "The legitimacy of this decision is highly questionable and raises serious concerns about governance.",
        "There is significant tension between stakeholders regarding the proposed changes to the policy framework!",
        "Our performance indicators show steady improvement across all key metrics this quarter.",
        "The legal legitimacy of these procedures must be thoroughly examined before implementation."
    ]
    
    print("📝 Demo texts:")
    for i, text in enumerate(demo_texts, 1):
        print(f"   {i}. {text[:60]}...")
    
    # Tension Detection Demo
    print_section("Tension Detection Results")
    try:
        start_time = time.time()
        tension_results = api.predict_tension(demo_texts)
        end_time = time.time()
        
        print(f"⏱️  Processing time: {end_time - start_time:.2f} seconds")
        print(f"📊 Model: {tension_results['model']}")
        print(f"🎯 Performance: {tension_results['performance']['accuracy']:.1%} accuracy")
        
        print("\n📋 Predictions:")
        for i, pred in enumerate(tension_results['predictions'], 1):
            status = "🔴 TENSION" if pred['prediction'] == 'tension' else "🟢 NO TENSION"
            confidence = pred['confidence']
            print(f"   {i}. {status} (confidence: {confidence:.1%})")
            
    except Exception as e:
        print(f"❌ Tension detection failed: {str(e)}")
        return False
    
    # Thematic Classification Demo
    print_section("Thematic Classification Results")
    try:
        start_time = time.time()
        thematic_results = api.predict_thematic(demo_texts)
        end_time = time.time()
        
        print(f"⏱️  Processing time: {end_time - start_time:.2f} seconds")
        print(f"📊 Model: {thematic_results['model']}")
        print(f"🎯 Performance: {thematic_results['performance']['f1_macro']:.1%} F1 Macro")
        print(f"🔧 Threshold: {thematic_results['performance']['optimal_threshold']}")
        
        print("\n📋 Predictions:")
        for i, pred in enumerate(thematic_results['predictions'], 1):
            theme = pred['tuned_prediction']
            confidence = pred['confidence']
            emoji = "⚖️" if theme == "Légitimité" else "📈"
            print(f"   {i}. {emoji} {theme} (confidence: {confidence:.1%})")
            
    except Exception as e:
        print(f"❌ Thematic classification failed: {str(e)}")
        return False
    
    # Combined Prediction Demo
    print_section("Combined Prediction Example")
    try:
        sample_text = demo_texts[2]  # The tension text
        print(f"📝 Sample text: {sample_text}")
        
        combined_result = api.predict_both(sample_text)
        
        tension_pred = combined_result['predictions'][0]['tension_detection']
        thematic_pred = combined_result['predictions'][0]['thematic_classification']
        
        print(f"\n🔍 Combined Analysis:")
        print(f"   🔴 Tension: {tension_pred['prediction']} ({tension_pred['confidence']:.1%})")
        print(f"   ⚖️  Theme: {thematic_pred['tuned_prediction']} ({thematic_pred['confidence']:.1%})")
        
    except Exception as e:
        print(f"❌ Combined prediction failed: {str(e)}")
        return False
    
    # Step 4: Performance Summary
    print_section("Step 4: Performance Summary")
    model_info = api.get_model_info()
    
    print("📊 Production Models:")
    for model_name, info in model_info['models'].items():
        if info['status'] == 'loaded':
            print(f"   ✅ {model_name.replace('_', ' ').title()}")
            print(f"      - Type: {info['performance']['type']}")
            if 'accuracy' in info['performance']:
                print(f"      - Accuracy: {info['performance']['accuracy']:.1%}")
            if 'f1_macro' in info['performance']:
                print(f"      - F1 Macro: {info['performance']['f1_macro']:.1%}")
    
    # Step 5: API Usage Examples
    print_section("Step 5: API Usage Examples")
    print("💻 Python Code Examples:")
    print("""
    # Initialize API
    from production_ml_api import ProductionMLAPI
    api = ProductionMLAPI()
    api.load_models()
    
    # Single prediction
    result = api.predict_tension("Your text here")
    
    # Batch prediction
    texts = ["Text 1", "Text 2", "Text 3"]
    results = api.predict_both(texts)
    
    # Get model information
    info = api.get_model_info()
    """)
    
    # Success Summary
    print_header("PRODUCTION SYSTEM VALIDATION COMPLETE")
    print("🎉 SUCCESS: Production ML system is fully operational!")
    print("\n📋 System Status:")
    print("   ✅ Models validated and loaded")
    print("   ✅ Tension detection working (97.54% accuracy)")
    print("   ✅ Thematic classification working (77.27% F1 Macro)")
    print("   ✅ Combined predictions working")
    print("   ✅ API ready for integration")
    
    print("\n🚀 Next Steps:")
    print("   1. Integrate API into your application")
    print("   2. Set up monitoring and logging")
    print("   3. Deploy to production environment")
    print("   4. Monitor performance and retrain as needed")
    
    print(f"\n📚 Documentation:")
    print(f"   - Deployment Guide: DEPLOYMENT_GUIDE.md")
    print(f"   - Production Summary: PRODUCTION_READY_SUMMARY.md")
    print(f"   - Model Validation: validate_production_models.py")
    
    return True


def main():
    """
    Main function to run the production demo.
    """
    try:
        success = demo_production_system()
        
        if success:
            print("\n" + "🎯" * 20)
            print("PRODUCTION SYSTEM READY FOR DEPLOYMENT!")
            print("🎯" * 20)
            return 0
        else:
            print("\n" + "❌" * 20)
            print("PRODUCTION SYSTEM VALIDATION FAILED!")
            print("❌" * 20)
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
        return 1
    except Exception as e:
        print(f"\n\n💥 Unexpected error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
