# Thematic Classification Implementation Roadmap

## Phase 1: Simple Models (Priority 1) ⚡
**Goal**: Quick baseline establishment with fast training times

### 1.1 TF-IDF Based Models
- [ ] **Logistic Regression + TF-IDF**
  - Training time: < 10s
  - Purpose: Solid lexical baseline
  - Implementation: `ThematicLogisticRegressionModel`
  
- [ ] **Naïve Bayes + TF-IDF** 
  - Training time: < 10s
  - Purpose: Probabilistic baseline
  - Implementation: `ThematicNaiveBayesModel`

### 1.2 Validation Setup
- [ ] Implement RankScore metric: `0.7 × Macro-F1 + 0.3 × Accuracy`
- [ ] Class imbalance handling with computed weights
- [ ] Cross-validation framework
- [ ] Basic evaluation metrics and plots

**Expected Outcome**: Working baseline in ~1 hour of development

---

## Phase 2: Semantic Models (Priority 2) 🧠
**Goal**: Leverage semantic understanding while keeping training fast

### 2.1 MiniLM Integration
- [ ] **MiniLM + Linear SVM**
  - Training time: ≈ 1 min
  - Model: `sentence-transformers/all-MiniLM-L6-v2`
  - Implementation: `ThematicMiniLMSVMModel`
  - Calibrated probabilities for ensemble potential

### 2.2 Optimization
- [ ] Optuna hyperparameter optimization
- [ ] Model comparison framework
- [ ] Performance benchmarking

**Expected Outcome**: Semantic baseline ready in ~2-3 hours

---

## Phase 3: Advanced Models (Priority 3) 🚀
**Goal**: Push accuracy boundaries (--deep flag)

### 3.1 Transformer Fine-tuning
- [ ] **Sentence-BERT Fine-tune**
  - Training time: 15-30 min
  - Currently disabled due to Windows issues
  - Requires: GPU resources + Linux/WSL environment
  
- [ ] **CamemBERT Fine-tune**
  - Training time: 1-2 hours
  - French-specific transformer
  - Currently disabled due to environment issues

### 3.2 Infrastructure Requirements
- [ ] Resolve HuggingFace cache issues on Windows
- [ ] Upgrade HF stack: `transformers 4.41+`, `sentence-transformers 2.6+`
- [ ] GPU environment setup
- [ ] Memory optimization (>8GB RAM requirement)

**Expected Outcome**: State-of-the-art models when infrastructure ready

---

## Implementation Strategy

### Immediate Actions (This Week)
1. **Start with Phase 1**: Implement TF-IDF models first
2. **Test existing infrastructure**: Use `train_thematic_models.py`
3. **Validate data pipeline**: Ensure data loading works correctly
4. **Establish evaluation framework**: RankScore + standard metrics

### Development Workflow
```bash
# Quick test with simple models
python train_thematic_models.py --table Table_A

# Full evaluation when ready
python train_thematic_models.py --table Table_A --deep
```

### Success Criteria
- [ ] Phase 1: Working baseline with <10s training time
- [ ] Phase 2: Semantic model with ~1min training time  
- [ ] Phase 3: Advanced models when infrastructure allows
- [ ] All models: RankScore > 0.8 target
- [ ] Consistent evaluation across all model types

---

## Technical Considerations

### Current Constraints
- Windows environment limitations
- HuggingFace cache compatibility issues
- Memory constraints for deep models
- No GPU acceleration currently

### Workarounds Implemented
- Default cache path usage (avoid custom cache_folder)
- Tuple-to-string conversion for Optuna
- Class weight computation for imbalance
- Optional deep models with --deep flag

### Next Infrastructure Upgrades
1. HuggingFace stack upgrade
2. Linux/WSL environment for deep models
3. GPU access for transformer fine-tuning
4. Memory optimization strategies

---

*This roadmap follows the iterative simple-to-complex approach as requested, prioritizing quick wins and gradual complexity increase.*
