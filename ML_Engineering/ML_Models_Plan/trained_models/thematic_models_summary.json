{"timestamp": "2025-06-14T21:04:54.221322", "data_statistics": {"total_samples": 302, "train_samples": 210, "val_samples": 31, "test_samples": 61, "class_distribution": {"train": [14, 196], "val": [2, 29], "test": [4, 57]}}, "model_performance": [{"Model": "Logistic Regression", "Test_Accuracy": 0.7868852459016393, "Val_Accuracy": 1.0, "Macro_F1": 0.5557422969187675, "Weighted_F1": 0.8341644854663176, "Performance_F1": 0.8761904761904762, "Legitimacy_F1": 0.23529411764705882, "Status": "Success"}, {"Model": "<PERSON><PERSON>", "Test_Accuracy": 0.9344262295081968, "Val_Accuracy": 0.9354838709677419, "Macro_F1": 0.4830508474576271, "Weighted_F1": 0.9027507641011392, "Performance_F1": 0.9661016949152542, "Legitimacy_F1": 0.0, "Status": "Success"}, {"Model": "Minilm Svm", "Test_Accuracy": 0.9344262295081968, "Val_Accuracy": 0.9354838709677419, "Macro_F1": 0.4830508474576271, "Weighted_F1": 0.9027507641011392, "Performance_F1": 0.9661016949152542, "Legitimacy_F1": 0.0, "Status": "Success"}], "best_model": {"name": "Logistic Regression", "accuracy": 0.7868852459016393, "target_achieved": false}, "target_metrics": {"target_range": [0.85, 0.95], "class_balance_challenge": "Performance: 89.4%, Légitimité: 10.6%"}}