"""
XGBoost with SMOTE for Thematic Classification
=============================================

This module implements XGBoost with SMOTE oversampling specifically designed
for imbalanced thematic classification (Performance vs Légitimité).

Key Features:
- Embedding-based SMOTE for semantic text oversampling
- XGBoost with optimized class imbalance handling
- Optuna hyperparameter optimization
- Integration with existing pipeline

Technical Approach:
Text → MiniLM Embeddings → SMOTE → XGBoost

Author: ML Engineering Team
Date: 2025-06-14
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path

# ML imports
import xgboost as xgb
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sentence_transformers import SentenceTransformer
from imblearn.over_sampling import SMOTE
import optuna
import joblib

logger = logging.getLogger(__name__)


class ThematicXGBoostSMOTEModel:
    """
    XGBoost model with SMOTE oversampling for thematic classification.
    
    Uses semantic embeddings from MiniLM as input to SMOTE for generating
    realistic synthetic minority class samples, then trains XGBoost.
    """
    
    def __init__(self, embedding_model: str = 'sentence-transformers/all-MiniLM-L6-v2',
                 random_state: int = 42):
        """
        Initialize the XGBoost + SMOTE model.
        
        Args:
            embedding_model: SentenceTransformer model for embeddings
            random_state: Random state for reproducibility
        """
        self.embedding_model_name = embedding_model
        self.random_state = random_state
        self.model = None
        self.encoder = None
        self.smote = None
        self.best_params = None
        self.is_trained = False
        
        # Initialize sentence transformer with CPU fallback
        try:
            self.encoder = SentenceTransformer(embedding_model, device='cpu')
            logger.info(f"Successfully initialized XGBoost-SMOTE with {embedding_model} on CPU")
        except Exception as e:
            logger.error(f"Failed to initialize SentenceTransformer: {e}")
            raise RuntimeError(f"Cannot initialize SentenceTransformer: {e}")
    
    def _encode_texts(self, texts: List[str]) -> np.ndarray:
        """
        Convert texts to embeddings.
        
        Args:
            texts: List of text strings
            
        Returns:
            Embedding matrix (n_samples, embedding_dim)
        """
        return self.encoder.encode(texts, convert_to_numpy=True, show_progress_bar=False, batch_size=32)
    
    def _apply_smote(self, X_embeddings: np.ndarray, y: np.ndarray) -> tuple:
        """
        Apply SMOTE oversampling to embeddings.
        
        Args:
            X_embeddings: Text embeddings
            y: Labels
            
        Returns:
            Tuple of (X_resampled, y_resampled)
        """
        # Initialize SMOTE with appropriate strategy
        self.smote = SMOTE(
            sampling_strategy='auto',  # Balance all classes
            random_state=self.random_state,
            k_neighbors=min(5, np.sum(y == np.bincount(y).argmin()) - 1)  # Adjust for small minority class
        )
        
        logger.info(f"Applying SMOTE to embeddings. Original shape: {X_embeddings.shape}")
        logger.info(f"Original class distribution: {np.bincount(y)}")
        
        X_resampled, y_resampled = self.smote.fit_resample(X_embeddings, y)
        
        logger.info(f"After SMOTE shape: {X_resampled.shape}")
        logger.info(f"After SMOTE class distribution: {np.bincount(y_resampled)}")
        
        return X_resampled, y_resampled
    
    def objective(self, trial, X_train: np.ndarray, y_train: np.ndarray,
                  X_val: np.ndarray, y_val: np.ndarray) -> float:
        """
        Optuna objective function for hyperparameter optimization.
        
        Args:
            trial: Optuna trial object
            X_train: Training embeddings
            y_train: Training labels
            X_val: Validation embeddings
            y_val: Validation labels
            
        Returns:
            Validation accuracy score
        """
        # Suggest hyperparameters
        params = {
            'objective': 'binary:logistic',
            'eval_metric': 'logloss',
            'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
            'max_depth': trial.suggest_int('max_depth', 3, 10),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
            'subsample': trial.suggest_float('subsample', 0.6, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
            'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
            'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
            'random_state': self.random_state,
            'verbosity': 0
        }
        
        # Calculate scale_pos_weight for class imbalance
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        if pos_count > 0:
            params['scale_pos_weight'] = neg_count / pos_count
        
        # Train model with suggested parameters
        model = xgb.XGBClassifier(**params)
        model.fit(X_train, y_train, eval_set=[(X_val, y_val)], verbose=False)
        
        # Predict on validation set
        y_pred = model.predict(X_val)
        accuracy = accuracy_score(y_val, y_pred)
        
        return accuracy
    
    def optimize_hyperparameters(self, X_train: np.ndarray, y_train: np.ndarray,
                                X_val: np.ndarray, y_val: np.ndarray,
                                n_trials: int = 50) -> Dict[str, Any]:
        """
        Optimize hyperparameters using Optuna.
        
        Args:
            X_train: Training embeddings
            y_train: Training labels
            X_val: Validation embeddings
            y_val: Validation labels
            n_trials: Number of optimization trials
            
        Returns:
            Dictionary with optimization results
        """
        logger.info(f"Starting XGBoost hyperparameter optimization with {n_trials} trials...")
        
        # Create study
        study = optuna.create_study(direction='maximize')
        
        # Optimize
        study.optimize(
            lambda trial: self.objective(trial, X_train, y_train, X_val, y_val),
            n_trials=n_trials
        )
        
        # Store best parameters
        self.best_params = study.best_params
        
        logger.info(f"XGBoost optimization completed. Best accuracy: {study.best_value:.4f}")
        logger.info(f"Best parameters: {self.best_params}")
        
        return {
            'best_params': self.best_params,
            'best_score': study.best_value,
            'study': study
        }
    
    def train(self, X_train: List[str], y_train: List[int],
              X_val: List[str] = None, y_val: List[int] = None,
              class_weights: Dict[int, float] = None,
              optimize: bool = True, n_trials: int = 50) -> Dict[str, Any]:
        """
        Train the XGBoost + SMOTE model.
        
        Args:
            X_train: Training texts
            y_train: Training labels
            X_val: Validation texts (optional)
            y_val: Validation labels (optional)
            class_weights: Class weights (not used, SMOTE handles imbalance)
            optimize: Whether to run hyperparameter optimization
            n_trials: Number of optimization trials
            
        Returns:
            Training results dictionary
        """
        logger.info("Training XGBoost + SMOTE model for thematic classification...")
        
        # Convert texts to embeddings
        logger.info("Converting texts to embeddings...")
        X_train_embeddings = self._encode_texts(X_train)
        
        if X_val is not None:
            X_val_embeddings = self._encode_texts(X_val)
        
        # Apply SMOTE to training data
        logger.info("Applying SMOTE oversampling...")
        X_train_resampled, y_train_resampled = self._apply_smote(
            X_train_embeddings, np.array(y_train)
        )
        
        results = {}
        
        if optimize and X_val is not None and y_val is not None:
            # Run hyperparameter optimization
            opt_results = self.optimize_hyperparameters(
                X_train_resampled, y_train_resampled, 
                X_val_embeddings, np.array(y_val), 
                n_trials
            )
            results.update(opt_results)
            
            # Use best parameters
            params = self.best_params.copy()
        else:
            # Use default parameters
            params = {
                'objective': 'binary:logistic',
                'eval_metric': 'logloss',
                'n_estimators': 300,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 1.0,
                'reg_lambda': 1.0,
                'random_state': self.random_state,
                'verbosity': 0
            }
        
        # Calculate scale_pos_weight for remaining imbalance
        neg_count = np.sum(y_train_resampled == 0)
        pos_count = np.sum(y_train_resampled == 1)
        if pos_count > 0:
            params['scale_pos_weight'] = neg_count / pos_count
        
        # Train final model
        self.model = xgb.XGBClassifier(**params)
        
        if X_val is not None and y_val is not None:
            self.model.fit(
                X_train_resampled, y_train_resampled,
                eval_set=[(X_val_embeddings, np.array(y_val))],
                verbose=False
            )
        else:
            self.model.fit(X_train_resampled, y_train_resampled)
        
        self.is_trained = True
        
        # Evaluate on training set
        train_pred = self.model.predict(X_train_resampled)
        train_accuracy = accuracy_score(y_train_resampled, train_pred)
        
        results.update({
            'train_accuracy': train_accuracy,
            'smote_samples_generated': len(y_train_resampled) - len(y_train),
            'final_class_distribution': np.bincount(y_train_resampled).tolist()
        })
        
        # Evaluate on validation set if provided
        if X_val is not None and y_val is not None:
            val_pred = self.model.predict(X_val_embeddings)
            val_accuracy = accuracy_score(y_val, val_pred)
            results['val_accuracy'] = val_accuracy
            
            logger.info(f"XGBoost + SMOTE training completed - Train Acc: {train_accuracy:.4f}, Val Acc: {val_accuracy:.4f}")
        else:
            logger.info(f"XGBoost + SMOTE training completed - Train Acc: {train_accuracy:.4f}")
        
        return results
    
    def predict(self, X: List[str]) -> np.ndarray:
        """Make predictions on new texts."""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        X_embeddings = self._encode_texts(X)
        return self.model.predict(X_embeddings)
    
    def predict_proba(self, X: List[str]) -> np.ndarray:
        """Get prediction probabilities."""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        X_embeddings = self._encode_texts(X)
        return self.model.predict_proba(X_embeddings)
    
    def evaluate(self, X_test: List[str], y_test: List[int], 
                class_names: List[str] = None) -> Dict[str, Any]:
        """
        Evaluate the model on test data.
        
        Args:
            X_test: Test texts
            y_test: Test labels
            class_names: Names of classes for reporting
            
        Returns:
            Evaluation results
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        logger.info("Evaluating XGBoost + SMOTE model...")
        
        # Make predictions
        y_pred = self.predict(X_test)
        y_proba = self.predict_proba(X_test)
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        conf_matrix = confusion_matrix(y_test, y_pred)
        class_report = classification_report(y_test, y_pred, target_names=class_names, 
                                           output_dict=True, zero_division=0)
        
        results = {
            'accuracy': accuracy,
            'confusion_matrix': conf_matrix,
            'classification_report': class_report,
            'predictions': y_pred,
            'probabilities': y_proba
        }
        
        logger.info(f"XGBoost + SMOTE test accuracy: {accuracy:.4f}")
        
        return results
    
    def save_model(self, filepath: str):
        """Save the trained model to disk."""
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        
        model_data = {
            'xgboost_model': self.model,
            'encoder_model_name': self.embedding_model_name,
            'smote': self.smote,
            'best_params': self.best_params,
            'random_state': self.random_state
        }
        
        joblib.dump(model_data, filepath)
        logger.info(f"XGBoost + SMOTE model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load a trained model from disk."""
        model_data = joblib.load(filepath)
        
        self.model = model_data['xgboost_model']
        self.embedding_model_name = model_data['encoder_model_name']
        self.smote = model_data['smote']
        self.best_params = model_data['best_params']
        self.random_state = model_data['random_state']
        
        # Reinitialize encoder
        self.encoder = SentenceTransformer(self.embedding_model_name, device='cpu')
        self.is_trained = True
        
        logger.info(f"XGBoost + SMOTE model loaded from {filepath}")
