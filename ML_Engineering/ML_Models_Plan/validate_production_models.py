"""
Production Model Validation and Documentation
============================================

This script validates all production-ready models and generates
comprehensive metadata for deployment.

Author: ML Engineering Team
Date: 2025-06-14
"""

import sys
import os
from pathlib import Path
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime
import joblib
from typing import Dict, Any, List

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProductionModelValidator:
    """
    Validates and documents production-ready models.
    """
    
    def __init__(self, models_dir: str = "trained_models"):
        """
        Initialize the validator.
        
        Args:
            models_dir: Directory containing trained models
        """
        self.models_dir = Path(models_dir)
        self.validation_results = {}
        
    def validate_all_models(self) -> Dict[str, Any]:
        """
        Validate all production models and generate metadata.
        
        Returns:
            Comprehensive validation results
        """
        logger.info("🔍 Starting production model validation...")
        
        # Define production models
        production_models = {
            'tension_detection': {
                'file': 'tension_random_forest_full.joblib',
                'type': 'Random Forest',
                'task': 'Binary Classification (Tension Detection)',
                'expected_performance': {'accuracy': 0.9754},
                'training_date': '2025-06-14',
                'features': 24,
                'samples': 1827
            },
            'thematic_classification': {
                'file': 'minilm_svm_tuned.joblib',
                'type': 'MiniLM SVM with Threshold Tuning',
                'task': 'Binary Classification (Performance vs Légitimité)',
                'expected_performance': {'f1_macro': 0.7727, 'accuracy': 0.9508},
                'training_date': '2025-06-14',
                'optimal_threshold': 0.64,
                'embedding_model': 'sentence-transformers/all-MiniLM-L6-v2',
                'samples': 302
            }
        }
        
        validation_summary = {
            'validation_date': datetime.now().isoformat(),
            'models_validated': 0,
            'models_passed': 0,
            'models_failed': 0,
            'production_ready': False,
            'models': {}
        }
        
        # Validate each model
        for model_name, model_info in production_models.items():
            logger.info(f"🔍 Validating {model_name}...")
            
            validation_result = self._validate_single_model(model_name, model_info)
            validation_summary['models'][model_name] = validation_result
            validation_summary['models_validated'] += 1
            
            if validation_result['status'] == 'PASSED':
                validation_summary['models_passed'] += 1
                logger.info(f"✅ {model_name} validation PASSED")
            else:
                validation_summary['models_failed'] += 1
                logger.error(f"❌ {model_name} validation FAILED")
        
        # Overall status
        validation_summary['production_ready'] = validation_summary['models_failed'] == 0
        
        # Save validation results
        self._save_validation_results(validation_summary)
        
        logger.info(f"🎯 Validation complete: {validation_summary['models_passed']}/{validation_summary['models_validated']} models passed")
        
        return validation_summary
    
    def _validate_single_model(self, model_name: str, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a single model.
        
        Args:
            model_name: Name of the model
            model_info: Model information dictionary
            
        Returns:
            Validation results for the model
        """
        result = {
            'model_name': model_name,
            'file_path': str(self.models_dir / model_info['file']),
            'validation_checks': {},
            'status': 'UNKNOWN',
            'metadata': model_info.copy(),
            'issues': []
        }
        
        try:
            # Check 1: File exists
            model_path = self.models_dir / model_info['file']
            result['validation_checks']['file_exists'] = model_path.exists()
            if not model_path.exists():
                result['issues'].append(f"Model file not found: {model_path}")
                result['status'] = 'FAILED'
                return result
            
            # Check 2: File size reasonable
            file_size_mb = model_path.stat().st_size / (1024 * 1024)
            result['validation_checks']['file_size_mb'] = file_size_mb
            result['validation_checks']['file_size_reasonable'] = 0.1 < file_size_mb < 1000
            if not result['validation_checks']['file_size_reasonable']:
                result['issues'].append(f"Unusual file size: {file_size_mb:.2f} MB")
            
            # Check 3: Model can be loaded
            try:
                model = joblib.load(model_path)
                result['validation_checks']['loadable'] = True
                
                # Check 4: Model has required methods
                required_methods = ['predict', 'predict_proba']
                for method in required_methods:
                    has_method = hasattr(model, method)
                    result['validation_checks'][f'has_{method}'] = has_method
                    if not has_method:
                        result['issues'].append(f"Model missing required method: {method}")
                
                # Check 5: Model type verification
                model_type_str = str(type(model))
                result['validation_checks']['model_type'] = model_type_str
                
                if model_name == 'tension_detection':
                    expected_in_type = 'RandomForest'
                elif model_name == 'thematic_classification':
                    expected_in_type = 'MiniLM'  # This might be in the class name
                else:
                    expected_in_type = None
                
                if expected_in_type:
                    type_match = expected_in_type.lower() in model_type_str.lower()
                    result['validation_checks']['type_match'] = type_match
                    if not type_match:
                        result['issues'].append(f"Model type mismatch. Expected: {expected_in_type}, Got: {model_type_str}")
                
            except Exception as e:
                result['validation_checks']['loadable'] = False
                result['issues'].append(f"Failed to load model: {str(e)}")
                result['status'] = 'FAILED'
                return result
            
            # Check 6: Model metadata
            result['metadata']['file_size_mb'] = file_size_mb
            result['metadata']['last_modified'] = datetime.fromtimestamp(model_path.stat().st_mtime).isoformat()
            
            # Determine overall status
            critical_checks = ['file_exists', 'loadable', 'has_predict', 'has_predict_proba']
            all_critical_passed = all(result['validation_checks'].get(check, False) for check in critical_checks)
            
            if all_critical_passed and len(result['issues']) == 0:
                result['status'] = 'PASSED'
            elif all_critical_passed:
                result['status'] = 'PASSED_WITH_WARNINGS'
            else:
                result['status'] = 'FAILED'
                
        except Exception as e:
            result['status'] = 'FAILED'
            result['issues'].append(f"Validation error: {str(e)}")
        
        return result
    
    def _save_validation_results(self, validation_summary: Dict[str, Any]):
        """
        Save validation results to file.
        
        Args:
            validation_summary: Complete validation results
        """
        # Save detailed results
        results_file = self.models_dir / 'production_model_validation.json'
        with open(results_file, 'w') as f:
            json.dump(validation_summary, f, indent=2)
        
        logger.info(f"💾 Validation results saved to: {results_file}")
        
        # Create production manifest
        manifest = {
            'production_manifest': {
                'version': '1.0.0',
                'validation_date': validation_summary['validation_date'],
                'production_ready': validation_summary['production_ready'],
                'models': {}
            }
        }
        
        for model_name, model_result in validation_summary['models'].items():
            if model_result['status'] in ['PASSED', 'PASSED_WITH_WARNINGS']:
                manifest['production_manifest']['models'][model_name] = {
                    'file': model_result['metadata']['file'],
                    'type': model_result['metadata']['type'],
                    'task': model_result['metadata']['task'],
                    'performance': model_result['metadata']['expected_performance'],
                    'status': model_result['status'],
                    'file_size_mb': model_result['metadata']['file_size_mb']
                }
        
        # Save manifest
        manifest_file = self.models_dir / 'production_manifest.json'
        with open(manifest_file, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        logger.info(f"📋 Production manifest saved to: {manifest_file}")
    
    def generate_model_cards(self):
        """
        Generate model cards for each production model.
        """
        logger.info("📄 Generating model cards...")
        
        # Tension Detection Model Card
        tension_card = {
            'model_name': 'Tension Detection Random Forest',
            'model_version': '1.0.0',
            'model_type': 'Random Forest Classifier',
            'task': 'Binary Text Classification',
            'description': 'Detects tension in text documents with 97.54% accuracy',
            'performance': {
                'accuracy': 0.9754,
                'training_samples': 1827,
                'features': 24,
                'cross_validation': 'Stratified 70/20/10 split'
            },
            'training_data': {
                'source': 'Data Engineering Pipeline',
                'preprocessing': '24 engineered tension detection features',
                'date': '2025-06-14'
            },
            'model_architecture': {
                'algorithm': 'Random Forest',
                'hyperparameters': 'Optimized with Optuna (50 trials)',
                'features': 'Engineered tension detection features'
            },
            'usage': {
                'input': 'Raw text documents',
                'output': 'Binary classification (tension/no_tension) with confidence scores',
                'api': 'production_ml_api.predict_tension()'
            },
            'limitations': [
                'Trained on specific document types',
                'Performance may vary on different text domains',
                'Requires feature extraction pipeline'
            ],
            'ethical_considerations': [
                'Model should not be used for surveillance without consent',
                'Results should be interpreted by domain experts',
                'Regular retraining recommended for changing contexts'
            ]
        }
        
        # Thematic Classification Model Card
        thematic_card = {
            'model_name': 'Thematic Classification MiniLM SVM',
            'model_version': '1.0.0',
            'model_type': 'SVM with Semantic Embeddings + Threshold Tuning',
            'task': 'Binary Text Classification (Performance vs Légitimité)',
            'description': 'Classifies documents into Performance or Légitimité themes with 77.27% F1 Macro',
            'performance': {
                'f1_macro': 0.7727,
                'accuracy': 0.9508,
                'minority_class_f1': 0.9739,
                'optimal_threshold': 0.64,
                'training_samples': 302
            },
            'training_data': {
                'source': 'Data Engineering Pipeline',
                'preprocessing': 'MiniLM sentence embeddings (384 dimensions)',
                'class_distribution': '89.4% Performance, 10.6% Légitimité',
                'date': '2025-06-14'
            },
            'model_architecture': {
                'embedding_model': 'sentence-transformers/all-MiniLM-L6-v2',
                'classifier': 'SVM with RBF kernel',
                'threshold_tuning': 'Optimal threshold = 0.64 for imbalanced classes',
                'innovation': 'Semantic-aware threshold optimization'
            },
            'usage': {
                'input': 'Raw text documents',
                'output': 'Binary classification (Performance/Légitimité) with confidence scores',
                'api': 'production_ml_api.predict_thematic()'
            },
            'limitations': [
                'Trained on imbalanced dataset',
                'Performance may vary on different document types',
                'Requires sentence-transformers library'
            ],
            'ethical_considerations': [
                'Model addresses class imbalance through threshold tuning',
                'Results should be validated by domain experts',
                'Regular monitoring for bias and performance drift recommended'
            ]
        }
        
        # Save model cards
        cards_dir = self.models_dir / 'model_cards'
        cards_dir.mkdir(exist_ok=True)
        
        with open(cards_dir / 'tension_detection_model_card.json', 'w') as f:
            json.dump(tension_card, f, indent=2)
        
        with open(cards_dir / 'thematic_classification_model_card.json', 'w') as f:
            json.dump(thematic_card, f, indent=2)
        
        logger.info(f"📄 Model cards saved to: {cards_dir}")


def main():
    """
    Run production model validation.
    """
    print("🔍 Production Model Validation")
    print("=" * 50)
    
    validator = ProductionModelValidator()
    
    # Validate all models
    results = validator.validate_all_models()
    
    # Generate model cards
    validator.generate_model_cards()
    
    # Print summary
    print("\n📊 VALIDATION SUMMARY")
    print("=" * 30)
    print(f"Models Validated: {results['models_validated']}")
    print(f"Models Passed: {results['models_passed']}")
    print(f"Models Failed: {results['models_failed']}")
    print(f"Production Ready: {'✅ YES' if results['production_ready'] else '❌ NO'}")
    
    if results['production_ready']:
        print("\n🎉 All models are production ready!")
    else:
        print("\n⚠️ Some models have issues. Check validation results.")
    
    return results


if __name__ == "__main__":
    main()
