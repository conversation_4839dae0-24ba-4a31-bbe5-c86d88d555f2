"""
Enhanced Thematic Classification Training with Threshold Tuning
==============================================================

This script extends the existing thematic classification training with
threshold tuning to improve minority class detection (Légitimité).

Key Features:
- Threshold tuning for all existing models
- Comparison of default vs tuned performance
- Detailed minority class analysis
- Full dataset support

Author: ML Engineering Team
Date: 2025-06-14
"""

import sys
import os
from pathlib import Path
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime
import argparse

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Import our modules
from data_preparation import DataPreparationPipeline
from models.thematic_classification.logistic_regression import ThematicLogisticRegressionModel
from models.thematic_classification.naive_bayes import ThematicNaiveBayesModel
from models.thematic_classification.miniLM_svm import ThematicMiniLMSVMModel
from models.thematic_classification.xgboost_smote import ThematicXGBoostSMOTEModel
from threshold_tuning import ThresholdTuner, apply_threshold_tuning_to_model

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('thematic_training_with_tuning.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class EnhancedThematicModelTrainer:
    """
    Enhanced trainer with threshold tuning for thematic classification.
    """
    
    def __init__(self, data_dir: str = "data_from_Data_Engineering", 
                 output_dir: str = "trained_models"):
        """
        Initialize the enhanced trainer.
        
        Args:
            data_dir: Directory containing the data engineering outputs
            output_dir: Directory to save trained models
        """
        self.data_dir = data_dir
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize data pipeline
        self.data_pipeline = DataPreparationPipeline(data_dir)
        self.results = {}
        
        logger.info(f"Initialized EnhancedThematicModelTrainer with data_dir: {data_dir}")
    
    def prepare_data(self, table_name: str | None = None) -> dict:
        """
        Prepare data for training (same as original).
        """
        logger.info("Preparing data for thematic classification models...")
        
        try:
            if table_name:
                # Quick single-table path for smoke testing
                logger.info(f"⚡ Loading SINGLE TABLE data: {table_name} …")
                target_df, ml_df = self.data_pipeline.load_single_table_data(table_name)
                thematic_dataset = self.data_pipeline.prepare_thematic_dataset(target_df, ml_df)
                
                if thematic_dataset.empty:
                    raise ValueError("Single-table dataset produced zero samples. Check data alignment.")
                
                thematic_splits = self.data_pipeline.create_train_test_splits(
                    thematic_dataset, 'theme_label')
                
                data_results = {
                    'datasets': {'thematic': thematic_dataset},
                    'splits': {'thematic': thematic_splits},
                    'class_weights': {'thematic': self.data_pipeline.get_class_weights(thematic_splits['y_train'])},
                    'encoders': {'thematic': self.data_pipeline.theme_encoder}
                }
            else:
                # Full dataset preparation
                data_results = self.data_pipeline.run_complete_preparation()
            
            # Extract thematic-specific data
            thematic_dataset = data_results['datasets']['thematic']
            thematic_splits = data_results['splits']['thematic']
            
            logger.info(f"Data preparation completed:")
            logger.info(f"  Total samples: {len(thematic_dataset)}")
            logger.info(f"  Training samples: {len(thematic_splits['train_indices'])}")
            logger.info(f"  Validation samples: {len(thematic_splits['val_indices'])}")
            logger.info(f"  Test samples: {len(thematic_splits['test_indices'])}")
            logger.info(f"  Class distribution: {thematic_splits['class_distribution']}")
            
            return data_results
            
        except Exception as e:
            logger.error(f"Data preparation failed: {str(e)}")
            raise
    
    def train_models_with_tuning(self, data_results: dict) -> dict:
        """
        Train all models and apply threshold tuning.
        
        Args:
            data_results: Results from data preparation
            
        Returns:
            Dictionary with training and tuning results for all models
        """
        logger.info("Starting thematic classification training with threshold tuning...")
        
        # Extract data
        thematic_dataset = data_results['datasets']['thematic']
        splits = data_results['splits']['thematic']
        class_weights = data_results['class_weights']['thematic']
        thematic_encoder = data_results['encoders']['thematic']
        
        # Get text data and labels
        train_indices = splits['train_indices']
        val_indices = splits['val_indices']
        test_indices = splits['test_indices']
        
        train_texts = thematic_dataset.iloc[train_indices]['text'].tolist()
        val_texts = thematic_dataset.iloc[val_indices]['text'].tolist()
        test_texts = thematic_dataset.iloc[test_indices]['text'].tolist()
        
        train_labels = splits['y_train'].tolist()
        val_labels = splits['y_val'].tolist()
        test_labels = splits['y_test'].tolist()
        
        class_names = list(thematic_encoder.classes_)
        
        models_results = {}
        
        # Model configurations
        models_to_train = {
            'Logistic Regression': ThematicLogisticRegressionModel(),
            'Naive Bayes': ThematicNaiveBayesModel(),
            'MiniLM SVM': ThematicMiniLMSVMModel(),
            'XGBoost SMOTE': ThematicXGBoostSMOTEModel(),
        }
        
        # Train each model with threshold tuning
        for model_name, model in models_to_train.items():
            logger.info(f"\n{'='*60}")
            logger.info(f"Training {model_name} Model with Threshold Tuning")
            logger.info(f"{'='*60}")
            
            try:
                # Train model (same as before)
                training_results = model.train(
                    train_texts, train_labels, val_texts, val_labels,
                    class_weights=class_weights,
                    optimize=True,
                    n_trials=50
                )
                
                # Apply threshold tuning
                logger.info(f"Applying threshold tuning to {model_name}...")
                tuning_results = apply_threshold_tuning_to_model(
                    model, val_texts, val_labels, test_texts, test_labels,
                    class_names=class_names, optimization_metric='f1_macro'
                )
                
                # Save model
                model_path = self.output_dir / f"{model_name.lower().replace(' ', '_').replace('-', '_')}_tuned.joblib"
                model.save_model(str(model_path))
                
                # Store comprehensive results
                models_results[model_name.lower().replace(' ', '_').replace('-', '_')] = {
                    'training': training_results,
                    'threshold_tuning': tuning_results,
                    'model': model,
                    'model_path': str(model_path)
                }
                
                # Log performance comparison
                default_acc = tuning_results['default_threshold_results']['accuracy']
                tuned_acc = tuning_results['tuned_threshold_results']['accuracy']
                default_f1_minority = tuning_results['default_threshold_results']['f1_minority']
                tuned_f1_minority = tuning_results['tuned_threshold_results']['f1_minority']
                best_threshold = tuning_results['best_threshold']
                
                logger.info(f"{model_name} Results:")
                logger.info(f"  Best Threshold: {best_threshold:.3f}")
                logger.info(f"  Default Accuracy: {default_acc:.4f} → Tuned: {tuned_acc:.4f}")
                logger.info(f"  Minority F1: {default_f1_minority:.4f} → {tuned_f1_minority:.4f}")
                logger.info(f"  Minority F1 Improvement: +{tuned_f1_minority - default_f1_minority:.4f}")
                logger.info(f"  Model saved to: {model_path}")
                
            except Exception as e:
                logger.error(f"{model_name} training/tuning failed: {str(e)}")
                models_results[model_name.lower().replace(' ', '_').replace('-', '_')] = {
                    'error': str(e),
                    'status': 'failed'
                }
        
        return models_results
    
    def compare_models_with_tuning(self, models_results: dict) -> dict:
        """
        Compare all models with both default and tuned thresholds.
        
        Args:
            models_results: Results from model training and tuning
            
        Returns:
            Dictionary with comprehensive model comparison
        """
        logger.info("\n" + "="*80)
        logger.info("THEMATIC MODEL COMPARISON WITH THRESHOLD TUNING")
        logger.info("="*80)
        
        # Extract performance metrics
        comparison_data = []
        
        for model_name, results in models_results.items():
            if 'threshold_tuning' in results:
                tuning_results = results['threshold_tuning']
                
                # Default threshold results
                default_results = tuning_results['default_threshold_results']
                tuned_results = tuning_results['tuned_threshold_results']
                
                comparison_data.append({
                    'Model': model_name.replace('_', ' ').title(),
                    'Default_Accuracy': default_results['accuracy'],
                    'Tuned_Accuracy': tuned_results['accuracy'],
                    'Default_F1_Macro': default_results['f1_macro'],
                    'Tuned_F1_Macro': tuned_results['f1_macro'],
                    'Default_F1_Minority': default_results['f1_minority'],
                    'Tuned_F1_Minority': tuned_results['f1_minority'],
                    'Best_Threshold': tuning_results['best_threshold'],
                    'F1_Minority_Improvement': tuned_results['f1_minority'] - default_results['f1_minority'],
                    'Status': 'Success'
                })
            else:
                comparison_data.append({
                    'Model': model_name.replace('_', ' ').title(),
                    'Default_Accuracy': 0,
                    'Tuned_Accuracy': 0,
                    'Default_F1_Macro': 0,
                    'Tuned_F1_Macro': 0,
                    'Default_F1_Minority': 0,
                    'Tuned_F1_Minority': 0,
                    'Best_Threshold': 0.5,
                    'F1_Minority_Improvement': 0,
                    'Status': 'Failed'
                })
        
        # Create comparison DataFrame
        comparison_df = pd.DataFrame(comparison_data)
        
        # Find best model (based on tuned F1 macro)
        successful_models = comparison_df[comparison_df['Status'] == 'Success']
        
        if len(successful_models) > 0:
            best_model_idx = successful_models['Tuned_F1_Macro'].idxmax()
            best_model_name = successful_models.loc[best_model_idx, 'Model']
            best_model_f1 = successful_models.loc[best_model_idx, 'Tuned_F1_Macro']
            
            logger.info("Model Performance Comparison (Default vs Tuned):")
            logger.info(f"\n{comparison_df.to_string(index=False)}")
            
            logger.info(f"\n🏆 BEST MODEL WITH TUNING: {best_model_name}")
            logger.info(f"🎯 Tuned F1 Macro: {best_model_f1:.4f}")
            
            # Analyze improvements
            total_improvements = successful_models['F1_Minority_Improvement'].sum()
            logger.info(f"📈 Total Minority F1 Improvement: +{total_improvements:.4f}")
            
        else:
            logger.error("❌ No models trained successfully!")
            best_model_name = None
            best_model_f1 = 0
        
        return {
            'comparison_df': comparison_df,
            'best_model': best_model_name,
            'best_f1_macro': best_model_f1,
            'successful_models': len(successful_models)
        }
    
    def run_complete_training_with_tuning(self, table_name: str | None = None):
        """
        Run the complete training pipeline with threshold tuning.
        """
        logger.info("🚀 Starting Enhanced Thematic Classification Training with Threshold Tuning")
        logger.info("="*90)
        
        try:
            # Step 1: Prepare data
            data_results = self.prepare_data(table_name)
            
            # Step 2: Train all models with threshold tuning
            models_results = self.train_models_with_tuning(data_results)
            
            # Step 3: Compare models
            comparison_results = self.compare_models_with_tuning(models_results)
            
            logger.info("\n" + "="*90)
            logger.info("🎉 ENHANCED THEMATIC CLASSIFICATION TRAINING COMPLETED!")
            logger.info("="*90)
            
            return {
                'data': data_results,
                'models': models_results,
                'comparison': comparison_results
            }
            
        except Exception as e:
            logger.error(f"Enhanced training pipeline failed: {str(e)}")
            raise


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train thematic models with threshold tuning")
    parser.add_argument("--table", help="Single table name (e.g., Table_A) for quick testing", default=None)
    args = parser.parse_args()

    trainer = EnhancedThematicModelTrainer()
    results = trainer.run_complete_training_with_tuning(table_name=args.table)
