# Production Deployment Guide

**Version**: 1.0.0  
**Date**: 2025-06-14  
**Status**: Production Ready  

---

## 🎯 **Overview**

This guide provides complete instructions for deploying the production-ready ML system for tension detection and thematic classification. The system achieves 97.54% accuracy for tension detection and 77.27% F1 Macro for thematic classification.

---

## 📋 **Prerequisites**

### **System Requirements**
- **Python**: 3.10 or higher
- **Memory**: 8GB RAM minimum, 16GB recommended
- **Storage**: 5GB free space for models and dependencies
- **CPU**: Multi-core processor (4+ cores recommended)
- **GPU**: Optional (CPU fallback available)

### **Environment Setup**
```bash
# Clone or access the ML_Engineering directory
cd ML_Engineering

# Create virtual environment
python -m venv .venv

# Activate virtual environment
# On Linux/Mac:
source .venv/bin/activate
# On Windows:
.venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
# OR using uv (faster):
uv sync
```

---

## 🚀 **Quick Start**

### **1. Validate Models**
```bash
cd ML_Models_Plan
python validate_production_models.py
```

Expected output:
```
🔍 Production Model Validation
==============================
Models Validated: 2
Models Passed: 2
Models Failed: 0
Production Ready: ✅ YES

🎉 All models are production ready!
```

### **2. Test Production API**
```bash
python production_ml_api.py
```

This will run example predictions and display results for both tasks.

### **3. Use in Your Application**
```python
from production_ml_api import ProductionMLAPI

# Initialize API
api = ProductionMLAPI()
api.load_models()

# Single prediction
result = api.predict_tension("This document contains urgent concerns!")
print(result)

# Batch prediction
texts = ["Document 1", "Document 2", "Document 3"]
results = api.predict_both(texts)
print(results)
```

---

## 📁 **File Structure**

```
ML_Models_Plan/
├── production_ml_api.py              # Main production API
├── validate_production_models.py     # Model validation
├── DEPLOYMENT_GUIDE.md              # This guide
├── PRODUCTION_READY_SUMMARY.md      # Final summary
├── trained_models/                  # Production models
│   ├── tension_random_forest_full.joblib
│   ├── minilm_svm_tuned.joblib
│   ├── production_manifest.json
│   └── model_cards/
├── models/                          # Model implementations
│   ├── tension_detection/
│   └── thematic_classification/
├── data_preparation.py              # Data pipeline
└── threshold_tuning.py              # Threshold optimization
```

---

## 🔧 **API Reference**

### **ProductionMLAPI Class**

#### **Initialization**
```python
api = ProductionMLAPI(
    models_dir="trained_models",      # Directory with model files
    data_dir="data_from_Data_Engineering"  # Data pipeline directory
)
```

#### **Load Models**
```python
load_results = api.load_models()
# Returns: {'tension_detection': True, 'thematic_classification': True}
```

#### **Tension Detection**
```python
result = api.predict_tension(texts)
```

**Input**: String or list of strings  
**Output**:
```json
{
  "task": "tension_detection",
  "model": "Random Forest",
  "performance": {"accuracy": 0.9754},
  "predictions": [
    {
      "text": "Sample text",
      "prediction": "tension",
      "confidence": 0.95,
      "probability_tension": 0.95,
      "probability_no_tension": 0.05
    }
  ],
  "timestamp": "2025-06-14T..."
}
```

#### **Thematic Classification**
```python
result = api.predict_thematic(texts)
```

**Input**: String or list of strings  
**Output**:
```json
{
  "task": "thematic_classification",
  "model": "MiniLM SVM with Threshold Tuning",
  "performance": {"f1_macro": 0.7727, "optimal_threshold": 0.64},
  "predictions": [
    {
      "text": "Sample text",
      "prediction": "Performance",
      "tuned_prediction": "Légitimité",
      "confidence": 0.85,
      "probability_legitimacy": 0.75,
      "probability_performance": 0.25,
      "threshold_applied": 0.64
    }
  ],
  "timestamp": "2025-06-14T..."
}
```

#### **Combined Prediction**
```python
result = api.predict_both(texts)
```

**Output**: Combined results from both models.

---

## 📊 **Performance Specifications**

### **Tension Detection Model**
- **Model**: Random Forest with 24 engineered features
- **Performance**: 97.54% accuracy
- **Training Data**: 1,827 samples
- **Inference Speed**: <1 second per document
- **Memory Usage**: ~100MB

### **Thematic Classification Model**
- **Model**: MiniLM SVM with threshold tuning (threshold=0.64)
- **Performance**: 77.27% F1 Macro, 95.08% accuracy
- **Training Data**: 302 samples (imbalanced: 89.4% vs 10.6%)
- **Inference Speed**: ~2 seconds per document (includes embedding)
- **Memory Usage**: ~500MB (includes sentence-transformers)

---

## 🔒 **Error Handling**

### **Common Issues and Solutions**

#### **1. Models Not Found**
```
Error: Model file not found
Solution: Ensure trained_models/ directory contains required .joblib files
```

#### **2. Memory Issues**
```
Error: Out of memory during prediction
Solution: Process texts in smaller batches, increase system RAM
```

#### **3. CUDA Issues**
```
Error: CUDA operation not supported
Solution: Models automatically fall back to CPU, no action needed
```

#### **4. Import Errors**
```
Error: Module not found
Solution: Ensure all dependencies installed: pip install -r requirements.txt
```

### **Error Handling in Code**
```python
try:
    api = ProductionMLAPI()
    load_results = api.load_models()
    
    if not all(load_results.values()):
        print("Warning: Some models failed to load")
        print(load_results)
    
    result = api.predict_tension("Sample text")
    
except Exception as e:
    print(f"Error: {str(e)}")
    # Handle error appropriately
```

---

## 🔧 **Integration Examples**

### **Flask Web API**
```python
from flask import Flask, request, jsonify
from production_ml_api import ProductionMLAPI

app = Flask(__name__)
ml_api = ProductionMLAPI()
ml_api.load_models()

@app.route('/predict/tension', methods=['POST'])
def predict_tension():
    data = request.json
    text = data.get('text', '')
    result = ml_api.predict_tension(text)
    return jsonify(result)

@app.route('/predict/thematic', methods=['POST'])
def predict_thematic():
    data = request.json
    text = data.get('text', '')
    result = ml_api.predict_thematic(text)
    return jsonify(result)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

### **Batch Processing**
```python
import pandas as pd
from production_ml_api import ProductionMLAPI

# Load data
df = pd.read_csv('documents.csv')
texts = df['text'].tolist()

# Initialize API
api = ProductionMLAPI()
api.load_models()

# Process in batches
batch_size = 100
results = []

for i in range(0, len(texts), batch_size):
    batch = texts[i:i+batch_size]
    batch_results = api.predict_both(batch)
    results.extend(batch_results['predictions'])

# Save results
results_df = pd.DataFrame(results)
results_df.to_csv('predictions.csv', index=False)
```

---

## 📈 **Monitoring and Maintenance**

### **Performance Monitoring**
```python
# Log predictions for monitoring
import logging

logging.basicConfig(
    filename='ml_predictions.log',
    level=logging.INFO,
    format='%(asctime)s - %(message)s'
)

# In your application:
result = api.predict_tension(text)
logging.info(f"Tension prediction: {result['predictions'][0]['prediction']}, "
            f"confidence: {result['predictions'][0]['confidence']}")
```

### **Model Retraining Indicators**
- **Performance Drift**: Monitor accuracy over time
- **Data Drift**: New document types or domains
- **Concept Drift**: Changes in what constitutes "tension" or themes
- **Volume Changes**: Significant increase/decrease in predictions

### **Recommended Monitoring Metrics**
- **Prediction Confidence**: Track average confidence scores
- **Class Distribution**: Monitor prediction distribution changes
- **Response Time**: Track inference speed
- **Error Rate**: Monitor failed predictions

---

## 🚀 **Production Deployment Checklist**

### **Pre-Deployment**
- [ ] All dependencies installed
- [ ] Models validated successfully
- [ ] API tests passing
- [ ] Error handling implemented
- [ ] Logging configured
- [ ] Performance benchmarks established

### **Deployment**
- [ ] Production environment configured
- [ ] Models deployed to production directory
- [ ] API endpoints tested
- [ ] Load testing completed
- [ ] Monitoring systems active
- [ ] Backup procedures in place

### **Post-Deployment**
- [ ] Monitor initial predictions
- [ ] Validate performance metrics
- [ ] Set up regular model validation
- [ ] Document any issues
- [ ] Plan retraining schedule

---

## 📞 **Support and Troubleshooting**

### **Validation Commands**
```bash
# Validate models
python validate_production_models.py

# Test API
python production_ml_api.py

# Check model info
python -c "from production_ml_api import ProductionMLAPI; api = ProductionMLAPI(); api.load_models(); print(api.get_model_info())"
```

### **Performance Benchmarks**
- **Tension Detection**: 97.54% accuracy (target: >90%)
- **Thematic Classification**: 77.27% F1 Macro (target: >60%)
- **Inference Speed**: <3 seconds per document
- **Memory Usage**: <1GB total

---

## 🎉 **Success Criteria**

Your deployment is successful when:
- ✅ All models load without errors
- ✅ Predictions return expected format
- ✅ Performance meets benchmarks
- ✅ Error handling works correctly
- ✅ Monitoring systems active

**Status**: 🚀 **READY FOR PRODUCTION DEPLOYMENT**
