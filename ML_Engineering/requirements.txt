# Required packages for ML Engineering Pipeline - Complete ML Stack
# Updated: 2025-06-14 - Added XGBoost + SMOTE dependencies

# Document processing
python-docx==1.1.0
docx2python==2.7.3
PyMuPDF==1.23.26
pdfplumber==0.10.3
openpyxl

# Text processing
chardet==5.2.0
ftfy==6.2.0
regex>=2022.1.18

# NLP - Core components
spacy==3.7.2
stanza==1.8.1
fr_core_news_lg @ https://github.com/explosion/spacy-models/releases/download/fr_core_news_lg-3.8.0/fr_core_news_lg-3.8.0-py3-none-any.whl
fr_core_news_sm @ https://github.com/explosion/spacy-models/releases/download/fr_core_news_sm-3.7.0/fr_core_news_sm-3.7.0-py3-none-any.whl
sentence-transformers>=2.6.0
bertopic==0.16.0

# Memory monitoring
psutil>=5.9.4

# Data handling
pandas>=1.5.0
numpy==1.24.3  # Specific version to avoid compatibility issues with spaCy
jsonschema>=4.0.0

# Machine Learning - Core (Compatible versions)
scikit-learn==1.3.2  # Fixed version for compatibility
faiss-cpu==1.7.4

# Machine Learning - Advanced Models
xgboost>=2.0.0  # Latest version for better performance
optuna>=3.0.0   # Hyperparameter optimization
imbalanced-learn==0.11.0  # SMOTE - compatible with sklearn 1.3.2

# Deep Learning
torch>=2.0.0
transformers>=4.41.0
huggingface_hub>=0.24.0
accelerate>=0.26.0

# Visualization and Analysis
matplotlib>=3.5.0
seaborn>=0.11.0

# Testing
pytest==8.0.0

# Additional ML utilities
joblib>=1.3.0  # Model persistence